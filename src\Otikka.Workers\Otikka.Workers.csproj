<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Hangfire" Version="1.8.21" />
		<PackageReference Include="Hangfire.PostgreSql" Version="1.20.12" />
		<PackageReference Include="Serilog" Version="4.3.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />

		<!-- Unificar versão do Roslyn para evitar warnings NU1608 e conflitos de runtime -->
		<PackageReference Include="Microsoft.CodeAnalysis" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Scripting" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Workspaces.Common" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.14.0" />
	</ItemGroup>

	<ItemGroup>
		<_ContentIncludedByDefault Remove="Views\Home\Index.cshtml" />
		<_ContentIncludedByDefault Remove="Views\Home\Privacy.cshtml" />
		<_ContentIncludedByDefault Remove="Views\Shared\Error.cshtml" />
		<_ContentIncludedByDefault Remove="Views\Shared\_Layout.cshtml" />
		<_ContentIncludedByDefault Remove="Views\Shared\_ValidationScriptsPartial.cshtml" />
		<_ContentIncludedByDefault Remove="Views\_ViewImports.cshtml" />
		<_ContentIncludedByDefault Remove="Views\_ViewStart.cshtml" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Otikka.Application\Otikka.Application.csproj" />
		<ProjectReference Include="..\Otikka.Infrastructure\Otikka.Infrastructure.csproj" />
		<ProjectReference Include="..\Otikka.Persistence\Otikka.Persistence.csproj" />
	</ItemGroup>

</Project>
