@attribute [Route(Rota)]
@inherits PageBase

@using Otikka.App.Components.Pages.Dashboard.ProdutoModulo.Produto.Componentes
@using Otikka.Application.Features.Produto.Commands.CreateProduto
@using Otikka.Application.Features.Empresa.Queries.GetCompany
@using Otikka.Domain.Entities.ProdutoModulo

@inject ILogger<Cadastrar> Logger

<PageTitle>
    Produto - Cadastro
</PageTitle>

<ProdutoForm EhEdicao="@false" Produto="Produto" OnSave="Save" @ref="produtoFormRef" />

@code {
    private const string Rota = Application.Routes.ProdutoCadastrar;

    [SupplyParameterFromForm]
    private CreateProduto Produto { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        Produto.EmpresaId = await GetEmpresaIdAsync();
    }

    private ProdutoForm? produtoFormRef;

    private async Task Save()
    {
        Produto.DataCriacao = DateTimeOffset.Now;
        Produto.CriadoPorId = await GetUsuarioIdLoggedAsync();

        Logger.LogInformation("Iniciando cadastro de produto: {InstanciaNome}", Produto.Nome);

        // Incluir o CustoUnitario do formulário no comando
        if (produtoFormRef != null)
        {
            Produto.CustoUnitario = produtoFormRef.CustoUnitario;
        }

        var result = await MessageBus.InvokeAsync<Result>(Produto);

        if (result.IsSuccess)
        {
            await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
            NavigationManager.NavigateTo(Application.Routes.ProdutoListar);
        }
        else
        {
            await AlertService.ShowError("Opps!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao cadastrar produto");
        }
    }
}