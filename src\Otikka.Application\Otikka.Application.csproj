﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Exceptions\**" />
		<Compile Remove="Responses\**" />
		<EmbeddedResource Remove="Exceptions\**" />
		<EmbeddedResource Remove="Responses\**" />
		<None Remove="Exceptions\**" />
		<None Remove="Responses\**" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Otikka.Domain\Otikka.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Features\Auth\RecoverPassword\" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="FluentResults" Version="4.0.0" />
		<PackageReference Include="FluentValidation" Version="12.0.0" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.8" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.11" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.8" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
		<PackageReference Include="WolverineFx" Version="3.13.4" />
	</ItemGroup>

</Project>
