using Otikka.Domain.Entities;

namespace Otikka.Persistence.Configurations;

public class EntidadeBaseEntityConfiguration<T> : IEntityTypeConfiguration<T> where T : EntidadeBase
{
    public virtual void Configure(EntityTypeBuilder<T> builder)
    {
        // Chave primária
        builder.HasKey(e => e.Id);

        // Campos obrigatórios e opcionais
        builder.Property(e => e.DataCriacao)
               .IsRequired();

        builder.Property(e => e.DataAtualizacao)
               .IsRequired(false);

        builder.Property(e => e.DataExclusao)
               .IsRequired(false);

        builder.Property(e => e.Version)
               .IsRowVersion(); // Marca como controle de concorrência otimista

        // Só configura os relacionamentos de auditoria se não for Usuario ou UsuarioEmpresa
        // para evitar referências circulares
        if (typeof(T).Name != "Usuario" && typeof(T).Name != "UsuarioEmpresa")
        {
            ConfigureAuditRelationships(builder);
        }
        
        builder.HasQueryFilter(e => e.DataExclusao == null);
    }

    protected virtual void ConfigureAuditRelationships(EntityTypeBuilder<T> builder)
    {
        // Auditoria - Relacionamentos com Usuario
        builder.HasOne(e => e.CriadoPor)
               .WithMany()
               .HasForeignKey(e => e.CriadoPorId)
               .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.AtualizadorPor)
               .WithMany()
               .HasForeignKey(e => e.AtualizadoPorId)
               .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.ExcluidoPor)
               .WithMany()
               .HasForeignKey(e => e.ExcluidoPorId)
               .OnDelete(DeleteBehavior.Restrict);
    }
}
