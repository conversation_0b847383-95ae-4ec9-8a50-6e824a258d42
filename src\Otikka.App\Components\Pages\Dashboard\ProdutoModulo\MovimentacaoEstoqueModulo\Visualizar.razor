@using Otikka.Application.Features.MovimentacaoEstoque.Queries.GetMovimentacaoEstoque
@using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo
@using Otikka.Domain.Enums
@using FluentResults
@using Wolverine

@attribute [Route(Application.Routes.MovimentacaoEstoqueVisualizar)]
@inherits PageBase

<PageTitle>Movimentação de Estoque - Visualizar</PageTitle>

<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Visualizar Movimentação de Estoque</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="javascript: void(0);">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="@Application.Routes.MovimentacaoEstoqueListar">Movimentações de Estoque</a></li>
                    <li class="breadcrumb-item active">Visualizar</li>
                </ol>
            </div>
        </div>
    </div>
</div>

@if (MovimentacaoEstoque == null)
{
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-2">Carregando movimentação...</p>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Detalhes da Movimentação</h5>
                        <div class="flex-shrink-0">
                            <a href="@Application.Routes.MovimentacaoEstoqueListar" class="btn btn-secondary">
                                <i class="ri-arrow-left-line"></i> Voltar
                            </a>
                            @if(MovimentacaoEstoque.CompraId is null){
                            <a href="@Application.Routes.GerarRota(Application.Routes.MovimentacaoEstoqueEditar, MovimentacaoEstoque.Id.ToString())" 
                               class="btn btn-primary ms-2">
                                <i class="ri-edit-line"></i> Editar
                            </a>
                            }
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Data da Movimentação</label>
                                <p class="form-control-plaintext">@MovimentacaoEstoque.DataMovimentacao.ToString("dd/MM/yyyy HH:mm")</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Tipo de Movimentação</label>
                                <p class="form-control-plaintext">
                                    <span class="badge @GetTipoMovimentacaoClass(MovimentacaoEstoque.TipoMovimentacao) fs-6">
                                        @GetTipoMovimentacaoText(MovimentacaoEstoque.TipoMovimentacao)
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Produto</label>
                                <p class="form-control-plaintext">@(MovimentacaoEstoque.Produto?.Nome ?? "Produto não encontrado")</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Quantidade</label>
                                <p class="form-control-plaintext">
                                    <span class="@(MovimentacaoEstoque.Quantidade > 0 ? "text-success" : "text-danger") fw-bold">
                                        @(MovimentacaoEstoque.Quantidade > 0 ? "+" : "")@MovimentacaoEstoque.Quantidade
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Preço Unitário</label>
                                <p class="form-control-plaintext">
                                    @if (MovimentacaoEstoque.CustoUnitario.HasValue)
                                {
                                    @MovimentacaoEstoque.CustoUnitario.Value.ToString("C")
                                }
                                    else
                                    {
                                        <span class="text-muted">Não informado</span>
                                    }
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">ID da Compra</label>
                                <p class="form-control-plaintext">
                                    @if (MovimentacaoEstoque.CompraId.HasValue)
                                    {
                                        <a href="@Application.Routes.GerarRota(Application.Routes.CompraEditar, MovimentacaoEstoque.CompraId.Value.ToString())" 
                                           class="text-primary text-decoration-underline">
                                            @MovimentacaoEstoque.CompraId.Value.ToString("N")[..8]...
                                        </a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Não vinculado a compra</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Descrição</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(MovimentacaoEstoque.Descricao))
                                    {
                                        @MovimentacaoEstoque.Descricao
                                    }
                                    else
                                    {
                                        <span class="text-muted">Nenhuma descrição informada</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    @if (MovimentacaoEstoque.DataCriacao != default)
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Data de Criação</label>
                                    <p class="form-control-plaintext text-muted">@MovimentacaoEstoque.DataCriacao.ToString("dd/MM/yyyy HH:mm")</p>
                                </div>
                            </div>
                            @if (MovimentacaoEstoque.DataAtualizacao.HasValue)
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Última Atualização</label>
                                        <p class="form-control-plaintext text-muted">@MovimentacaoEstoque.DataAtualizacao.Value.ToString("dd/MM/yyyy HH:mm")</p>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public Guid Id { get; set; }
    
    private MovimentacaoEstoque? MovimentacaoEstoque;

    protected override async Task OnInitializedAsync()
    {
        await LoadMovimentacaoEstoque();
    }

    private async Task LoadMovimentacaoEstoque()
    {
        try
        {
            var result = await MessageBus.InvokeAsync<Result<MovimentacaoEstoque>>(new GetMovimentacaoEstoque(Id));
            
            if (result.IsSuccess && result.Value != null)
            {
                MovimentacaoEstoque = result.Value;
            }
            else
            {
                await AlertService.ShowError("Erro!", result.Errors.FirstOrDefault()?.Message ?? "Movimentação não encontrada");
                NavigationManager.NavigateTo(Application.Routes.MovimentacaoEstoqueListar);
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro!", $"Erro inesperado: {ex.Message}");
            NavigationManager.NavigateTo(Application.Routes.MovimentacaoEstoqueListar);
        }
    }

    private string GetTipoMovimentacaoClass(TipoMovimentacaoEstoque tipo)
    {
        return tipo switch
        {
            TipoMovimentacaoEstoque.Entrada => "bg-success",
            TipoMovimentacaoEstoque.Saida => "bg-danger",
            TipoMovimentacaoEstoque.Ajuste => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetTipoMovimentacaoText(TipoMovimentacaoEstoque tipo)
    {
        return tipo switch
        {
            TipoMovimentacaoEstoque.Entrada => "Entrada",
            TipoMovimentacaoEstoque.Saida => "Saída",
            TipoMovimentacaoEstoque.Ajuste => "Ajuste",
            _ => "Desconhecido"
        };
    }
}
