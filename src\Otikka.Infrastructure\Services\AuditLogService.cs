using Hangfire;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Infrastructure;
using Otikka.Application.Contracts.Persistence;
using Otikka.Domain.Entities.Common;
using FluentResults;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;

namespace Otikka.Infrastructure.Services;

/// <summary>
/// Serviço de auditoria que processa logs em background usando Hangfire
/// </summary>
public class AuditLogService : IAuditLogService
{
    private readonly ILogger<AuditLogService> _logger;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IRecurringJobManager _recurringJobManager;
    private readonly IAuditLogRepositorio _auditLogRepositorio;

    public AuditLogService(
        ILogger<AuditLogService> logger,
        IBackgroundJobClient backgroundJobClient,
        IRecurringJobManager recurringJobManager,
        IAuditLogRepositorio auditLogRepositorio)
    {
        _logger = logger;
        _backgroundJobClient = backgroundJobClient;
        _recurringJobManager = recurringJobManager;
        _auditLogRepositorio = auditLogRepositorio;
    }

    /// <summary>
    /// Enfileira um log de auditoria para processamento em background
    /// </summary>
    public async Task<Result> EnfileirarLogAsync(AuditLog auditLog)
    {
        try
        {
            // Salva o log temporariamente no banco para processamento posterior
            var resultado = await _auditLogRepositorio.AdicionarAsync(auditLog);
            if (resultado.IsFailed)
            {
                _logger.LogError("Erro ao salvar log de auditoria temporário: {Errors}", 
                    string.Join("; ", resultado.Errors.Select(e => e.Message)));
                return resultado;
            }

            // Enfileira o processamento do log
            var jobId = _backgroundJobClient.Enqueue<IAuditLogService>(
                service => service.ProcessarLogAsync(auditLog.Id));

            _logger.LogDebug("Log de auditoria enfileirado - ID: {AuditLogId}, JobId: {JobId}", 
                auditLog.Id, jobId);

            return Result.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enfileirar log de auditoria - ID: {AuditLogId}", auditLog.Id);
            return Result.Fail($"Erro ao enfileirar log de auditoria: {ex.Message}");
        }
    }

    /// <summary>
    /// Processa um log de auditoria (executado pelo Hangfire)
    /// </summary>
    public async Task<Result> ProcessarLogAsync(Guid auditLogId)
    {
        try
        {
            var auditLog = await _auditLogRepositorio.ObterPorIdAsync(auditLogId);
            if (auditLog == null)
            {
                _logger.LogWarning("Log de auditoria não encontrado - ID: {AuditLogId}", auditLogId);
                return Result.Fail("Log de auditoria não encontrado");
            }

            // Incrementa o contador de tentativas
            auditLog.TentativasProcessamento++;

            try
            {
                // Aqui você pode adicionar lógicas específicas de processamento
                // Por exemplo: enviar para sistemas externos, indexar para busca, etc.
                
                // Simula processamento do log
                await ProcessarLogInterno(auditLog);

                // Marca como processado
                auditLog.Processado = true;
                auditLog.DataProcessamento = DateTimeOffset.UtcNow;
                auditLog.MensagemErro = null;

                await _auditLogRepositorio.AtualizarAsync(auditLog);

                _logger.LogInformation("Log de auditoria processado com sucesso - ID: {AuditLogId}, Tabela: {Tabela}, Operação: {Operacao}",
                    auditLogId, auditLog.NomeTabela, auditLog.TipoOperacao);

                return Result.Ok();
            }
            catch (Exception ex)
            {
                // Registra o erro no log
                auditLog.MensagemErro = ex.Message;
                await _auditLogRepositorio.AtualizarAsync(auditLog);

                _logger.LogError(ex, "Erro ao processar log de auditoria - ID: {AuditLogId}, Tentativa: {Tentativa}",
                    auditLogId, auditLog.TentativasProcessamento);

                // Se ainda não atingiu o limite de tentativas, reagenda
                if (auditLog.TentativasProcessamento < 3)
                {
                    var delay = TimeSpan.FromMinutes(Math.Pow(2, auditLog.TentativasProcessamento)); // Backoff exponencial
                    _backgroundJobClient.Schedule<IAuditLogService>(
                        service => service.ProcessarLogAsync(auditLogId), delay);
                    
                    _logger.LogInformation("Log de auditoria reagendado - ID: {AuditLogId}, Delay: {Delay}",
                        auditLogId, delay);
                }

                return Result.Fail($"Erro ao processar log: {ex.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro crítico ao processar log de auditoria - ID: {AuditLogId}", auditLogId);
            return Result.Fail($"Erro crítico: {ex.Message}");
        }
    }

    /// <summary>
    /// Processa múltiplos logs de auditoria em lote
    /// </summary>
    public async Task<Result> ProcessarLogsEmLoteAsync(List<Guid> auditLogIds)
    {
        try
        {
            var resultados = new List<Result>();
            
            foreach (var id in auditLogIds)
            {
                var resultado = await ProcessarLogAsync(id);
                resultados.Add(resultado);
            }

            var falhas = resultados.Where(r => r.IsFailed).ToList();
            if (falhas.Any())
            {
                _logger.LogWarning("Processamento em lote concluído com {Falhas} falhas de {Total} logs",
                    falhas.Count, auditLogIds.Count);
                return Result.Fail($"Processamento em lote com {falhas.Count} falhas");
            }

            _logger.LogInformation("Processamento em lote concluído com sucesso - {Total} logs processados",
                auditLogIds.Count);
            return Result.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar logs em lote");
            return Result.Fail($"Erro no processamento em lote: {ex.Message}");
        }
    }

    /// <summary>
    /// Reprocessa logs que falharam no processamento
    /// </summary>
    public async Task<Result> ReprocessarLogsFalhadosAsync(int maxTentativas = 3)
    {
        try
        {
            var logsFalhados = await _auditLogRepositorio.ObterLogsFalhadosAsync(maxTentativas);
            
            if (!logsFalhados.Any())
            {
                _logger.LogInformation("Nenhum log falhado encontrado para reprocessamento");
                return Result.Ok();
            }

            var ids = logsFalhados.Select(l => l.Id).ToList();
            _logger.LogInformation("Iniciando reprocessamento de {Count} logs falhados", ids.Count);
            
            return await ProcessarLogsEmLoteAsync(ids);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao reprocessar logs falhados");
            return Result.Fail($"Erro no reprocessamento: {ex.Message}");
        }
    }

    /// <summary>
    /// Limpa logs antigos baseado em critérios de retenção
    /// </summary>
    public async Task<Result> LimparLogsAntigosAsync(int diasRetencao = 365)
    {
        try
        {
            var dataLimite = DateTimeOffset.UtcNow.AddDays(-diasRetencao);
            var logsRemovidos = await _auditLogRepositorio.RemoverLogsAntigosAsync(dataLimite);
            
            _logger.LogInformation("Limpeza de logs concluída - {Count} logs removidos (anteriores a {DataLimite})",
                logsRemovidos, dataLimite.ToString("yyyy-MM-dd"));
            
            return Result.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao limpar logs antigos");
            return Result.Fail($"Erro na limpeza: {ex.Message}");
        }
    }

    /// <summary>
    /// Obtém estatísticas dos logs de auditoria
    /// </summary>
    public async Task<Result<AuditLogEstatisticas>> ObterEstatisticasAsync(DateTimeOffset dataInicio, DateTimeOffset dataFim)
    {
        try
        {
            var estatisticas = await _auditLogRepositorio.ObterEstatisticasAsync(dataInicio, dataFim);
            return Result.Ok(estatisticas);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas de auditoria");
            return Result.Fail<AuditLogEstatisticas>($"Erro ao obter estatísticas: {ex.Message}");
        }
    }

    /// <summary>
    /// Configura jobs recorrentes para manutenção dos logs
    /// </summary>
    public void ConfigurarJobsRecorrentes()
    {
        // Reprocessa logs falhados a cada 30 minutos
        _recurringJobManager.AddOrUpdate<IAuditLogService>(
            "reprocessar-logs-falhados",
            service => service.ReprocessarLogsFalhadosAsync(3),
            "*/30 * * * *"); // A cada 30 minutos

        // Limpa logs antigos diariamente às 2h da manhã
        _recurringJobManager.AddOrUpdate<IAuditLogService>(
            "limpar-logs-antigos",
            service => service.LimparLogsAntigosAsync(365),
            "0 2 * * *"); // Diariamente às 2h
    }

    /// <summary>
    /// Lógica interna de processamento do log (pode ser customizada)
    /// </summary>
    private async Task ProcessarLogInterno(AuditLog auditLog)
    {
        // Aqui você pode implementar lógicas específicas como:
        // - Enviar para sistemas de monitoramento
        // - Indexar para busca
        // - Enviar notificações
        // - Integrar com sistemas externos
        // - Etc.
        
        // Por enquanto, apenas simula um processamento
        await Task.Delay(100); // Simula processamento
        
        _logger.LogDebug("Processamento interno concluído para log {AuditLogId}", auditLog.Id);
    }
}