using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;
using Otikka.Persistence.Repositories;
using Otikka.Persistence.Services;
using Gestao.Dominio.Repositorios;

namespace Otikka.Persistence;

public static class PersistenceServiceRegistration
{
    public static IServiceCollection AddPersistenceServices(this IServiceCollection services, IConfiguration configuration)
    {
        AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

        var connectionString = configuration.GetConnectionString("AppConnection") ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
        
        services.AddDbContextFactory<ApplicationDbContext>(options =>
        {
            options.UseNpgsql(connectionString);
            
            // Adiciona logging SQL apenas no ambiente de desenvolvimento
            var environment = configuration["ASPNETCORE_ENVIRONMENT"] ?? Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (environment == "Development")
            {
                options.EnableSensitiveDataLogging()
                       .EnableDetailedErrors()
                       .LogTo(Console.WriteLine, LogLevel.Information);
            }
        });

        services.AddScoped<IUsuarioRepositorio, UsuarioRepository>();
        services.AddScoped<IEmpresaRepositorio, EmpresaRepositorio>();
        services.AddScoped<IFormaPagamentoRepositorio, FormaPagamentoRepositorio>();
        services.AddScoped<IUsuarioEmpresaRepositorio, UsuarioEmpresaRepositorio>();

        // Repositórios do ProdutoModulo
        services.AddScoped<ICategoriaProdutoRepositorio, CategoriaProdutoRepositorio>();
        services.AddScoped<IMarcaProdutoRepositorio, MarcaProdutoRepositorio>();
        services.AddScoped<IProdutoRepositorio, ProdutoRepositorio>();

        // Repositórios do PessoaModulo
        services.AddScoped<IPessoaRepositorio, PessoaRepositorio>();

        // Repositórios do VendaModulo
        services.AddScoped<IVendaRepositorio, VendaRepositorio>();

        // Repositórios do CompraModulo
        services.AddScoped<ICompraRepositorio, CompraRepositorio>();
        services.AddScoped<IMovimentacaoEstoqueRepositorio, MovimentacaoEstoqueRepositorio>();
        services.AddScoped<INFeEntradaRepositorio, NFeEntradaRepositorio>();

        // Repositórios do OrdemServicoModulo
        services.AddScoped<IOrdemServicoRepositorio, OrdemServicoRepositorio>();

        // Repositórios do TransacaoComercialModulo
        services.AddScoped<ITransacaoComercialRepositorio, TransacaoComercialRepositorio>();

        // Repositórios do TransacaoFinanceiraModulo
        services.AddScoped<ITransacaoFinanceiraRepositorio, TransacaoFinanceiraRepositorio>();
        services.AddScoped<IPagamentoRepositorio, PagamentoRepositorio>();

        // Outros repositórios
        services.AddScoped<IColaboradorRepositorio, ColaboradorRepositorio>();
        services.AddScoped<IColaboradorEmpresaRepositorio, ColaboradorEmpresaRepositorio>();
        services.AddScoped<IReceitaRepositorio, ReceitaRepositorio>();
        services.AddScoped<IDocumentoRepositorio, DocumentoRepositorio>();
        services.AddScoped<IProdutoServicoVendidoRepositorio, ProdutoServicoVendidoRepositorio>();
        services.AddScoped<ICategoriaFinanceiraRepositorio, CategoriaFinanceiraRepositorio>();
        
        // Repositório de Cache de CEP
        services.AddScoped<ICepCacheRepository, CepCacheRepository>();
        
        // Repositório de Auditoria
        services.AddSingleton<IAuditLogRepositorio, AuditLogRepositorio>();

        return services;
    }
}
