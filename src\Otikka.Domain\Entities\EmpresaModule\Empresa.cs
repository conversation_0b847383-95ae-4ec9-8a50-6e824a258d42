using System.Text.Json.Serialization;

namespace Otikka.Domain.Entities.EmpresaModule;

public class Empresa : EntidadeBase
{
    [Display(Name = "Razão Social")]
    public string? RazaoSocial { get; set; }

    [Display(Name = "InstanciaNome Fantasia")]
    public string? NomeFantasia { get; set; }

    [Display(Name = "Documento")]
    public string? Documento { get; set; }

    [Display(Name = "Sigla")]
    public string? Sigla { get; set; }

    [Display(Name = "Logo")]
    public string? Logo { get; set; }

    [Display(Name = "Logo Largura")]
    public int? LogoLargura { get; set; }

    [Display(Name = "Logo Altura")]
    public int? LogoAltura { get; set; }
    public Endereco? Endereco { get; set; } = new Endereco();

    [Display(Name = "Tipo de Unidade")]
    public TipoUnidadeEmpresa TipoUnidade { get; set; }

    [Display(Name = "Empresa Pai")]
    public Guid? EmpresaPaiId { get; set; }

    [JsonIgnore]
    public Empresa? EmpresaPai { get; set; }

    [JsonIgnore]
    public ICollection<UsuarioEmpresa>? UsuariosEmpresas { get; set; }

    [Display(Name = "Email")]
    public string? Email { get; set; }

    [Display(Name = "Telefone")]
    public string? Telefone { get; set; }

    [Display(Name = "Usuário Id")]
    public Guid? UsuarioId { get; set; }

    [JsonIgnore]
    public Usuario? Usuario { get; set; }

    public ICollection<Pessoa>? EmpresaPessoas { get; set; }

    public ICollection<CategoriaTransacaoFinanceira>? Categorias { get; set; }
    public ICollection<TransacaoFinanceira>? TransacoesFinanceiras { get; set; }

    #region Configurações
    public TipoAlgoritmoGeradorId TipoNumeracaoOrdemServico { get; set; }
    #endregion
}
