using Hangfire;
using Otikka.Workers.Jobs;

namespace Otikka.Workers.Configuration;

/// <summary>
/// Configuração dos jobs do Hangfire para o sistema de auditoria
/// </summary>
public static class HangfireJobsConfiguration
{
    /// <summary>
    /// Configura todos os jobs recorrentes do sistema
    /// </summary>
    public static void ConfigurarJobsRecorrentes()
    {        
        // Configura job para processar logs de auditoria falhados a cada 30 minutos
        RecurringJob.AddOrUpdate<AuditLogJob>(
            "reprocessar-logs-falhados",
            job => job.ReprocessarLogsFalhadosAsync(),
            "*/30 * * * *"); // A cada 30 minutos

        // Configura job para limpar logs antigos diariamente às 2:00
        RecurringJob.AddOrUpdate<AuditLogJob>(
            "limpar-logs-antigos",
            job => job.LimparLogsAntigosAsync(90), // Remove logs com mais de 90 dias
            "0 2 * * *"); // Diariamente às 2:00

        // Configura job para gerar relatório de estatísticas semanalmente
        RecurringJob.AddOrUpdate<AuditLogJob>(
            "gerar-relatorio-estatisticas",
            job => job.GerarRelatorioEstatisticasAsync(),
            "0 6 * * 1"); // Segundas-feiras às 6:00
    }

    /// <summary>
    /// Remove todos os jobs recorrentes do sistema de auditoria
    /// </summary>
    public static void RemoverJobsRecorrentes()
    {
        RecurringJob.RemoveIfExists("reprocessar-logs-falhados");
        RecurringJob.RemoveIfExists("limpar-logs-antigos");
        RecurringJob.RemoveIfExists("gerar-relatorio-estatisticas");
    }
}