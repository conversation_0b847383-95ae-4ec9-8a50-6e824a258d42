@inherits PageBase

@using Otikka.Application.Features.MarcaProduto.Queries.ListAllMarcasProduto
@using Otikka.Application.Features.CategoriaProduto.Queries.ListAllCategoriasProduto
@using Otikka.Application.Features.Produto.Commands.CreateProduto
@using Otikka.Application.Utilities
@using Otikka.Domain.Enums

@inject ILogger<ProdutoCadastroRapidoForm> Logger

<div class="modal-header">
    <h5 class="modal-title">Cadastro Rápido de Produto</h5>
    <button type="button" class="btn-close" @onclick="Fechar"></button>
</div>

<EditForm Model="Produto" OnValidSubmit="Submit" FormName="ProdutoCadastroRapido">
    <FluentValidationValidator Options="@(options => options.IncludeRuleSets("ProdutoValidacao"))" />

    <div class="modal-body">
        <!-- Informações Básicas -->
        <div class="mb-4">
            <h6 class="fw-semibold mb-3">Informações Básicas</h6>
            <div class="row gy-3">
                <div class="col-md-6">
                    <div>
                        <label for="codigo" class="form-label">Código de Referência <span class="text-danger">*</span></label>
                        <InputText @bind-Value="Produto!.Codigo" autocomplete="not" class="form-control" id="codigo" placeholder="Digite o código de referência"/>
                        <ValidationMessage For="() => Produto!.Codigo"/>
                    </div>
                </div>
                <div class="col-md-6">
                    <div>
                        <label for="name" class="form-label">Nome do Produto <span class="text-danger">*</span></label>
                        <InputText @bind-Value="Produto!.Nome" autocomplete="not" class="form-control" id="name" placeholder="Digite o nome do produto"/>
                        <ValidationMessage For="() => Produto!.Nome"/>
                    </div>
                </div>
                <div class="col-md-6">
                    <div>
                        <label for="ean" class="form-label">Código de Barras (EAN)</label>
                        <InputText @bind-Value="Produto!.EAN" autocomplete="not" class="form-control" id="ean" placeholder="Digite o código EAN" onkeypress="return (event.charCode !=8 && event.charCode ==0 || (event.charCode >= 48 && event.charCode <= 57))"/>
                        <ValidationMessage For="() => Produto!.EAN"/>
                    </div>
                </div>
                <div class="col-md-6">
                    <div>
                        <label for="precoVenda" class="form-label">Preço de Venda <span class="text-danger">*</span></label>
                        <InputNumber @bind-Value="Produto!.PrecoVenda" class="form-control" id="precoVenda" placeholder="0,00" min="0" step="0.01"/>
                        <ValidationMessage For="() => Produto!.PrecoVenda"/>
                    </div>
                </div>
            </div>
        </div>

        <!-- Classificação -->
        <div class="mb-4">
            <h6 class="fw-semibold mb-3">Classificação</h6>
            <div class="row gy-3">
                <div class="col-md-6">
                    <div>
                        <label for="categorias" class="form-label">Categoria</label>
                        @if (CategoriasProdutos is not null)
                        {
                            <InputSelect @bind-Value="Produto!.CategoriaProdutoId" autocomplete="not" class="form-select" id="categorias">
                                <option value="">Selecione uma categoria</option>
                                @foreach (var entity in CategoriasProdutos)
                                {
                                    <option value="@entity.Id">@entity.Nome</option>
                                }
                            </InputSelect>
                        }
                        else
                        {
                            <select class="form-select" disabled>
                                <option>Nenhuma categoria cadastrada</option>
                            </select>
                        }
                        <ValidationMessage For="() => Produto!.CategoriaProdutoId"/>
                    </div>
                </div>
                <div class="col-md-6">
                    <div>
                        <label for="marcas" class="form-label">Marca</label>
                        @if (MarcasProduto is not null)
                        {
                            <InputSelect @bind-Value="Produto!.MarcaProdutoId" autocomplete="not" class="form-select" id="marcas">
                                <option value="">Selecione uma marca</option>
                                @foreach (var entity in MarcasProduto)
                                {
                                    <option value="@entity.Id">@entity.Nome</option>
                                }
                            </InputSelect>
                        }
                        else
                        {
                            <select class="form-select" disabled>
                                <option>Nenhuma marca cadastrada</option>
                            </select>
                        }
                        <ValidationMessage For="() => Produto!.MarcaProdutoId"/>
                    </div>
                </div>
            </div>
        </div>

        <!-- Controle de Estoque -->
        <div class="mb-4">
            <div class="d-flex align-items-center justify-content-between mb-3">
                <h6 class="fw-semibold mb-0">Controle de Estoque</h6>
                <div class="form-check form-switch">
                    <InputCheckbox @bind-Value="Produto!.ControleEstoque" class="form-check-input" id="controleEstoque"/>
                    <label class="form-check-label ms-2" for="controleEstoque">
                        <strong>Controlar Estoque</strong>
                    </label>
                </div>
            </div>
            @if (Produto!.ControleEstoque)
            {
                <div class="row gy-3">
                    <div class="col-md-6">
                        <div>
                            <label for="quantidadeEstoqueCorrente" class="form-label">Quantidade em Estoque</label>
                            <InputNumber @bind-Value="Produto!.QuantidadeEstoqueCorrente" class="form-control" id="quantidadeEstoqueCorrente" placeholder="0" min="0"/>
                            <ValidationMessage For="() => Produto!.QuantidadeEstoqueCorrente"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div>
                            <label for="quantidadeEstoqueMinimo" class="form-label">Estoque Mínimo</label>
                            <InputNumber @bind-Value="Produto!.QuantidadeEstoqueMinimo" class="form-control" id="quantidadeEstoqueMinimo" placeholder="0" min="0"/>
                            <ValidationMessage For="() => Produto!.QuantidadeEstoqueMinimo"/>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-light" @onclick="Fechar">Cancelar</button>
        <SubmitButton IsProcessing="Processing" ButtonClass="btn-primary">
            <i class="ti ti-device-floppy me-1"></i>Cadastrar Produto
        </SubmitButton>
    </div>
</EditForm>

@code {
    [CascadingParameter] public BlazoredModalInstance BlazoredModal { get; set; } = default!;
    
    private Domain.Entities.ProdutoModulo.Produto? Produto { get; set; } = new();
    private List<Domain.Entities.ProdutoModulo.CategoriaProduto>? CategoriasProdutos { get; set; }
    private List<Domain.Entities.ProdutoModulo.MarcaProduto>? MarcasProduto { get; set; }

    protected override async Task OnInitializedAsync()
    {
        Logger.LogInformation("Inicializando formulário de cadastro rápido de produto");
        
        // Inicializa o produto com valores padrão
        Produto = new()
        {
            Id = Guid.NewGuid(),
            Ativo = true,
            ControleEstoque = false,
            QuantidadeEstoqueCorrente = 0,
            QuantidadeEstoqueMinimo = 0,
            PrecoVenda = 0
        };

        try
        {
            var empresaId = await GetEmpresaIdAsync();

            // Carregar categorias usando CQRS
            var categoriasResult = await MessageBus.InvokeAsync<Result<List<Domain.Entities.ProdutoModulo.CategoriaProduto>>>(
                new ListAllCategoriasProduto() { EmpresaId = empresaId }
            );
            if (categoriasResult.IsSuccess)
            {
                CategoriasProdutos = categoriasResult.Value;
                Logger.LogInformation("Categorias carregadas com sucesso: {Count}", CategoriasProdutos.Count);
            }
            else
            {
                Logger.LogWarning("Erro ao carregar categorias: {Error}", categoriasResult.Errors.FirstOrDefault()?.Message);
                await AlertService.ShowAlert("Erro", "Não foi possível carregar as categorias de produtos.");
            }

            // Carregar marcas usando CQRS
            var marcasResult = await MessageBus.InvokeAsync<Result<List<Domain.Entities.ProdutoModulo.MarcaProduto>>>(
                new ListAllMarcasProduto() { EmpresaId = empresaId }
            );
            if (marcasResult.IsSuccess)
            {
                MarcasProduto = marcasResult.Value;
                Logger.LogInformation("Marcas carregadas com sucesso: {Count}", MarcasProduto.Count);
            }
            else
            {
                Logger.LogWarning("Erro ao carregar marcas: {Error}", marcasResult.Errors.FirstOrDefault()?.Message);
                await AlertService.ShowAlert("Erro", "Não foi possível carregar as marcas de produtos.");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao carregar dados para formulário de cadastro rápido de produto");
            await AlertService.ShowAlert("Erro", $"Ocorreu um erro ao inicializar o formulário: {ex.Message}");
        }
    }

    private async Task Submit()
    {
        try
        {
            await ProcessingChange(true);
            
            Logger.LogInformation("Iniciando cadastro rápido de produto: {Nome}", Produto?.Nome);
            
            if (Produto == null)
            {
                await AlertService.ShowAlert("Erro", "Dados do produto inválidos.");
                return;
            }

            // Define a empresa do produto
            Produto.EmpresaId = await GetEmpresaIdAsync();
            
            // Cria o comando para cadastrar o produto
            var command = new CreateProduto
            {
                Id = Produto.Id,
                Codigo = Produto.Codigo,
                Nome = Produto.Nome,
                EAN = Produto.EAN,
                SKU = Produto.SKU,
                PrecoVenda = Produto.PrecoVenda,
                MarcaProdutoId = Produto.MarcaProdutoId,
                CategoriaProdutoId = Produto.CategoriaProdutoId,
                ControleEstoque = Produto.ControleEstoque,
                QuantidadeEstoqueCorrente = Produto.QuantidadeEstoqueCorrente,
                QuantidadeEstoqueMinimo = Produto.QuantidadeEstoqueMinimo,
                EmpresaId = Produto.EmpresaId,
                Ativo = true
            };

            // Envia o comando para criar o produto
            var result = await MessageBus.InvokeAsync<Result<Domain.Entities.ProdutoModulo.Produto>>(command);

            if (result.IsSuccess)
            {
                Logger.LogInformation("Produto cadastrado com sucesso: {Id}", result.Value.Id);
                await ToastService.ShowSuccess("Produto cadastrado com sucesso!");
                
                // Fecha o modal e retorna o produto cadastrado
                await BlazoredModal.CloseAsync(ModalResult.Ok(result.Value));
            }
            else
            {
                var errorMessage = result.Errors.FirstOrDefault()?.Message ?? "Erro ao cadastrar produto";
                Logger.LogWarning("Falha ao cadastrar produto: {Error}", errorMessage);
                await AlertService.ShowError("Erro de Validação", errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro inesperado ao cadastrar produto: {Message}", ex.Message);
            await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
        }
        finally
        {
            await ProcessingChange(false);
        }
    }

    private async Task Fechar()
    {
        await BlazoredModal.CancelAsync();
    }
}