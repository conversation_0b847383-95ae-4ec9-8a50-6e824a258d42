﻿namespace Otikka.Application.Models;

public class PaginatedList<T>
{
    public PaginatedList(List<T> items, int pageIndex, int totalPages)
    {
        Items = items;
        PageIndex = pageIndex;
        TotalPages = totalPages;
        TotalCount = items.Count;
        SortColumn = string.Empty;
        Ascending = true;
    }
    public PaginatedList(List<T> items, int pageIndex, int totalPages, string sortColumn, bool ascending)
    {
        Items = items;
        PageIndex = pageIndex;
        TotalPages = totalPages;
        TotalCount = items.Count;
        SortColumn = sortColumn;
        Ascending = ascending;
    }
    public PaginatedList(List<T> items, int pageIndex, int totalPages, int totalCount, string sortColumn, bool ascending)
    {
        Items = items;
        PageIndex = pageIndex;
        TotalPages = totalPages;
        TotalCount = totalCount;
        SortColumn = sortColumn;
        Ascending = ascending;
    }
    public string SortColumn { get; set; } = string.Empty; // InstanciaNome da coluna para ordenação (ex: "InstanciaNome", "Preco")
    public bool Ascending { get; set; }
    public List<T> Items { get; } = new List<T>();
    public int PageIndex { get; }
    public int TotalPages { get; }
    public int TotalCount { get; }
    public bool HasNextPage => PageIndex < TotalPages;
    public bool HasPreviousPage => PageIndex > 1;
}
