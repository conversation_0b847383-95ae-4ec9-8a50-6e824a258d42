using Hangfire;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Infrastructure;
using Otikka.Domain.Entities.Common;

namespace Otikka.Workers.Jobs;

/// <summary>
/// Job Hangfire responsável por processar logs de auditoria em background
/// </summary>
public class AuditLogJob
{
    private readonly IAuditLogService _auditLogService;
    private readonly ILogger<AuditLogJob> _logger;

    public AuditLogJob(
        IAuditLogService auditLogService,
        ILogger<AuditLogJob> logger)
    {
        _auditLogService = auditLogService;
        _logger = logger;
    }

    /// <summary>
    /// Processa um log de auditoria individual
    /// </summary>
    /// <param name="auditLogId">ID do log de auditoria</param>
    [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 30, 60, 120 })]
    public async Task ProcessarLogIndividualAsync(Guid auditLogId)
    {
        try
        {
            _logger.LogInformation("Iniciando processamento do log de auditoria {AuditLogId}", auditLogId);
            
            await _auditLogService.ProcessarLogAsync(auditLogId);
            
            _logger.LogInformation("Log de auditoria {AuditLogId} processado com sucesso", auditLogId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar log de auditoria {AuditLogId}", auditLogId);
            throw; // Re-throw para que o Hangfire possa tentar novamente
        }
    }

    /// <summary>
    /// Processa logs de auditoria em lote
    /// </summary>
    /// <param name="quantidadeLogs">Quantidade de logs para processar</param>
    [AutomaticRetry(Attempts = 2, DelaysInSeconds = new[] { 60, 180 })]
    public async Task ProcessarLogsEmLoteAsync(int quantidadeLogs = 50)
    {
        try
        {
            _logger.LogInformation("Iniciando processamento em lote de {Quantidade} logs de auditoria", quantidadeLogs);
            
            var resultado = await _auditLogService.ProcessarLogsEmLoteAsync(quantidadeLogs);
            
            _logger.LogInformation("Processamento em lote concluído. {Processados} logs processados, {Falhados} falharam", 
                resultado.LogsProcessados, resultado.LogsFalhados);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no processamento em lote de logs de auditoria");
            throw;
        }
    }

    /// <summary>
    /// Reprocessa logs que falharam anteriormente
    /// </summary>
    /// <param name="tentativasMaximas">Número máximo de tentativas para considerar</param>
    [AutomaticRetry(Attempts = 1, DelaysInSeconds = new[] { 300 })]
    public async Task ReprocessarLogsFalhadosAsync(int tentativasMaximas = 3)
    {
        try
        {
            _logger.LogInformation("Iniciando reprocessamento de logs falhados (máx {TentativasMaximas} tentativas)", tentativasMaximas);
            
            var resultado = await _auditLogService.ReprocessarLogsFalhadosAsync(tentativasMaximas);
            
            _logger.LogInformation("Reprocessamento concluído. {Reprocessados} logs reprocessados", resultado);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no reprocessamento de logs falhados");
            throw;
        }
    }

    /// <summary>
    /// Remove logs antigos do sistema
    /// </summary>
    /// <param name="diasParaManter">Número de dias para manter os logs</param>
    [AutomaticRetry(Attempts = 1, DelaysInSeconds = new[] { 600 })]
    public async Task LimparLogsAntigosAsync(int diasParaManter = 90)
    {
        try
        {
            _logger.LogInformation("Iniciando limpeza de logs antigos (mantendo {Dias} dias)", diasParaManter);
            
            var dataLimite = DateTimeOffset.UtcNow.AddDays(-diasParaManter);
            var logsRemovidos = await _auditLogService.LimparLogsAntigosAsync(dataLimite);
            
            _logger.LogInformation("Limpeza concluída. {LogsRemovidos} logs removidos", logsRemovidos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro na limpeza de logs antigos");
            throw;
        }
    }

    /// <summary>
    /// Gera relatório de estatísticas dos logs de auditoria
    /// </summary>
    [AutomaticRetry(Attempts = 1)]
    public async Task GerarRelatorioEstatisticasAsync()
    {
        try
        {
            _logger.LogInformation("Gerando relatório de estatísticas de auditoria");
            
            var dataInicio = DateTimeOffset.UtcNow.AddDays(-30); // Últimos 30 dias
            var dataFim = DateTimeOffset.UtcNow;
            
            var estatisticas = await _auditLogService.ObterEstatisticasAsync(dataInicio, dataFim);
            
            _logger.LogInformation(
                "Estatísticas dos últimos 30 dias: {TotalLogs} logs, {Processados} processados, {Falhados} falhados, {Pendentes} pendentes",
                estatisticas.TotalLogs,
                estatisticas.LogsProcessados,
                estatisticas.LogsFalhados,
                estatisticas.LogsPendentes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao gerar relatório de estatísticas");
            // Não re-throw aqui pois é apenas um relatório
        }
    }

    /// <summary>
    /// Configura jobs recorrentes do Hangfire para auditoria
    /// </summary>
    public static void ConfigurarJobsRecorrentes()
    {
        // Processa logs em lote a cada 5 minutos
        RecurringJob.AddOrUpdate<AuditLogJob>(
            "processar-logs-lote",
            job => job.ProcessarLogsEmLoteAsync(100),
            "*/5 * * * *"); // A cada 5 minutos

        // Reprocessa logs falhados a cada hora
        RecurringJob.AddOrUpdate<AuditLogJob>(
            "reprocessar-logs-falhados",
            job => job.ReprocessarLogsFalhadosAsync(3),
            "0 * * * *"); // A cada hora

        // Limpa logs antigos diariamente às 2:00 AM
        RecurringJob.AddOrUpdate<AuditLogJob>(
            "limpar-logs-antigos",
            job => job.LimparLogsAntigosAsync(90),
            "0 2 * * *"); // Diariamente às 2:00 AM

        // Gera relatório de estatísticas diariamente às 6:00 AM
        RecurringJob.AddOrUpdate<AuditLogJob>(
            "relatorio-estatisticas",
            job => job.GerarRelatorioEstatisticasAsync(),
            "0 6 * * *"); // Diariamente às 6:00 AM
    }

    /// <summary>
    /// Remove jobs recorrentes (útil para testes ou desabilitação)
    /// </summary>
    public static void RemoverJobsRecorrentes()
    {
        RecurringJob.RemoveIfExists("processar-logs-lote");
        RecurringJob.RemoveIfExists("reprocessar-logs-falhados");
        RecurringJob.RemoveIfExists("limpar-logs-antigos");
        RecurringJob.RemoveIfExists("relatorio-estatisticas");
    }
}