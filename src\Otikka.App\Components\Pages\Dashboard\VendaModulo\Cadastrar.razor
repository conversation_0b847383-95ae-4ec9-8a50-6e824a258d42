﻿@using Otikka.App.Components.Pages.Dashboard.VendaModulo.Componentes
@using Otikka.Domain.Entities.Common
@using Otikka.Application.Features.Venda.Commands.CreateVenda
@using Wolverine
@attribute [Route(Application.Routes.VendaCadastrar)]
@inherits PageBase
<PageTitle>
    Venda - Cadastro
</PageTitle>
<VendaForm EhEdicao="@false" Venda="Venda" OnSave="Save" />

@code {

    [SupplyParameterFromForm] private CreateVenda Venda { get; set; } = new() { DataVenda = DateTimeOffset.Now, ProdutoServicoVendidos = new List<ProdutoServicoVendido>() };

    private async Task Save()
    {
        try
        {
            Venda.CriadoPorId = await GetUsuarioIdLoggedAsync();
            Venda.DataCriacao = DateTime.UtcNow;
            Venda.EmpresaId = await GetEmpresaIdAsync();
            
            var result = await MessageBus.InvokeAsync<Result>(Venda);

            if (result.IsSuccess)
            {
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.VendaListar);
            }
            else
            {
                await AlertService.ShowError("Opps!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao cadastrar venda");
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Opps!", $"Erro inesperado: {ex.Message}");
        }
    }

}