﻿@using Otikka.App.Components.Generics.Componentes
@using Otikka.App.Components.Pages
@using Otikka.App.Services
@using Otikka.Domain.Entities.Common
@using Otikka.Domain.Entities.ProdutoModulo
@using Otikka.Application.Features.Produto.Queries.GetAllProdutos
@inherits PageBase

@inject ProdutoVendaEstado ProdutoVendaEstado

@if (OrdemServicoOuVenda is not null)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Produtos</h4>
                    <button class="btn btn-sm btn-primary" type="button" @onclick="AbrirModal">Incluir</button>
                </div><!-- end card header -->
                <div class="card-body pt-0" style="padding-right:12px; padding-left: 12px;">
                    <ValidationMessage For="() => OrdemServicoOuVenda.ProdutoServicoVendidos" class="alert alert-danger my-2" role="alert" />
                    <div class="row table-responsive">
                        <table class="table">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col">Produto</th>
                                    <th scope="col" style="min-width:80px">Quantidade</th>
                                    <th scope="col" style="min-width:150px">Valor Unitário</th>
                                    <th scope="col">Valor Total</th>
                                    <th scope="col"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (OrdemServicoOuVenda is not null && OrdemServicoOuVenda.ProdutoServicoVendidos is not null && OrdemServicoOuVenda.ProdutoServicoVendidos.Count > 0)
                                {
                                    @foreach (ProdutoServicoVendido produtoServico in OrdemServicoOuVenda.ProdutoServicoVendidos)
                                    {
                                        <tr>
                                            <td class="align-middle">@(produtoServico.Produto is not null ? produtoServico.Produto.Nome : "-")</td>
                                            <td class="align-middle">
                                                <InputNumber @bind-Value="produtoServico.Quantidade" class="form-control" @bind-Value:after="RecalcularResumoValores" />
                                            </td>
                                            <td class="align-middle">
                                                <div class="input-group">
                                                    @if (produtoServico.PrecoProduto != produtoServico.PrecoVenda)
                                                    {
                                                        <span class="input-group-text">
                                                            <small>
                                                                <s>(@produtoServico.PrecoProduto.ToString("C"))</s>
                                                            </small>
                                                        </span>
                                                    }
                                                    <span class="input-group-text">R$</span>
                                                    <InputNumber @bind-Value="produtoServico.PrecoVenda" class="form-control" @bind-Value:after="RecalcularResumoValores" />
                                                </div>
                                            </td>
                                            <td class="align-middle">@((produtoServico.PrecoVenda * produtoServico.Quantidade).ToString("C"))</td>
                                            <td>
                                                <button class="btn btn-danger btn-sm" type="button" @onclick="() => ExcluirItem(produtoServico)">Excluir</button>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="5" class="text-center">
                                            <div class="alert alert-dark mb-0" role="alert">
                                                Nenhum <b>produto</b> registrado!
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3"></td>
                                    <td>
                                        <b>Valor (sem desc/acresc.):</b>
                                    </td>
                                    <td id="product_service_total">@ValorTotal.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td colspan="3"></td>
                                    <td>
                                        <b>Acrescimo/Desconto:</b>
                                    </td>
                                    <td id="product_service_acres_discount">@AcrescimoDesconto.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td colspan="3"></td>
                                    <td>
                                        <b>Valor final:</b>
                                    </td>
                                    <td id="product_service_liquid">@ValorLiquido.ToString("C")</td>
                                </tr>
                            </tfoot>
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
}
@code {
    public List<Produto>? Produtos { get; set; }

    public decimal ValorTotal { get; set; } = 0;
    public decimal ValorLiquido { get; set; } = 0;
    public decimal AcrescimoDesconto { get; set; } = 0;

    [Parameter] public TransacaoComercial? OrdemServicoOuVenda { get; set; }
    [Parameter] public bool IncluirEncomenda { get; set; }


    protected override async Task OnParametersSetAsync()
    {
        try
        {
            var produtosQuery = new GetAllProdutos { IncluirProdutoPorEncomendado = IncluirEncomenda, EmpresaId = await GetEmpresaIdAsync() };
            var produtosResult = await MessageBus.InvokeAsync<FluentResults.Result<List<Produto>>>(produtosQuery);

            CalcularTotal();
            CalcularValorLiquido();
            CalcularDescontoAcrescimo();

            if (produtosResult.IsSuccess)
            {
                Produtos = produtosResult.Value;
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", produtosResult.Errors.Select(e => e.Message)));
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowAlert("Erro", $"Erro ao carregar produtos: {ex.Message}");
        }
    }

    public void ExcluirItem(ProdutoServicoVendido produtoServico)
    {
        if (OrdemServicoOuVenda is not null && OrdemServicoOuVenda.ProdutoServicoVendidos is not null)
            OrdemServicoOuVenda.ProdutoServicoVendidos.Remove(produtoServico);
        RecalcularResumoValores();
    }

    public void RecalcularResumoValores()
    {
        CalcularTotal();
        CalcularValorLiquido();
        CalcularDescontoAcrescimo();
        ProdutoVendaEstado.NotificarMudanca();
    }

    public void CalcularTotal()
    {
        decimal total = 0;
        ValorTotal = total;
    }

    public void CalcularDescontoAcrescimo()
    {
        AcrescimoDesconto = ValorLiquido - ValorTotal;
    }

    public void CalcularValorLiquido()
    {
        decimal total = 0;
        if (OrdemServicoOuVenda is null || OrdemServicoOuVenda.ProdutoServicoVendidos is null) return;
        foreach (var produto in OrdemServicoOuVenda.ProdutoServicoVendidos)
        {
            total += produto.Quantidade * produto.PrecoVenda;
        }

        ValorLiquido = total;
        OrdemServicoOuVenda.TransacaoFinanceira!.ValorTotal = total;
    }

    private async Task AbrirModal()
    {
        if (OrdemServicoOuVenda is not null && OrdemServicoOuVenda.EmpresaId == Guid.Empty)
        {
            OrdemServicoOuVenda.EmpresaId = await GetEmpresaIdAsync();
        }
        var parameters = new ModalParameters()
            .Add("IncluirEncomenda", IncluirEncomenda)
            .Add("Produtos", Produtos)
            .Add("OrdemServicoOuVenda", OrdemServicoOuVenda);
        var options = new ModalOptions();
        options.Size = ModalSize.ExtraLarge;

        var produtoServico = Modal.Show<ProdutoServicoVendaIncluirForm>("Incluir produto", parameters, options);

        var result = await produtoServico.Result;

        if (result.Confirmed && result.Data is ProdutoServicoVendido produtoVenda)
        {
            var produto = Produtos?.First(a => a.Id == produtoVenda.ProdutoId);

            var existeProduto = OrdemServicoOuVenda?.ProdutoServicoVendidos?.Any(a => a.ProdutoId == produtoVenda.ProdutoId) ?? false;
            if (existeProduto)
            {
                await ToastService.ShowWarning("Este produto já foi adicionado!");
                return;
            }

            produtoVenda.EmpresaId = await GetEmpresaIdAsync();
            produtoVenda.ProdutoId = produto!.Id;
            produtoVenda.Produto = produto;

            OrdemServicoOuVenda?.ProdutoServicoVendidos?.Add(produtoVenda);
            RecalcularResumoValores();
        }
    }
}