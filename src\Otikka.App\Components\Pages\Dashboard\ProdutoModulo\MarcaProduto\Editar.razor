@attribute [Route(Application.Routes.MarcaEditar)]
@inherits PageBase

@using Otikka.App.Components.Pages.Dashboard.ProdutoModulo.MarcaProduto.Componentes
@using Otikka.Application.Features.MarcaProduto.Commands.UpdateMarcaProduto
@using Otikka.Application.Features.MarcaProduto.Queries.GetMarcaProduto

@inject ILogger<Editar> Logger

<PageTitle>
    Marca - Atualizar
</PageTitle>

@if (IsLoading)
{
    <div class="d-flex justify-content-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
    </div>
}
else
{
    <MarcaProdutoForm EhEdicao="@true" MarcaProduto="MarcaProduto" OnSave="Save" />
}

@code {
    
    [Parameter] public Guid Id { get; set; }
    [SupplyParameterFromForm] private UpdateMarcaProduto MarcaProduto { get; set; } = new();
    private bool IsLoading = true;
    

    protected override async Task OnParametersSetAsync()
    {
        Logger.LogInformation("Carregando marca de produto para edição: {Id}", Id);

        var result = await MessageBus.InvokeAsync<Result<Domain.Entities.ProdutoModulo.MarcaProduto>>(new GetMarcaProduto { Id = Id });

        if (result.IsSuccess && result.Value != null)
        {
            var marca = result.Value;
            
            // Mapear para UpdateMarcaProduto
            MarcaProduto = new UpdateMarcaProduto
            {
                Id = marca.Id,
                Version = marca.Version,
                Nome = marca.Nome,
                EmpresaId = marca.EmpresaId,
                DataCriacao = marca.DataCriacao,
                DataAtualizacao = marca.DataAtualizacao,
                AtualizadoPorId = marca.AtualizadoPorId
            };

            // Verificar se pertence à empresa
            var empresaId = await GetEmpresaIdAsync();
            if (MarcaProduto.EmpresaId != empresaId)
            {
                await AlertService.ShowError("Opps!", $"A marca não pertence à empresa!");
                NavigationManager.NavigateTo(Application.Routes.MarcaListar);
                return;
            }
        }
        else
        {
            await AlertService.ShowError("Opps!","Marca não encontrada!");
            NavigationManager.NavigateTo(Application.Routes.MarcaListar);
            return;
        }

        IsLoading = false;
    }

    private async Task Save()
    {
        Logger.LogInformation("Iniciando atualização de marca de produto: {InstanciaNome} - ID: {Id}", MarcaProduto.Nome, MarcaProduto.Id);

        var result = await MessageBus.InvokeAsync<Result>(MarcaProduto);
        
        if (result.IsSuccess)
        {
            await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
            NavigationManager.NavigateTo(Application.Routes.MarcaListar);
        }
        else
        {
            await AlertService.ShowError("Opps!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao atualizar marca");
        }
    }
}