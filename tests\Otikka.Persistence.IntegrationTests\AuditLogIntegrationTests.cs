using Hangfire;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Otikka.Application.Contracts.Infrastructure;
using Otikka.Application.Contracts.Persistence;
using Otikka.Domain.Entities.Common;
using Otikka.Domain.Entities.EmpresaModule;
using Otikka.Domain.Enums;
using Otikka.Infrastructure.Services;
using Otikka.Persistence;
using Otikka.Persistence.Repositories;
using System.Text.Json;
using Xunit;

namespace Otikka.Persistence.IntegrationTests;

/// <summary>
/// Testes de integração para o sistema de auditoria
/// </summary>
public class AuditLogIntegrationTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly IAuditLogRepositorio _auditLogRepositorio;
    private readonly IAuditLogService _auditLogService;
    private readonly IServiceProvider _serviceProvider;

    public AuditLogIntegrationTests()
    {
        // Configurar serviços para teste
        var services = new ServiceCollection();
        
        // Registrar dependências necessárias
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddLogging();
        
        // Registrar mocks do Hangfire
        var mockBackgroundJobClient = new Mock<IBackgroundJobClient>();
        var mockRecurringJobManager = new Mock<IRecurringJobManager>();
        services.AddSingleton(mockBackgroundJobClient.Object);
        services.AddSingleton(mockRecurringJobManager.Object);
        
        // Registrar repositório e serviços
        services.AddScoped<IAuditLogRepositorio, AuditLogRepositorio>();
        services.AddScoped<IAuditLogService, AuditLogService>();
        
        // Configurar banco de dados em memória
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));
            
        services.AddDbContextFactory<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<ApplicationDbContext>();
        _auditLogRepositorio = _serviceProvider.GetRequiredService<IAuditLogRepositorio>();
        _auditLogService = _serviceProvider.GetRequiredService<IAuditLogService>();

        // Garantir que o banco foi criado
        _context.Database.EnsureCreated();
    }

    /// <summary>
    /// Testa a criação de um log de auditoria
    /// </summary>
    [Fact]
    public async Task DeveAdicionarLogAuditoria_ComSucesso()
    {
        // Arrange
        var auditLog = new AuditLog
        {
            Id = Guid.NewGuid(),
            NomeTabela = "Empresas",
            EntidadeId = Guid.NewGuid().ToString(),
            TipoOperacao = "INSERT",
            ValoresAntigos = null,
            ValoresNovos = JsonSerializer.Serialize(new { Nome = "Empresa Teste", Cnpj = "12345678000195" }),
            CamposModificados = "Nome,Cnpj",
            UsuarioId = Guid.NewGuid(),
            EmpresaId = Guid.NewGuid(),
            DataOperacao = DateTime.UtcNow,
            EnderecoIP = "***********",
            UserAgent = "Test Agent",
            Processado = false
        };

        // Act
        var resultado = await _auditLogRepositorio.AdicionarAsync(auditLog);

        // Assert
        Assert.True(resultado.IsSuccess);
        
        var logSalvo = await _auditLogRepositorio.ObterPorIdAsync(auditLog.Id);
        Assert.NotNull(logSalvo);
        Assert.Equal(auditLog.NomeTabela, logSalvo.NomeTabela);
        Assert.Equal(auditLog.TipoOperacao, logSalvo.TipoOperacao);
    }

    /// <summary>
    /// Testa a obtenção de logs falhados
    /// </summary>
    [Fact]
    public async Task DeveObterLogsFalhados_ComSucesso()
    {
        // Arrange
        var logFalhado = new AuditLog
        {
            Id = Guid.NewGuid(),
            NomeTabela = "Produtos",
            EntidadeId = Guid.NewGuid().ToString(),
            TipoOperacao = "UPDATE",
            ValoresAntigos = JsonSerializer.Serialize(new { Nome = "Produto Antigo" }),
            ValoresNovos = JsonSerializer.Serialize(new { Nome = "Produto Novo" }),
            CamposModificados = "Nome",
            UsuarioId = Guid.NewGuid(),
            EmpresaId = Guid.NewGuid(),
            DataOperacao = DateTime.UtcNow,
            Processado = false,
            TentativasProcessamento = 3,
            MensagemErro = "Erro de teste"
        };

        await _auditLogRepositorio.AdicionarAsync(logFalhado);

        // Act
        var resultado = await _auditLogRepositorio.ObterLogsFalhadosAsync(5); // Usar maxTentativas = 5 para incluir o log com 3 tentativas

        // Assert
        Assert.NotEmpty(resultado);
        Assert.Contains(resultado, log => log.Id == logFalhado.Id);
    }

    /// <summary>
    /// Testa a obtenção de estatísticas de auditoria
    /// </summary>
    [Fact]
    public async Task DeveObterEstatisticas_ComSucesso()
    {
        // Arrange
        var logs = new List<AuditLog>
        {
            new AuditLog
            {
                Id = Guid.NewGuid(),
                NomeTabela = "Clientes",
                EntidadeId = Guid.NewGuid().ToString(),
                TipoOperacao = "INSERT",
                UsuarioId = Guid.NewGuid(),
                EmpresaId = Guid.NewGuid(),
                DataOperacao = DateTime.UtcNow,
                Processado = true
            },
            new AuditLog
            {
                Id = Guid.NewGuid(),
                NomeTabela = "Clientes",
                EntidadeId = Guid.NewGuid().ToString(),
                TipoOperacao = "UPDATE",
                UsuarioId = Guid.NewGuid(),
                EmpresaId = Guid.NewGuid(),
                DataOperacao = DateTime.UtcNow,
                Processado = false
            }
        };

        foreach (var log in logs)
        {
            await _auditLogRepositorio.AdicionarAsync(log);
        }

        // Act
        var dataInicio = DateTimeOffset.UtcNow.AddDays(-1);
        var dataFim = DateTimeOffset.UtcNow.AddDays(1);
        var resultado = await _auditLogRepositorio.ObterEstatisticasAsync(dataInicio, dataFim);

        // Assert
        Assert.NotNull(resultado);
        Assert.True(resultado.TotalLogs >= 2);
    }

    /// <summary>
    /// Testa o processamento de log via serviço
    /// </summary>
    [Fact]
    public async Task DeveProcessarLog_ComSucesso()
    {
        // Arrange
        var logId = Guid.NewGuid();
        var auditLog = new AuditLog
        {
            Id = logId,
            NomeTabela = "OrdemServico",
            EntidadeId = Guid.NewGuid().ToString(),
            TipoOperacao = "DELETE",
            ValoresAntigos = JsonSerializer.Serialize(new { Status = "Ativo" }),
            ValoresNovos = JsonSerializer.Serialize(new { Status = "Excluido" }),
            CamposModificados = "Status",
            UsuarioId = Guid.NewGuid(),
            EmpresaId = Guid.NewGuid(),
            DataOperacao = DateTime.UtcNow,
            Processado = false
        };

        await _auditLogRepositorio.AdicionarAsync(auditLog);

        // Act
        var resultado = await _auditLogService.ProcessarLogAsync(logId);

        // Assert
        Assert.True(resultado.IsSuccess);
        
        var logProcessado = await _auditLogRepositorio.ObterPorIdAsync(logId);
        Assert.NotNull(logProcessado);
        Assert.True(logProcessado.Processado);
        Assert.NotNull(logProcessado.DataProcessamento);
    }

    /// <summary>
    /// Testa a verificação de logs não processados
    /// </summary>
    [Fact]
    public async Task DeveVerificarLogsNaoProcessados_ComSucesso()
    {
        // Arrange
        var logNaoProcessado = new AuditLog
        {
            Id = Guid.NewGuid(),
            NomeTabela = "Vendas",
            EntidadeId = Guid.NewGuid().ToString(),
            TipoOperacao = "INSERT",
            UsuarioId = Guid.NewGuid(),
            EmpresaId = Guid.NewGuid(),
            DataOperacao = DateTime.UtcNow,
            Processado = false
        };

        await _auditLogRepositorio.AdicionarAsync(logNaoProcessado);

        // Act
        var resultado = await _auditLogRepositorio.ExistemLogsNaoProcessadosAsync();

        // Assert
        Assert.True(resultado);
    }

    public void Dispose()
    {
        _context?.Dispose();
        _serviceProvider?.GetService<IServiceScope>()?.Dispose();
    }
}