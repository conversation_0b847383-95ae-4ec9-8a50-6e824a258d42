﻿using Otikka.Application.Models;
using Otikka.Domain.Entities.PessoaModulo;

namespace Otikka.Application.Contracts.Persistence;

public interface IReceitaRepositorio
{
    Task<List<Receita>> ObterTudo(Guid clienteId);
    Task<PaginatedList<Receita>> ObterTudo(Guid clienteId, PaginationParameters paginationParams);
    Task<PaginatedList<Receita>> ObterTodasReceitas(Guid empresaId, PaginationParameters paginationParams);
    Task<Receita?> Obter(Guid id);
    Task Cadastrar(Receita entity);
    Task Atualizar(Receita entity);
    Task Excluir(Guid id);
    Task Excluir(Receita entidade);
    Task<PaginatedList<Pessoa>> ObterClientesComReceitasVencidas(Guid empresaId, PaginationParameters paginationParams);
}