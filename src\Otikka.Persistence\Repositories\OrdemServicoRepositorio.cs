using Microsoft.EntityFrameworkCore;
using Otikka.Application.Contracts.Persistence;
using Otikka.Application.Models;
using Otikka.Application.Models.Requests;
using Otikka.Application.Models.Responses;
using Otikka.Domain.Common.ExtensionMethods;
using Otikka.Domain.Entities.Common;
using Otikka.Domain.Entities.OrdemServicoModulo;
using Otikka.Domain.Entities.PessoaModulo;
using Otikka.Domain.Entities.ProdutoModulo;
using Otikka.Domain.Entities.TransacaoFinanceiraModulo;
using Otikka.Domain.Enums;

namespace Otikka.Persistence.Repositories;

public class OrdemServicoRepositorio : IOrdemServicoRepositorio
{
    private readonly IDbContextFactory<ApplicationDbContext> _factory;

    public OrdemServicoRepositorio(IDbContextFactory<ApplicationDbContext> factory)
    {
        _factory = factory;
    }

    public async Task<string?> ObterUltimoNumeroOrdemServicoPorEmpresa(Guid empresaId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        return await _db.OrdensServicos.Where(a => a.EmpresaId == empresaId).MaxAsync(a => a.NumeroIdentificador);
    }



    public async Task<string> GerarNumeroIdentificador(Empresa empresa)
    {
        string numeroIdentificador = string.Empty;
        if (empresa.TipoNumeracaoOrdemServico != Domain.Enums.TipoAlgoritmoGeradorId.Manual)
        {
            if (empresa.TipoNumeracaoOrdemServico ==
                TipoAlgoritmoGeradorId.SequencialEmpresa)
            {
                string? identificadorMax = await ObterUltimoNumeroOrdemServicoPorEmpresa(empresa.Id);

                if (identificadorMax is null) return "1";

                int id;
                if (int.TryParse(identificadorMax, out id))
                    numeroIdentificador = (id + 1).ToString();
            }
        }

        return numeroIdentificador;
    }

    public async Task<PaginatedList<OrdemServico>> ObterTudo(Guid EmpresaId, OrdemServicoFiltroRequest filtro, PaginationParameters paginationParams)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.OrdensServicos.Where(a=>a.EmpresaId == EmpresaId).AsQueryable();



            if (!string.IsNullOrWhiteSpace(filtro.PalavraBusca))
                query = query.Where(a =>
                    (a.Cliente != null && a.Cliente.Nome != null && a.Cliente.Nome.Contains(filtro.PalavraBusca)) ||
                    (a.NumeroIdentificador != null && a.NumeroIdentificador.Contains(filtro.PalavraBusca)));

            if (filtro.DataEmissaoInicio.HasValue)
                query = query.Where(a =>
                    a.RegistroData >= filtro.DataEmissaoInicio.Value.InicioDia());

            if (filtro.DataEmissaoTermino.HasValue)
                query = query.Where(a =>
                    a.RegistroData <= filtro.DataEmissaoTermino.Value.FinalDia());

            if (filtro.DataPrevisaoInicio.HasValue)
                query = query.Where(a =>
                    a.PrevisaoEntregaData >= filtro.DataPrevisaoInicio.Value.InicioDia());

            if (filtro.DataPrevisaoTermino.HasValue)
                query = query.Where(a =>
                    a.PrevisaoEntregaData <= filtro.DataPrevisaoTermino.Value.FinalDia());

            if (filtro.VendedorId.HasValue)
                query = query.Where(a => a.VendedorId == filtro.VendedorId.Value);

            if (filtro.Situacao != null)
                query = query.Where(a => a.OrdemServicoUltimoStatus == filtro.Situacao);

            // Aplicar ordenação
            query = ApplySortOrdemServico(query, paginationParams.SortColumn, paginationParams.Ascending);

            var items = await query
                .Include(a => a.Cliente)
                .Include(a => a.ProdutoServicoVendidos!.Where(a => a.DataExclusao == null)).ThenInclude(a => a.Produto)
                .Include(a => a.Vendedor)
                .Include(a=>a.TransacaoFinanceira)
                .Skip((paginationParams.PageIndex - 1) * paginationParams.PageSize)
                .Take(paginationParams.PageSize)
                .Select(a => new OrdemServico()
                {
                    Id = a.Id,
                    Cliente = new Pessoa
                    {
                        Nome = a.Cliente!.Nome,
                        Documento = a.Cliente.Documento,
                        Telefone1 = a.Cliente.Telefone1,
                        Telefone2 = a.Cliente.Telefone2
                    },
                    NumeroIdentificador = a.NumeroIdentificador,
                    RegistroData = a.RegistroData,
                    PrevisaoEntregaData = a.PrevisaoEntregaData,
                    EntregaData = a.EntregaData,
                    OrdemServicoUltimoStatus = a.OrdemServicoUltimoStatus,

                    Observacao = a.Observacao,
                    ProdutoServicoVendidos = a.ProdutoServicoVendidos!
                        .Select(p => new ProdutoServicoVendido
                        {
                            Produto = new Produto() { Nome = p.Produto != null ? p.Produto.Nome : "Produto não encontrado" },
                            Quantidade = p.Quantidade,
                            PrecoVenda = p.PrecoVenda
                        }).ToList(),
                    TransacaoFinanceira = a.TransacaoFinanceira,
                    Vendedor = a.Vendedor
                })
                .ToListAsync();

            var count = await query.CountAsync();

            int totalPages = (int)Math.Ceiling((decimal)count / paginationParams.PageSize);

            return new PaginatedList<OrdemServico>(items, paginationParams.PageIndex, totalPages, paginationParams.SortColumn, paginationParams.Ascending);
        }
    }

    public async Task<OrdemServico?> Obter(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();

        return await _db.OrdensServicos
            .Include(a => a.Receita)
            .Include(a=>a.TransacaoFinanceira).ThenInclude(a=>a!.Pagamentos!.Where(a => a.DataExclusao == null)).ThenInclude(a => a.FormaPagamento)            
            .Include(a => a.ProdutoServicoVendidos!.Where(a => a.DataExclusao == null)).ThenInclude(a => a.Produto)
            .SingleOrDefaultAsync(a => a.Id == id);
    }


    public async Task<OrdemServico?> ObterSemTracking(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();

        return await _db.OrdensServicos
            .AsNoTracking()
            .Include(a => a.Receita)
            .Include(a=>a.TransacaoFinanceira).ThenInclude(a=>a!.Pagamentos!.Where(a => a.DataExclusao == null)).ThenInclude(a => a.FormaPagamento)            
            .Include(a => a.ProdutoServicoVendidos!.Where(a => a.DataExclusao == null)).ThenInclude(a => a.Produto)
            .SingleOrDefaultAsync(a => a.Id == id);
    }

    public async Task<OrdemServico?> ObterCompleto(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();

        return await _db.OrdensServicos
            .Include(a => a.Vendedor)
            .Include(a => a.Cliente)
            .Include(a => a.Receita)
            .Include(a => a.TransacaoFinanceira!.Pagamentos!.Where(a => a.DataExclusao == null)).ThenInclude(a => a.FormaPagamento)
            .Include(a => a.ProdutoServicoVendidos!.Where(a => a.DataExclusao == null)).ThenInclude(a => a.Produto)
            .SingleOrDefaultAsync(a => a.Id == id);
    }

    public async Task Cadastrar(OrdemServico entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();
        try
        {
            // Limpa o ChangeTracker para evitar conflitos
            _db.ChangeTracker.Clear();

            // Limpa navegações para evitar problemas de tracking
            LimparNavegacoes(entity);

            _db.OrdensServicos.Add(entity);
            await _db.SaveChangesAsync();

            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw; // Re-throw the exception to be handled by the calling code
        }
    }

    public async Task Atualizar(OrdemServico entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();
        try
        {
            var entidade = (OrdemServico)entity.Clone();
            if (entidade.Receita is not null)
            {
                entidade.Receita.ClienteId = entidade.ClienteId;
            }

            // Primeira etapa: Atualizar campos básicos (sem controle de estoque manual)
            await AtualizarCamposBasicos(_db, entity);

            // Segunda etapa: Atualizar produtos vendidos
            if (entity.ProdutoServicoVendidos != null)
            {
                await AtualizarProdutosVendidosSeparadamente(_db, entity);
            }

            // Terceira etapa: Atualizar transação financeira e pagamentos
            if (entity.TransacaoFinanceira != null)
            {
                await AtualizarTransacaoFinanceiraSeparadamente(_db, entity);
            }

            // Quarta etapa: Atualizar receita
            if (entity.Receita != null)
            {
                await AtualizarReceitaSeparadamente(_db, entity);
            }

            await _db.SaveChangesAsync();

            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task Excluir(Guid id)
    {
        var entity = await Obter(id);

        if (entity is not null)
            await Excluir(entity);
    }

    public async Task Excluir(OrdemServico entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();

        try
        {
            _db.OrdensServicos.Remove(entity);
            await _db.SaveChangesAsync();

            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// Atualiza campos básicos da ordem de serviço
    /// </summary>
    private static async Task AtualizarCamposBasicos(ApplicationDbContext db, OrdemServico entity)
    {
        // Limpa o ChangeTracker
        db.ChangeTracker.Clear();

        // Carrega apenas a entidade principal COM tracking
        var originalEntity = await db.OrdensServicos
            .SingleOrDefaultAsync(o => o.Id == entity.Id);

        if (originalEntity == null)
        {
            throw new InvalidOperationException("Ordem de serviço não encontrada.");
        }

        // Atualiza campos básicos da ordem de serviço
        originalEntity.DataAtualizacao = entity.DataAtualizacao;
        originalEntity.AtualizadoPorId = entity.AtualizadoPorId;
        originalEntity.ClienteId = entity.ClienteId;
        originalEntity.VendedorId = entity.VendedorId;
        originalEntity.NumeroIdentificador = entity.NumeroIdentificador;
        originalEntity.Observacao = entity.Observacao;
        originalEntity.RegistroData = entity.RegistroData;
        originalEntity.PrevisaoEntregaData = entity.PrevisaoEntregaData;
        originalEntity.EntregaData = entity.EntregaData;
        originalEntity.OrdemServicoUltimoStatus = entity.OrdemServicoUltimoStatus;
        originalEntity.OrdemServicoUltimoStatusObservacao = entity.OrdemServicoUltimoStatusObservacao;
        originalEntity.LaboratorioId = entity.LaboratorioId;
        originalEntity.LaboratorioDataEnvio = entity.LaboratorioDataEnvio;
        originalEntity.LaboratorioDataEntrega = entity.LaboratorioDataEntrega;
        originalEntity.LaboratorioObservacao = entity.LaboratorioObservacao;
        originalEntity.BaixaDataEntrega = entity.BaixaDataEntrega;
        originalEntity.BaixaObservacao = entity.BaixaObservacao;

        // Salva mudanças básicas
        await db.SaveChangesAsync();
    }

    /// <summary>
    /// Atualiza produtos vendidos separadamente
    /// </summary>
    private static async Task AtualizarProdutosVendidosSeparadamente(ApplicationDbContext db, OrdemServico entity)
    {
        var produtosAntigos = await db.ProdutoServicoVendidos
            .Where(p => p.TransacaoComercialId == entity.Id)
            .ToListAsync();

        var produtosAtuais = entity.ProdutoServicoVendidos ?? new List<ProdutoServicoVendido>();
        
        var produtosParaRemover = produtosAntigos
            .Where(antigo => !produtosAtuais.Any(novo => novo.Id == antigo.Id))
            .ToList();

        if (produtosParaRemover.Count > 0)
        {
            db.ProdutoServicoVendidos.RemoveRange(produtosParaRemover);
        }

        foreach (var psv in produtosAtuais)
        {
            var existingPsv = produtosAntigos.FirstOrDefault(p => p.Id == psv.Id);
            if (existingPsv != null)
            {
                db.Entry(existingPsv).CurrentValues.SetValues(psv);
            }
            else
            {
                psv.TransacaoComercialId = entity.Id;
                psv.Id = psv.Id == Guid.Empty ? Guid.NewGuid() : psv.Id;
                db.ProdutoServicoVendidos.Add(psv);
            }
        }
    }

    /// <summary>
    /// Atualiza transação financeira separadamente
    /// </summary>
    private static async Task AtualizarTransacaoFinanceiraSeparadamente(ApplicationDbContext db, OrdemServico entity)
    {
        if (entity.TransacaoFinanceira == null)
        {
            return;
        }

        var transacaoExistente = await db.TransacoesFinanceiras
            .Include(t => t.Pagamentos)
            .FirstOrDefaultAsync(t => t.TransacaoComercialId == entity.Id);

        if (transacaoExistente != null)
        {
            db.Entry(transacaoExistente).CurrentValues.SetValues(entity.TransacaoFinanceira);
                    
            if (entity.TransacaoFinanceira.Pagamentos != null)
            {
                var pagamentosAntigos = transacaoExistente.Pagamentos?.ToList() ?? new List<Pagamento>();
                var pagamentosAtuais = entity.TransacaoFinanceira.Pagamentos ?? new List<Pagamento>();
                
                var pagamentosParaRemover = pagamentosAntigos
                    .Where(antigo => !pagamentosAtuais.Any(novo => novo.Id == antigo.Id))
                    .ToList();
                
                if (pagamentosParaRemover.Any())
                {
                    db.Pagamentos.RemoveRange(pagamentosParaRemover);
                }

                foreach (var pag in pagamentosAtuais)
                {
                    var existingPag = pagamentosAntigos.FirstOrDefault(p => p.Id == pag.Id);
                    if (existingPag != null)
                    {
                        db.Entry(existingPag).CurrentValues.SetValues(pag);
                    }
                    else
                    {
                        pag.TransacaoFinanceiraId = transacaoExistente.Id;
                        pag.Id = pag.Id == Guid.Empty ? Guid.NewGuid() : pag.Id;
                        db.Pagamentos.Add(pag);
                    }
                }
            }
        }
        else
        {
            entity.TransacaoFinanceira.TransacaoComercialId = entity.Id;
            entity.TransacaoFinanceira.TipoTransacaoComercial = TipoTransacaoComercial.OrdemServico;
            db.TransacoesFinanceiras.Add(entity.TransacaoFinanceira);
        }
    }

    /// <summary>
    /// Limpa navegações da entidade para evitar problemas de tracking
    /// </summary>
    private static void LimparNavegacoes(OrdemServico entity)
    {
        entity.Cliente = null!;
        entity.Vendedor = null!;
        
        if (entity.ProdutoServicoVendidos != null)
        {
            foreach (var psv in entity.ProdutoServicoVendidos)
            {
                psv.Produto = null;
            }
        }

        if (entity.TransacaoFinanceira?.Pagamentos != null)
        {
            foreach (var pag in entity.TransacaoFinanceira.Pagamentos)
            {
                pag.FormaPagamento = null;
            }
        }
    }
    
    /// <summary>
    /// Atualiza a receita separadamente
    /// </summary>
    private static async Task AtualizarReceitaSeparadamente(ApplicationDbContext db, OrdemServico entity)
    {
        var receitaExistente = await db.Receitas
            .FirstOrDefaultAsync(r => r.OrdemServicoId == entity.Id);

        // Caso 1: A receita foi removida na edição
        if (entity.Receita == null && receitaExistente != null)
        {
            db.Receitas.Remove(receitaExistente);
        }
        // Caso 2: A receita foi adicionada ou atualizada
        else if (entity.Receita != null)
        {
            if (receitaExistente != null)
            {
                // Atualiza a receita existente
                db.Entry(receitaExistente).CurrentValues.SetValues(entity.Receita);
            }
            else
            {
                // Adiciona uma nova receita
                entity.Receita.OrdemServicoId = entity.Id;
                db.Receitas.Add(entity.Receita);
            }
        }
    }

    // Métodos para relatórios
    public async Task<int> ObterQuantidadeTotal(Guid empresaId, RelatorioFiltroRequest filtro)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        
        var query = _db.OrdensServicos.Where(a => a.EmpresaId == empresaId).AsQueryable();
        
        query = AplicarFiltrosRelatorio(query, filtro);
        
        return await query.CountAsync();
    }

    public async Task<int> ObterQuantidadePorSituacao(Guid empresaId, TipoOrdemServicoSituacao situacao, RelatorioFiltroRequest filtro)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        
        var query = _db.OrdensServicos
            .Where(a => a.EmpresaId == empresaId && a.OrdemServicoUltimoStatus == situacao)
            .AsQueryable();
        
        query = AplicarFiltrosRelatorio(query, filtro);
        
        return await query.CountAsync();
    }

    public async Task<decimal> ObterValorTotal(Guid empresaId, RelatorioFiltroRequest filtro)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        
        var query = _db.OrdensServicos
            .Where(a => a.EmpresaId == empresaId)
            .AsQueryable();
        
        query = AplicarFiltrosRelatorio(query, filtro);
        
        return await query
            .Include(a => a.TransacaoFinanceira)
            .Where(a => a.TransacaoFinanceira != null)
            .SumAsync(a => a.TransacaoFinanceira!.ValorTotal ?? 0);
    }

    private static IQueryable<OrdemServico> AplicarFiltrosRelatorio(IQueryable<OrdemServico> query, RelatorioFiltroRequest filtro)
    {
        if (filtro.VendedorId.HasValue && filtro.VendedorId != Guid.Empty)
            query = query.Where(a => a.VendedorId == filtro.VendedorId);

        if (filtro.DataInicio.HasValue)
            query = query.Where(a => a.RegistroData >= filtro.DataInicio.Value.InicioDia());

        if (filtro.DataTermino.HasValue)
            query = query.Where(a => a.RegistroData <= filtro.DataTermino.Value.FinalDia());

        return query;
    }

    private IQueryable<OrdemServico> ApplySortOrdemServico(IQueryable<OrdemServico> query, string sortColumn, bool ascending)
    {
        if (string.IsNullOrWhiteSpace(sortColumn))
        {
            return query.OrderByDescending(a => a.DataCriacao);
        }

        return sortColumn.ToLower() switch
        {
            "numeroidentificador" => ascending ? query.OrderBy(a => a.NumeroIdentificador) : query.OrderByDescending(a => a.NumeroIdentificador),
            "registrodata" => ascending ? query.OrderBy(a => a.RegistroData) : query.OrderByDescending(a => a.RegistroData),
            "previsaoentregadata" => ascending ? query.OrderBy(a => a.PrevisaoEntregaData) : query.OrderByDescending(a => a.PrevisaoEntregaData),
            "entregadata" => ascending ? query.OrderBy(a => a.EntregaData) : query.OrderByDescending(a => a.EntregaData),
            "datacriacao" => ascending ? query.OrderBy(a => a.DataCriacao) : query.OrderByDescending(a => a.DataCriacao),
            "cliente" => ascending ? query.OrderBy(a => a.Cliente!.Nome) : query.OrderByDescending(a => a.Cliente!.Nome),
            "vendedor" => ascending ? query.OrderBy(a => a.Vendedor!.Nome) : query.OrderByDescending(a => a.Vendedor!.Nome),
            "status" => ascending ? query.OrderBy(a => a.OrdemServicoUltimoStatus) : query.OrderByDescending(a => a.OrdemServicoUltimoStatus),
            _ => query.OrderByDescending(a => a.DataCriacao)
        };
    }

    public async Task<List<OrdemServicoRelatorioItem>> ObterParaRelatorioOSVendas(Guid empresaId, RelatorioOSVendasFiltroRequest filtro)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();

        var query = _db.OrdensServicos
            .Where(a => a.EmpresaId == empresaId)
            .Include(a => a.Cliente)
            .Include(a => a.Vendedor)
            .Include(a => a.TransacaoFinanceira)
            .AsQueryable();

        // Aplicar filtros específicos do relatório
        if (filtro.DataEmissaoInicio.HasValue)
            query = query.Where(a => a.RegistroData >= filtro.DataEmissaoInicio.Value.InicioDia());

        if (filtro.DataEmissaoFim.HasValue)
            query = query.Where(a => a.RegistroData <= filtro.DataEmissaoFim.Value.FinalDia());

        if (filtro.DataPrevisaoInicio.HasValue)
            query = query.Where(a => a.PrevisaoEntregaData >= filtro.DataPrevisaoInicio.Value.InicioDia());

        if (filtro.DataPrevisaoFim.HasValue)
            query = query.Where(a => a.PrevisaoEntregaData <= filtro.DataPrevisaoFim.Value.FinalDia());

        if (filtro.VendedorId.HasValue && filtro.VendedorId != Guid.Empty)
            query = query.Where(a => a.VendedorId == filtro.VendedorId);

        return await query
            .Select(a => new OrdemServicoRelatorioItem
            {
                Id = a.Id,
                NumeroIdentificador = a.NumeroIdentificador,
                RegistroData = a.RegistroData,
                PrevisaoEntregaData = a.PrevisaoEntregaData,
                ClienteNome = a.Cliente != null ? a.Cliente.Nome : "Cliente não informado",
                VendedorNome = a.Vendedor != null ? a.Vendedor.Nome : "Vendedor não informado",
                ValorTotal = a.TransacaoFinanceira != null ? a.TransacaoFinanceira.ValorTotal ?? 0 : 0,
                ValorPago = a.TransacaoFinanceira != null ? a.TransacaoFinanceira.ValorPago ?? 0 : 0,
                ValorAPagar = a.TransacaoFinanceira != null ? a.TransacaoFinanceira.ValorRestante ?? 0 : 0,
                Status = a.OrdemServicoUltimoStatus.ToString()
            })
            .OrderByDescending(a => a.RegistroData)
            .ToListAsync();
    }
}