﻿using Blazored.Modal;
using Blazored.Modal.Services;
using Otikka.Application.Constants;
using Otikka.Application.Contracts.Infrastructure;
using Otikka.Domain.Entities.EmpresaModule;
using Otikka.Domain.Entities.UsuarioModule;
using System.Xml.Linq;
using Wolverine;

namespace Otikka.App.Components.Pages;

public class PageBase : ComponentBase
{
    [CascadingParameter] public BlazoredModalInstance BlazoredModal { get; set; } = default!;
    [CascadingParameter] public IModalService Modal { get; set; } = default!;
    [Inject] protected IConfiguration Configuration { get; set; } = default!;
    [Inject] protected IJSRuntime JsRuntime { get; set; } = default!;
    [Inject] protected IMessageBus MessageBus { get; set; } = default!;
    [Inject] protected NavigationManager NavigationManager { get; set; } = default!;
    [Inject] protected ILocalStorageService LocalStorage { get; set; } = default!;
    [Inject] protected IToastService ToastService { get; set; } = default!;
    [Inject] protected IAlertService AlertService { get; set; } = default!;

    protected bool Processing { get; set; } = false;
    protected override async Task OnInitializedAsync()
    {
        var uri = NavigationManager.Uri.ToLower();
        if (uri.Contains(Application.Constants.Application.Routes.Dashboard))
        {
            var userExists = await LocalStorage.ContainKeyAsync(Application.Constants.Application.Storage.User);
            if (!userExists)
            {
                NavigationManager.NavigateTo(Application.Constants.Application.Routes.Login, forceLoad: true);
                return;
            }

            var companyExists = await LocalStorage.ContainKeyAsync(Application.Constants.Application.Storage.Company);
            if (!companyExists && !uri.EndsWith(Application.Constants.Application.Routes.EmpresaSelecao.ToLower()) && !uri.EndsWith(Application.Constants.Application.Routes.EmpresaCadastrar.ToLower()))
            {
                NavigationManager.NavigateTo(Application.Constants.Application.Routes.EmpresaSelecao, forceLoad: true);
                return;
            }
        }
        await base.OnInitializedAsync();
    }

    protected async Task<Guid> GetUsuarioIdLoggedAsync()
    {
        var user = await LocalStorage.GetItemAsync<Usuario>(Application.Constants.Application.Storage.User);
        return user!.Id;
    }
    protected async Task<Guid> GetEmpresaIdAsync()
    {
        var empresa = await LocalStorage.GetItemAsync<Empresa>(Application.Constants.Application.Storage.Company);
        return empresa?.Id ?? Guid.Empty;
    }

    protected async Task<Empresa> GetEmpresaAsync()
    {
        var empresa = await LocalStorage.GetItemAsync<Empresa>(Application.Constants.Application.Storage.Company);
        return empresa!;
    }
    protected async Task<Usuario> GetUsuarioAsync()
    {
        var usuario = await LocalStorage.GetItemAsync<Usuario>(Application.Constants.Application.Storage.User);
        return usuario!;
    }

    protected async Task ProcessingChange(bool processing)
    {
        Processing = processing;
        StateHasChanged();
        await Task.Yield();
    }

    #region Messages
    protected string? SuccessMessage { get; set; }
    protected string? ErrorMessage { get; set; }
    protected string? WarningMessage { get; set; }

    protected void ClearMessages()
    {
        SuccessMessage = null;
        ErrorMessage = null;
        WarningMessage = null;
    }
    #endregion

    #region Max Upload Size

    protected string ObterTamanhoMaximoUploadComoString()
    {
        var maxRequestBodySizeBytes = ObterTamanhoMaximoUploadComoLong();
        double maxRequestBodySizeMB = maxRequestBodySizeBytes / (1024.0 * 1024.0);
        return $"{maxRequestBodySizeMB.ToString("F2")}MB";
    }
    public long ObterTamanhoMaximoUploadComoLong()
    {
        bool isIIS = Environment.GetEnvironmentVariable("ASPNETCORE_IIS_HTTPAUTH") != null;

        if (isIIS)
        {
            return ObterMaxUploadDoIIS();
        }
        else
        {
            return ObterMaxUploadDoKestrel();
        }
    }

    private long ObterMaxUploadDoIIS()
    {
        string webConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "web.config");

        if (File.Exists(webConfigPath))
        {
            XDocument webConfig = XDocument.Load(webConfigPath);

            var maxAllowedContentLength = webConfig
                .Descendants("requestLimits")
                .Attributes("maxAllowedContentLength")
                .FirstOrDefault();

            if (maxAllowedContentLength != null)
            {
                return long.Parse(maxAllowedContentLength.Value);
            }
        }

        // Retorna um valor padrão se não for encontrado
        return 30000000; // Por exemplo, 30 MB
    }

    private long ObterMaxUploadDoKestrel()
    {
        return Configuration.GetValue<long>("Kestrel:Limits:MaxRequestBodySize", 30000000); // Default 30 MB
    }
    #endregion
}
