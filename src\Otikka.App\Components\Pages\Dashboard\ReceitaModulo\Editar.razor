@attribute [Route(Application.Routes.ReceitaEditar)]
@using Otikka.Application.Features.Pessoa.Commands.UpdateReceita
@using Otikka.Application.Features.Pessoa.Queries.GetReceita
@using Otikka.Domain.Entities.PessoaModulo
@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Receita.Componentes
@using Otikka.App.Components.Pages.Dashboard.ReceitaModulo.Componentes
@using FluentResults
@using Wolverine
@using Otikka.Application.Contracts.Infrastructure
@inherits PageBase
@inject ILogger<Editar> Logger
@inject IFileStorageService FileStorageService

<PageTitle>Editar Receita - Otikka</PageTitle>

@if (carregando)
{
    <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="text-muted mt-2">Carregando dados da receita...</p>
        </div>
    </div>
}
else if (Receita == null)
{
    <div class="text-center py-5">
        <div class="avatar-lg mx-auto mb-4">
            <div class="avatar-title bg-danger bg-opacity-10 text-danger rounded-circle">
                <i class="ti ti-alert-circle fs-24"></i>
            </div>
        </div>
        <h4 class="text-muted">Receita não encontrada</h4>
        <p class="text-muted mb-4">A receita solicitada não foi encontrada ou você não tem permissão para acessá-la.</p>
        <a href="@Application.Routes.ReceitaListar" class="btn btn-primary">
            <i class="ti ti-arrow-left me-1"></i> Voltar para Lista
        </a>
    </div>
}
else
{
    <div class="row" id="receita-container">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Editar Receita</h4>
                </div>
                <EditForm Model="Receita" OnValidSubmit="Submit" FormName="EditarReceita">
                    <FluentValidationValidator />
                    <div class="card-body">
                        <div class="row gy-4">
                            <!-- Seleção de Cliente -->
                            <ClienteSelectorForm @bind-ClienteId="Receita.ClienteId" OnClienteSelected="OnClienteSelected" />
                            
                            @if (ClienteSelecionado != null)
                            {
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <i class="ti ti-info-circle me-2"></i>
                                        <strong>Cliente selecionado:</strong> @ClienteSelecionado.Nome
                                        @if (!string.IsNullOrEmpty(ClienteSelecionado.Email))
                                        {
                                            <span class="text-muted"> - @ClienteSelecionado.Email</span>
                                        }
                                    </div>
                                </div>
                            }
                        </div>

                        <!-- Campos da Receita -->
                        <div class="mt-4">
                            <h5 class="card-title mb-3">Dados da Receita</h5>
                            <ReceitaCamposForm Receita="ReceitaOriginal" OnFileSelected="OnFileSelected" ShowUpload="@false" />
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="@Application.Routes.ReceitaListar" class="btn btn-light">
                                        <i class="ti ti-arrow-left me-1"></i> Voltar
                                    </a>
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-secondary" @onclick="ResetarFormulario">
                                            <i class="ti ti-refresh me-1"></i> Resetar
                                        </button>
                                        <SubmitButton IsProcessing="Processing" ButtonClass="btn-primary">
                                            <i class="ti ti-save me-1"></i> Atualizar Receita
                                        </SubmitButton>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public Guid Id { get; set; }

    private UpdateReceita? Receita;
    private Receita? ReceitaOriginal;
    private Pessoa? ClienteSelecionado;
    private IBrowserFile? selectedFile;
    private bool carregando = true;

    protected override async Task OnInitializedAsync()
    {
        await CarregarReceita();
    }

    private async Task CarregarReceita()
    {
        try
        {
            carregando = true;
            StateHasChanged();

            var empresaId = await GetEmpresaIdAsync();
            var query = new GetReceita
            {
                Id = Id,
                EmpresaId = empresaId
            };

            var result = await MessageBus.InvokeAsync<Result<Receita>>(query);

            if (result.IsSuccess)
            {
                ReceitaOriginal = result.Value;
                ClienteSelecionado = ReceitaOriginal.Cliente;

                // Mapear para o comando de atualização
                Receita = new UpdateReceita
                {
                    Id = ReceitaOriginal.Id,
                    ClienteId = ReceitaOriginal.ClienteId,
                    EmpresaId = ReceitaOriginal.EmpresaId,
                    NomeProfissional = ReceitaOriginal.NomeProfissional,
                    DataValidade = ReceitaOriginal.DataValidade,
                    ImagemReceitaUrl = ReceitaOriginal.ImagemReceitaUrl,
                    Adicao = ReceitaOriginal.Adicao,
                    
                    // Olho Direito - Longe
                    EsfericoOlhoDireitoLonge = ReceitaOriginal.EsfericoOlhoDireitoLonge,
                    CilindricoOlhoDireitoLonge = ReceitaOriginal.CilindricoOlhoDireitoLonge,
                    EixoOlhoDireitoLonge = ReceitaOriginal.EixoOlhoDireitoLonge,
                    AlturaOlhoDireitoLonge = ReceitaOriginal.AlturaOlhoDireitoLonge,
                    DNPOlhoDireitoLonge = ReceitaOriginal.DNPOlhoDireitoLonge,
                    
                    // Olho Direito - Perto
                    EsfericoOlhoDireitoPerto = ReceitaOriginal.EsfericoOlhoDireitoPerto,
                    CilindricoOlhoDireitoPerto = ReceitaOriginal.CilindricoOlhoDireitoPerto,
                    EixoOlhoDireitoPerto = ReceitaOriginal.EixoOlhoDireitoPerto,
                    AlturaOlhoDireitoPerto = ReceitaOriginal.AlturaOlhoDireitoPerto,
                    DNPOlhoDireitoPerto = ReceitaOriginal.DNPOlhoDireitoPerto,
                    
                    // Olho Esquerdo - Longe
                    EsfericoOlhoEsquerdoLonge = ReceitaOriginal.EsfericoOlhoEsquerdoLonge,
                    CilindricoOlhoEsquerdoLonge = ReceitaOriginal.CilindricoOlhoEsquerdoLonge,
                    EixoOlhoEsquerdoLonge = ReceitaOriginal.EixoOlhoEsquerdoLonge,
                    AlturaOlhoEsquerdoLonge = ReceitaOriginal.AlturaOlhoEsquerdoLonge,
                    DNPOlhoEsquerdoLonge = ReceitaOriginal.DNPOlhoEsquerdoLonge,
                    
                    // Olho Esquerdo - Perto
                    EsfericoOlhoEsquerdoPerto = ReceitaOriginal.EsfericoOlhoEsquerdoPerto,
                    CilindricoOlhoEsquerdoPerto = ReceitaOriginal.CilindricoOlhoEsquerdoPerto,
                    EixoOlhoEsquerdoPerto = ReceitaOriginal.EixoOlhoEsquerdoPerto,
                    AlturaOlhoEsquerdoPerto = ReceitaOriginal.AlturaOlhoEsquerdoPerto,
                    DNPOlhoEsquerdoPerto = ReceitaOriginal.DNPOlhoEsquerdoPerto
                };

                Logger.LogInformation("Receita carregada com sucesso: {ReceitaId}", Id);
            }
            else
            {
                var errorMessage = string.Join("; ", result.Errors.Select(e => e.Message));
                Logger.LogWarning("Erro ao carregar receita {ReceitaId}: {Errors}", Id, errorMessage);
                await AlertService.ShowAlert("Erro", errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao carregar receita {ReceitaId}: {Message}", Id, ex.Message);
            await AlertService.ShowAlert("Erro", "Erro ao carregar dados da receita");
        }
        finally
        {
            carregando = false;
            StateHasChanged();
        }
    }

    private async Task Submit()
    {
        if (Receita == null) return;

        try
        {
            await ProcessingChange(true);

            if (Receita.ClienteId == Guid.Empty)
            {
                await AlertService.ShowAlert("Atenção", "Por favor, selecione um cliente para a receita.");
                return;
            }

            Logger.LogInformation("Iniciando atualização de receita: {ReceitaId}", Receita.Id);

            // Upload da nova imagem se selecionada
            if (selectedFile != null)
            {
                await UploadImagemReceita();
            }

            var result = await MessageBus.InvokeAsync<Result>(Receita);

            if (result.IsSuccess)
            {
                Logger.LogInformation("Receita atualizada com sucesso: {ReceitaId}", Receita.Id);
                
                await AlertService.ShowAlert("Sucesso", "Receita atualizada com sucesso!");
                NavigationManager.NavigateTo(Application.Routes.ReceitaListar);
            }
            else
            {
                var errorMessage = string.Join("; ", result.Errors.Select(e => e.Message));
                Logger.LogWarning("Falha ao atualizar receita {ReceitaId}: {Errors}", Receita.Id, errorMessage);
                await AlertService.ShowAlert("Erro", errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao atualizar receita {ReceitaId}: {Message}", Receita?.Id, ex.Message);
            await AlertService.ShowAlert("Erro", "Erro interno do sistema. Tente novamente.");
        }
        finally
        {
            await ProcessingChange(false);
        }
    }

    private async Task OnFileSelected(IBrowserFile file)
    {
        selectedFile = file;
        Logger.LogInformation("Arquivo selecionado para upload: {FileName}", file.Name);
    }

    private async Task UploadImagemReceita()
    {
        if (selectedFile == null || Receita == null) return;

        try
        {
            Logger.LogInformation("Iniciando upload da nova imagem da receita: {FileName}", selectedFile.Name);

            using var stream = selectedFile.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024); // 5MB
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);

            var fileName = $"receita-{Receita.Id}-{selectedFile.Name}";
            var contentType = selectedFile.ContentType;

            var bucketName = "otikka-receitas";
            var objectName = $"receitas/imagens/{fileName}";

            memoryStream.Position = 0; // Reset stream position
            var uploadResult = await FileStorageService.UploadFileAsync(
                bucketName,
                objectName,
                memoryStream,
                contentType);

            if (uploadResult.IsSuccess)
            {
                Receita.ImagemReceitaUrl = uploadResult.Value;
                Logger.LogInformation("Upload da nova imagem concluído com sucesso: {Url}", Receita.ImagemReceitaUrl);
            }
            else
            {
                Logger.LogError("Erro no upload da nova imagem: {Errors}", string.Join("; ", uploadResult.Errors.Select(e => e.Message)));
                await AlertService.ShowAlert("Atenção", "Erro ao fazer upload da nova imagem. A receita será salva com a imagem anterior.");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro no upload da nova imagem da receita");
            await AlertService.ShowAlert("Atenção", "Erro ao fazer upload da nova imagem. A receita será salva com a imagem anterior.");
        }
    }

    private void OnClienteSelected(Pessoa cliente)
    {
        ClienteSelecionado = cliente;
        Logger.LogInformation("Cliente alterado: {ClienteId} - {Nome}", cliente.Id, cliente.Nome);
        StateHasChanged();
    }

    private async Task ResetarFormulario()
    {
        var confirmacao = await AlertService.ShowConfirm(
            "Confirmar",
            "Tem certeza que deseja resetar o formulário para os valores originais?",
            "Sim, Resetar",
            "Cancelar");

        if (confirmacao)
        {
            await CarregarReceita();
        }
    }
}
