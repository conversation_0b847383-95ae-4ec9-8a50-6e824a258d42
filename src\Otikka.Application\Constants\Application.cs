namespace Otikka.Application.Constants
{
    public class Application
    {
        public class Config
        {
            public const string Name = "Otikka";
        }

        public class Storage
        {
            public const string Company = "company";
            public const string User = "user";
        }

        public static class Routes
        {
            public const string Home = "/";
            #region Dashboard
            public const string Dashboard = "/dashboard";
            #endregion

            #region Auth
            public const string Login = "/Auth/Login";
            public const string Logout = "/Auth/Logout";
            public const string Cadastrar = "/Auth/Cadastrar";
            public const string RecuperarSenha = "/Auth/RecuperarSenha";
            public const string CriarSenha = "/Auth/CriarSenha";
            #endregion
            #region Ordem de Servico
            public const string OrdemServicoListar = "/Dashboard/OrdemServico/Listar";
            public const string OrdemServicoEditar = "/Dashboard/OrdemServico/Editar/{id:guid}";
            public const string OrdemServicoCadastrar = "/Dashboard/OrdemServico/Cadastrar";
            public const string OrdemServicoDuplicar = "/Dashboard/OrdemServico/Duplicar/{id:guid}";
            public const string OrdemServicoVisualizar = "/Dashboard/OrdemServico/Visualizar/{id:guid}";

            #endregion

            #region ClienteModulo (Cliente e Receita)

            public const string ClienteListar = "/Dashboard/Cliente/Listar";
            public const string ClienteEditar = "/Dashboard/Cliente/Editar/{id:guid}";
            public const string ClienteCadastrar = "/Dashboard/Cliente/Cadastrar";

            #region Receitas do Cliente

            public const string ReceitaDoClienteListar = "/Dashboard/Cliente/{clienteId:guid}/Receita/Listar";
            public const string ReceitaDoClienteEditar = "/Dashboard/Cliente/{clienteId:guid}/Receita/Editar/{id:guid}";
            public const string ReceitaDoClienteCadastrar = "/Dashboard/Cliente/{ClienteId:guid}/Receita/Cadastrar";

            #endregion
            #region Receitas do Cliente

            public const string ReceitaListar = "/Dashboard/Receita/Listar";
            public const string ReceitaEditar = "/Dashboard/Receita/Editar/{id:guid}";
            public const string ReceitaCadastrar = "/Dashboard/Receita/Cadastrar";

            #endregion

            #region Ordem de Servico do Cliente

            public const string OrdemServicoVendasListar =
                "/Dashboard/Cliente/{clienteId:guid}/OrdemServicoVendas/Listar";

            #endregion

            #endregion

            #region Colaborador

            public const string ColaboradorListar = "/Dashboard/Colaborador/Listar";
            public const string ColaboradorEditar = "/Dashboard/Colaborador/Editar/{id:guid}";
            public const string ColaboradorCadastrar = "/Dashboard/Colaborador/Cadastrar";




            #endregion

            #region Empresa

            public const string EmpresaSelecao = "/Dashboard/Empresa/Selecao";
            public const string EmpresaListar = "/Dashboard/Empresa/Listar";
            public const string EmpresaEditar = "/Dashboard/Empresa/Editar/{id:guid}";
            public const string EmpresaCadastrar = "/Dashboard/Empresa/Cadastrar";
            public const string PlanoEscolher = "/Dashboard/Plano/Escolher";

            #endregion

            #region Financeiro

            public const string FinanceiroCategoriaListar = "/Dashboard/FinanceiroModulo/Categoria/Listar";
            public const string FinanceiroCategoriaEditar = "/Dashboard/FinanceiroModulo/Categoria/Editar/{id:guid}";
            public const string FinanceiroCategoriaCadastrar = "/Dashboard/FinanceiroModulo/Categoria/Cadastrar";

            public const string TransacaoFinanceiraListar =
                "/Dashboard/FinanceiroModulo/TransacaoFinanceira/{Type}/Listar";

            public const string TransacaoFinanceiraEditar =
                "/Dashboard/FinanceiroModulo/TransacaoFinanceira/{Type}/Editar/{id:guid}";

            public const string TransacaoFinanceiraCadastrar =
                "/Dashboard/FinanceiroModulo/TransacaoFinanceira/{Type}/Cadastrar";


            #endregion

            #region FormaPagamento

            public const string FormaPagamentoListar = "/Dashboard/FormaPagamento/Listar";
            public const string FormaPagamentoEditar = "/Dashboard/FormaPagamento/Editar/{id:guid}";
            public const string FormaPagamentoCadastrar = "/Dashboard/FormaPagamento/Cadastrar";

            #endregion

            #region Fornecedor

            public const string FornecedorListar = "/Dashboard/Fornecedor/Listar";
            public const string FornecedorEditar = "/Dashboard/Fornecedor/Editar/{id:guid}";
            public const string FornecedorCadastrar = "/Dashboard/Fornecedor/Cadastrar";

            #endregion



            #region ProdutoModulo

            public const string CategoriaProdutoListar = "/Dashboard/ProdutoModulo/CategoriaProduto/Listar";
            public const string CategoriaProdutoEditar = "/Dashboard/ProdutoModulo/CategoriaProduto/Editar/{id:guid}";
            public const string CategoriaProdutoCadastrar = "/Dashboard/ProdutoModulo/GrupoProduto/Cadastrar";

            public const string MarcaListar = "/Dashboard/ProdutoModulo/MarcaProduto/Listar";
            public const string MarcaEditar = "/Dashboard/ProdutoModulo/MarcaProduto/Editar/{id:guid}";
            public const string MarcaCadastrar = "/Dashboard/ProdutoModulo/MarcaProduto/Cadastrar";

            public const string ProdutoListar = "/Dashboard/ProdutoModulo/Produto/Listar";
            public const string ProdutoEditar = "/Dashboard/ProdutoModulo/Produto/Editar/{id:guid}";
            public const string ProdutoCadastrar = "/Dashboard/ProdutoModulo/Produto/Cadastrar";

            public const string ProdutoEstoqueListar = "/Dashboard/ProdutoModulo/Produto/Estoque/{produtoId:guid}/Listar";
            public const string ProdutoEstoqueEditar = "/Dashboard/ProdutoModulo/Produto/Estoque/{produtoId:guid}/Editar/{id:guid}";
            public const string ProdutoEstoqueCadastrar = "/Dashboard/ProdutoModulo/Produto/Estoque/{produtoId:guid}/pCadastrar";

            #endregion

            #region Servico

            public const string ServicoListar = "/Dashboard/Servico/Listar";
            public const string ServicoEditar = "/Dashboard/Servico/Editar/{id:guid}";
            public const string ServicoCadastrar = "/Dashboard/Servico/Cadastrar";

            #endregion

            #region Venda

            public const string VendaListar = "/Dashboard/Venda/Listar";
            public const string VendaEditar = "/Dashboard/Venda/Editar/{id:guid}";
            public const string VendaCadastrar = "/Dashboard/Venda/Cadastrar";

            #endregion

            #region Compra

            public const string CompraListar = "/Dashboard/Compra/Listar";
            public const string CompraEditar = "/Dashboard/Compra/Editar/{id:guid}";
            public const string CompraCadastrar = "/Dashboard/Compra/Cadastrar";

            #endregion

            #region MovimentacaoEstoque

            public const string MovimentacaoEstoqueListar = "/Dashboard/MovimentacaoEstoque/Listar";
            public const string MovimentacaoEstoqueEditar = "/Dashboard/MovimentacaoEstoque/Editar/{id:guid}";
            public const string MovimentacaoEstoqueCadastrar = "/Dashboard/MovimentacaoEstoque/Cadastrar";
            public const string MovimentacaoEstoqueVisualizar = "/Dashboard/MovimentacaoEstoque/Visualizar/{id:guid}";

            #endregion

            #region Relatório

            public const string RelatorioGeral = "/Dashboard/Relatorio/Geral";
            public const string RelatorioReceitasVencidas = "/Dashboard/Relatorio/ReceitasVencidas";
            public const string RelatorioOSVendas = "/Dashboard/Relatorio/OSVendas";

            #endregion

            #region Comunicado


            public const string ComunicadoListar = "/Dashboard/Comunicado/Listar";
            public const string ComunicadoCadastrar = "/Dashboard/Comunicado/Cadastrar";
            public const string ComunicadoEditar = "/Dashboard/Comunicado/Editar/{id:guid}";

            #endregion



            #region Suporte
            public const string Suporte = "/Dashboard/Suporte";
            #endregion

            #region Perfil de Usuário
            public const string PerfilUsuario = "/Dashboard/Perfil";
            #endregion

            #region Páginas Legais
            public const string TermosUso = "/termos-de-uso";
            public const string PoliticaPrivacidade = "/politica-de-privacidade";
            #endregion
            public static string GerarRota(string rota, params string[] parametros)
            {
                foreach (var parametro in parametros)
                {
                    int inicio = rota.IndexOf('{');
                    int fim = rota.IndexOf('}');

                    if (inicio >= 0 && fim > inicio)
                    {
                        rota = rota.Substring(0, inicio) + parametro + rota.Substring(fim + 1);
                    }
                }

                return rota;
            }
        }

        public static class Messages
        {
            public const string MSG_DEL_TITULO = "Confirmar exclusão";
            public const string MSG_DEL_CONFIRMACAO = "Você tem certeza que deseja excluir?";

            public const string SucessoSalvar = "Registro salvo com sucesso!";
            public const string SucessoSalvar_EMPRESA = "Sua empresa foi cadastrada, agora comece a cadastrar suas lojas e depósitos!";

            // Mensagens do MessagingCenter
            public const string PerfilAtualizado = "perfil_atualizado";
        }
        public class InitialSQL
        {
            public const string SyncItensCompraWithMovimentacaoEstoque = @"
                CREATE OR REPLACE FUNCTION fn_sync_movimentacao_on_insert_item_compra()
                RETURNS TRIGGER AS $$
                DECLARE
                    v_produto_controle_estoque BOOLEAN;
                    v_empresa_id UUID;
                BEGIN
                    -- Obter EmpresaId da compra
                    SELECT c.""EmpresaId"" INTO v_empresa_id
                    FROM ""Compras"" c
                    WHERE c.""Id"" = NEW.""CompraId"";

                    -- Verificar se o produto tem controle de estoque
                    SELECT p.""ControleEstoque"" INTO v_produto_controle_estoque
                    FROM ""Produtos"" p
                    WHERE p.""Id"" = NEW.""ProdutoId"";

                    -- Se tem controle de estoque, criar movimentação
                    IF v_produto_controle_estoque = TRUE THEN
                        INSERT INTO ""MovimentacoesEstoque""
                            (""Id"", ""ProdutoId"", ""TipoMovimentacao"", ""DataMovimentacao"",
                             ""Quantidade"", ""PrecoUnitario"", ""CompraId"", ""ItemCompraId"",
                             ""Descricao"", ""DataCriacao"", ""DataAtualizacao"", ""EmpresaId"")
                        VALUES
                            (gen_random_uuid(), NEW.""ProdutoId"", 'Entrada', CURRENT_TIMESTAMP,
                             NEW.""Quantidade"", NEW.""PrecoUnitario"", NEW.""CompraId"", NEW.""Id"",
                             'Entrada por compra', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, v_empresa_id);

                        -- Atualizar estoque corrente do produto
                        UPDATE ""Produtos""
                        SET ""QuantidadeEstoqueCorrente"" = ""QuantidadeEstoqueCorrente"" + NEW.""Quantidade""
                        WHERE ""Id"" = NEW.""ProdutoId"";
                    END IF;

                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;

                CREATE TRIGGER tr_sync_movimentacao_on_insert_item_compra
                AFTER INSERT ON ""ItensCompra""
                FOR EACH ROW
                EXECUTE FUNCTION fn_sync_movimentacao_on_insert_item_compra();


                CREATE OR REPLACE FUNCTION fn_sync_movimentacao_on_update_item_compra()
                RETURNS TRIGGER AS $$
                DECLARE
                    v_produto_controle_estoque BOOLEAN;
                    v_quantidade_anterior INTEGER;
                BEGIN
                    -- Verificar se o produto tem controle de estoque
                    SELECT p.""ControleEstoque"" INTO v_produto_controle_estoque
                    FROM ""Produtos"" p
                    WHERE p.""Id"" = NEW.""ProdutoId"";

                    -- Se tem controle de estoque, atualizar movimentação e estoque
                    IF v_produto_controle_estoque = TRUE THEN
                        -- Obter quantidade anterior da movimentação
                        SELECT ""Quantidade"" INTO v_quantidade_anterior
                        FROM ""MovimentacoesEstoque""
                        WHERE ""ItemCompraId"" = NEW.""Id"";

                        -- Atualizar movimentação
                        UPDATE ""MovimentacoesEstoque""
                        SET  ""Quantidade""     = NEW.""Quantidade"",
                             ""PrecoUnitario""  = NEW.""PrecoUnitario"",
                             ""DataAtualizacao""= CURRENT_TIMESTAMP
                        WHERE ""ItemCompraId"" = NEW.""Id"";

                        -- Ajustar estoque corrente (reverter quantidade anterior e aplicar nova)
                        IF v_quantidade_anterior IS NOT NULL THEN
                            UPDATE ""Produtos""
                            SET ""QuantidadeEstoqueCorrente"" = ""QuantidadeEstoqueCorrente"" - v_quantidade_anterior + NEW.""Quantidade""
                            WHERE ""Id"" = NEW.""ProdutoId"";
                        END IF;
                    END IF;

                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;

                CREATE TRIGGER tr_sync_movimentacao_on_update_item_compra
                AFTER UPDATE ON ""ItensCompra""
                FOR EACH ROW
                WHEN (OLD.""Quantidade"" IS DISTINCT FROM NEW.""Quantidade""
                   OR OLD.""PrecoUnitario"" IS DISTINCT FROM NEW.""PrecoUnitario"")
                EXECUTE FUNCTION fn_sync_movimentacao_on_update_item_compra();


                CREATE OR REPLACE FUNCTION fn_sync_movimentacao_on_delete_item_compra()
                RETURNS TRIGGER AS $$
                DECLARE
                    v_produto_controle_estoque BOOLEAN;
                    v_quantidade_movimentacao INTEGER;
                BEGIN
                    -- Verificar se o produto tem controle de estoque
                    SELECT p.""ControleEstoque"" INTO v_produto_controle_estoque
                    FROM ""Produtos"" p
                    WHERE p.""Id"" = OLD.""ProdutoId"";

                    -- Se tem controle de estoque, reverter no estoque e remover movimentação
                    IF v_produto_controle_estoque = TRUE THEN
                        -- Obter quantidade da movimentação que será removida
                        SELECT ""Quantidade"" INTO v_quantidade_movimentacao
                        FROM ""MovimentacoesEstoque""
                        WHERE ""ItemCompraId"" = OLD.""Id"";

                        -- Reverter a quantidade no estoque
                        IF v_quantidade_movimentacao IS NOT NULL THEN
                            UPDATE ""Produtos""
                            SET ""QuantidadeEstoqueCorrente"" = ""QuantidadeEstoqueCorrente"" - v_quantidade_movimentacao
                            WHERE ""Id"" = OLD.""ProdutoId"";
                        END IF;

                        -- Remover a movimentação
                        DELETE FROM ""MovimentacoesEstoque""
                        WHERE ""ItemCompraId"" = OLD.""Id"";
                    END IF;

                    RETURN OLD;
                END;
                $$ LANGUAGE plpgsql;

                CREATE TRIGGER tr_sync_movimentacao_on_delete_item_compra
                BEFORE DELETE ON ""ItensCompra""
                FOR EACH ROW
                EXECUTE FUNCTION fn_sync_movimentacao_on_delete_item_compra();
            ";

            public const string SyncProdutoServicoVendidoWithMovimentacaoEstoque = @"
                -- Triggers para sincronização de ProdutoServicoVendidos com MovimentacoesEstoque

                -- Função para INSERT
                CREATE OR REPLACE FUNCTION fn_sync_movimentacao_on_insert_produto_servico_vendido()
                RETURNS TRIGGER AS $$
                DECLARE
                    v_produto_controle_estoque BOOLEAN;
                    v_tipo_transacao_comercial TEXT;
                BEGIN
                    -- Verificar se o produto tem controle de estoque
                    SELECT p.""ControleEstoque"" INTO v_produto_controle_estoque
                    FROM ""Produtos"" p
                    WHERE p.""Id"" = NEW.""ProdutoId"";

                    -- Se não tem controle de estoque, não criar movimentação
                    IF v_produto_controle_estoque IS NULL OR v_produto_controle_estoque = FALSE THEN
                        RETURN NEW;
                    END IF;

                    -- Determinar o tipo de transação comercial
                    SELECT CASE
                        WHEN EXISTS (SELECT 1 FROM ""TransacoesComerciais"" tc WHERE tc.""Id"" = NEW.""TransacaoComercialId"" AND tc.""Discriminator"" = 'Venda') THEN 'Venda'
                        WHEN EXISTS (SELECT 1 FROM ""TransacoesComerciais"" tc WHERE tc.""Id"" = NEW.""TransacaoComercialId"" AND tc.""Discriminator"" = 'OrdemServico') THEN 'OrdemServico'
                        ELSE 'Desconhecido'
                    END INTO v_tipo_transacao_comercial;

                    -- Criar movimentação de estoque
                    INSERT INTO ""MovimentacoesEstoque""
                        (""Id"", ""ProdutoId"", ""TipoMovimentacao"", ""DataMovimentacao"",
                         ""Quantidade"", ""PrecoUnitario"", ""TransacaoComercialId"", ""TipoTransacaoComercial"",
                         ""Descricao"", ""DataCriacao"", ""DataAtualizacao"", ""EmpresaId"")
                    VALUES
                        (gen_random_uuid(), NEW.""ProdutoId"", 'Saida', CURRENT_TIMESTAMP,
                         -NEW.""Quantidade"", NEW.""PrecoVenda"", NEW.""TransacaoComercialId"", v_tipo_transacao_comercial,
                         'Saída por ' || CASE WHEN v_tipo_transacao_comercial = 'Venda' THEN 'venda' ELSE 'ordem de serviço' END,
                         CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, NEW.""EmpresaId"");

                    -- Atualizar estoque corrente do produto
                    UPDATE ""Produtos""
                    SET ""QuantidadeEstoqueCorrente"" = ""QuantidadeEstoqueCorrente"" - NEW.""Quantidade""
                    WHERE ""Id"" = NEW.""ProdutoId"";

                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;

                CREATE TRIGGER tr_sync_movimentacao_on_insert_produto_servico_vendido
                AFTER INSERT ON ""ProdutoServicoVendidos""
                FOR EACH ROW
                EXECUTE FUNCTION fn_sync_movimentacao_on_insert_produto_servico_vendido();


                -- Função para UPDATE
                CREATE OR REPLACE FUNCTION fn_sync_movimentacao_on_update_produto_servico_vendido()
                RETURNS TRIGGER AS $$
                DECLARE
                    v_produto_controle_estoque BOOLEAN;
                    v_movimentacao_id UUID;
                BEGIN
                    -- Verificar se o produto tem controle de estoque
                    SELECT p.""ControleEstoque"" INTO v_produto_controle_estoque
                    FROM ""Produtos"" p
                    WHERE p.""Id"" = NEW.""ProdutoId"";

                    -- Se não tem controle de estoque, não atualizar movimentação
                    IF v_produto_controle_estoque IS NULL OR v_produto_controle_estoque = FALSE THEN
                        RETURN NEW;
                    END IF;

                    -- Verificar se existe movimentação para esta transação e produto
                    SELECT m.""Id"" INTO v_movimentacao_id
                    FROM ""MovimentacoesEstoque"" m
                    WHERE m.""TransacaoComercialId"" = NEW.""TransacaoComercialId""
                      AND m.""ProdutoId"" = NEW.""ProdutoId"";

                    IF v_movimentacao_id IS NOT NULL THEN
                        -- Reverter a quantidade anterior no estoque
                        UPDATE ""Produtos""
                        SET ""QuantidadeEstoqueCorrente"" = ""QuantidadeEstoqueCorrente"" - (
                            SELECT ""Quantidade"" FROM ""MovimentacoesEstoque"" WHERE ""Id"" = v_movimentacao_id
                        )
                        WHERE ""Id"" = NEW.""ProdutoId"";

                        -- Atualizar a movimentação
                        UPDATE ""MovimentacoesEstoque""
                        SET ""Quantidade"" = -NEW.""Quantidade"",
                            ""PrecoUnitario"" = NEW.""PrecoVenda"",
                            ""DataAtualizacao"" = CURRENT_TIMESTAMP
                        WHERE ""Id"" = v_movimentacao_id;

                        -- Aplicar a nova quantidade no estoque
                        UPDATE ""Produtos""
                        SET ""QuantidadeEstoqueCorrente"" = ""QuantidadeEstoqueCorrente"" - NEW.""Quantidade""
                        WHERE ""Id"" = NEW.""ProdutoId"";
                    END IF;

                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;

                CREATE TRIGGER tr_sync_movimentacao_on_update_produto_servico_vendido
                AFTER UPDATE ON ""ProdutoServicoVendidos""
                FOR EACH ROW
                WHEN (OLD.""Quantidade"" IS DISTINCT FROM NEW.""Quantidade""
                   OR OLD.""PrecoVenda"" IS DISTINCT FROM NEW.""PrecoVenda"")
                EXECUTE FUNCTION fn_sync_movimentacao_on_update_produto_servico_vendido();


                -- Função para DELETE
                CREATE OR REPLACE FUNCTION fn_sync_movimentacao_on_delete_produto_servico_vendido()
                RETURNS TRIGGER AS $$
                DECLARE
                    v_produto_controle_estoque BOOLEAN;
                    v_movimentacao_quantidade INTEGER;
                BEGIN
                    -- Verificar se o produto tem controle de estoque
                    SELECT p.""ControleEstoque"" INTO v_produto_controle_estoque
                    FROM ""Produtos"" p
                    WHERE p.""Id"" = OLD.""ProdutoId"";

                    -- Se não tem controle de estoque, não fazer nada
                    IF v_produto_controle_estoque IS NULL OR v_produto_controle_estoque = FALSE THEN
                        RETURN OLD;
                    END IF;

                    -- Obter a quantidade da movimentação que será removida
                    SELECT ""Quantidade"" INTO v_movimentacao_quantidade
                    FROM ""MovimentacoesEstoque""
                    WHERE ""TransacaoComercialId"" = OLD.""TransacaoComercialId""
                      AND ""ProdutoId"" = OLD.""ProdutoId"";

                    -- Reverter a movimentação no estoque (subtrair a quantidade negativa = somar)
                    IF v_movimentacao_quantidade IS NOT NULL THEN
                        UPDATE ""Produtos""
                        SET ""QuantidadeEstoqueCorrente"" = ""QuantidadeEstoqueCorrente"" - v_movimentacao_quantidade
                        WHERE ""Id"" = OLD.""ProdutoId"";
                    END IF;

                    -- Remover a movimentação
                    DELETE FROM ""MovimentacoesEstoque""
                    WHERE ""TransacaoComercialId"" = OLD.""TransacaoComercialId""
                      AND ""ProdutoId"" = OLD.""ProdutoId"";

                    RETURN OLD;
                END;
                $$ LANGUAGE plpgsql;

                CREATE TRIGGER tr_sync_movimentacao_on_delete_produto_servico_vendido
                BEFORE DELETE ON ""ProdutoServicoVendidos""
                FOR EACH ROW
                EXECUTE FUNCTION fn_sync_movimentacao_on_delete_produto_servico_vendido();
            ";

        }
    }
}