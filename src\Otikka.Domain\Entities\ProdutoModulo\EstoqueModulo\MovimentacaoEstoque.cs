using Otikka.Domain.Entities.CompraModule;
using Otikka.Domain.Entities.NotaFiscalModule.Entrada;
using Otikka.Domain.Entities.ProdutoModulo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo
{
    public class MovimentacaoEstoque : EmpresaEntidadeBase
    {
        public Guid ProdutoId { get; set; }
        public Produto Produto { get; set; } = null!;

        // Tipo de movimentação (Entrada, Saída, Ajuste)
        public TipoMovimentacaoEstoque TipoMovimentacao { get; set; }
        public DateTime DataMovimentacao { get; set; }

        // Quantidade da movimentação, positiva ou negativa dependendo do tipo
        public int Quantidade { get; set; }

        // Custo unitário no caso de entradas ou ajustes de preço
        public decimal? CustoUnitario { get; set; }

        // Referência à NFe de entrada (se aplicável)
        public Guid? CompraId { get; set; }
        public Compra? Compra { get; set; }

        public Guid? ItemCompraId { get; set; }
        public ItemCompra? ItemCompra { get; set; }

        public Guid? TransacaoComercialId { get; set; }
        public TransacaoComercial? TransacaoComercial { get; set; }
        public TipoTransacaoComercial? TipoTransacaoComercial { get; set; } 

        // Descrição ou observação adicional sobre a movimentação
        public string? Descricao { get; set; }
    }
}
