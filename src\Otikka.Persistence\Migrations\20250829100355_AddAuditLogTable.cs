﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Otikka.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddAuditLogTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AuditLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    NomeTabela = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "Nome da tabela que foi modificada"),
                    EntidadeId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "ID da entidade que foi modificada"),
                    TipoOperacao = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, comment: "Tipo de operação: INSERT, UPDATE, DELETE"),
                    ValoresAntigos = table.Column<string>(type: "jsonb", nullable: true, comment: "Valores anteriores em formato JSON (PostgreSQL JSONB)"),
                    ValoresNovos = table.Column<string>(type: "jsonb", nullable: true, comment: "Valores novos em formato JSON (PostgreSQL JSONB)"),
                    CamposModificados = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true, comment: "Campos que foram modificados (separados por vírgula)"),
                    UsuarioId = table.Column<Guid>(type: "uuid", nullable: true, comment: "ID do usuário que realizou a operação"),
                    NomeUsuario = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true, comment: "Nome do usuário que realizou a operação"),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: true, comment: "ID da empresa (para sistemas multi-tenant)"),
                    DataOperacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false, comment: "Data e hora da operação"),
                    EnderecoIP = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true, comment: "Endereço IP do usuário (suporta IPv4 e IPv6)"),
                    UserAgent = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true, comment: "User Agent do navegador"),
                    InformacoesAdicionais = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true, comment: "Informações adicionais sobre a operação"),
                    Processado = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false, comment: "Indica se o log foi processado com sucesso"),
                    DataProcessamento = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true, comment: "Data de processamento do log"),
                    MensagemErro = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true, comment: "Mensagem de erro caso o processamento falhe"),
                    TentativasProcessamento = table.Column<int>(type: "integer", nullable: false, defaultValue: 0, comment: "Número de tentativas de processamento")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AuditLogs_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_AuditLogs_Usuarios_UsuarioId",
                        column: x => x.UsuarioId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLog_DataOperacao",
                table: "AuditLogs",
                column: "DataOperacao");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLog_EmpresaId",
                table: "AuditLogs",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLog_EntidadeId",
                table: "AuditLogs",
                column: "EntidadeId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLog_NomeTabela",
                table: "AuditLogs",
                column: "NomeTabela");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLog_ProcessamentoStatus",
                table: "AuditLogs",
                columns: new[] { "Processado", "TentativasProcessamento" },
                filter: "\"Processado\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLog_UsuarioId",
                table: "AuditLogs",
                column: "UsuarioId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AuditLogs");
        }
    }
}
