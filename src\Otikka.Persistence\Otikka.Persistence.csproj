<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\cs\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\de\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\es\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\fr\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\it\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\ja\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\ko\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\Microsoft.Bcl.AsyncInterfaces.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\Microsoft.Build.Locator.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe.config" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\Newtonsoft.Json.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\pl\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\pt-BR\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\ru\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.Buffers.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.Collections.Immutable.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.CommandLine.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.Memory.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.Numerics.Vectors.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.Runtime.CompilerServices.Unsafe.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.Text.Encodings.Web.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.Text.Json.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.Threading.Tasks.Extensions.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\System.ValueTuple.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\tr\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\zh-Hans\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-net472\zh-Hant\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\cs\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\de\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\es\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\fr\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\it\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\ja\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\ko\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\Microsoft.Build.Locator.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.deps.json" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll.config" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\Newtonsoft.Json.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\pl\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\pt-BR\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\ru\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\System.Collections.Immutable.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\System.CommandLine.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\System.Text.Encodings.Web.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\System.Text.Json.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\tr\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\zh-Hans\System.CommandLine.resources.dll" />
	  <Content Remove="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.msbuild\4.11.0\contentFiles\any\any\BuildHost-netcore\zh-Hant\System.CommandLine.resources.dll" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Dapper" Version="2.1.66" />
		<PackageReference Include="Hangfire.Core" Version="1.8.21" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Workspaces.MSBuild" Version="4.14.0" />
		<!-- Forçar unificação do Roslyn 4.14.0 -->
		<PackageReference Include="Microsoft.CodeAnalysis" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Scripting" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Scripting.Common" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Workspaces.Common" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.VisualBasic" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.VisualBasic.Workspaces" Version="4.14.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		
		<PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Otikka.Application\Otikka.Application.csproj" />
	</ItemGroup>

</Project>
