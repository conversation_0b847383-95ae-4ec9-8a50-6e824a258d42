using ApexCharts;
using Blazored.Modal;
using Microsoft.EntityFrameworkCore;
using Otikka.App.Services;
using Otikka.Application.Features.Users.Commands.CreatePassword;
using Otikka.Application.Features.Users.Queries.GetUserLogin;
using Syncfusion.Blazor;
using Wolverine;

namespace Otikka.App;
public static class StartupExtensions
{

    public static WebApplication ConfigureServices(this WebApplicationBuilder builder)
    {
        #region Configure - Serilog
        Log.Logger = new LoggerConfiguration()
                            .WriteTo.Console()
                            .CreateBootstrapLogger();

        builder.Host.UseSerilog((context, services, configuration) =>
        {
            configuration
                .ReadFrom.Configuration(context.Configuration)
                .ReadFrom.Services(services);
        });
        #endregion

        #region Configure - Host - Wolverine (Wolverine is a library for building event-driven applications in .NET)
        builder.Host.UseWolverine(opts =>
        {
            opts.Discovery.IncludeAssembly(typeof(Login).Assembly);
            opts.Discovery.IncludeAssembly(typeof(CreatePassword).Assembly);
        });
        #endregion
        #region Configure - Blazored Modal
        builder.Services.AddBlazoredModal();
        #endregion
        #region Configure - Blazor Server
        builder.Services.AddRazorComponents()
                        .AddInteractiveServerComponents()
                        .AddCircuitOptions(options =>
                        {
                            options.DetailedErrors = true;
                        });

        #endregion
        #region Configure - ASP.NET Core MVC
        builder.Services.AddControllersWithViews();
        #endregion
        #region Configure - Blazor Syncfusion
        Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(builder.Configuration["SyncfusionLicenseKey"]);
        builder.Services.AddSyncfusionBlazor();
        #endregion
        #region Configure - Hangfire Service
        builder.Services.AddHangfire(config => config
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UsePostgreSqlStorage(options => options.UseNpgsqlConnection(builder.Configuration.GetConnectionString(("HangfireConnection")))));
        #endregion
        #region Configure - Osen - Requirements
        builder.Services.AddScoped<NavigationService>();
        builder.Services.AddScoped<ILocalStorageService, ProtectedLocalStorageService>();
        builder.Services.AddScoped<ProdutoVendaEstado>();
        builder.Services.AddHttpContextAccessor(); // Necessário para o AuditInterceptor
        #endregion


        builder.Services.AddApplicationServices();
        builder.Services.AddInfrastructureServices(builder.Configuration);
        builder.Services.AddPersistenceServices(builder.Configuration);

        return builder.Build();
    }
    public static WebApplication ConfigurePipeline(this WebApplication app)
    {
        //app.UseMiddleware<GlobalExceptionHandlingMiddleware>();
        // Configure the HTTP request pipeline.
        if (!app.Environment.IsDevelopment())
        {
            app.UseExceptionHandler("/Error", createScopeForErrors: true);
            // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            app.UseHsts();
        }

        app.UseHttpsRedirection();

        app.UseStaticFiles();
        app.UseAntiforgery();

        app.MapControllers();
        app.MapRazorComponents<Components.App>()
            .AddInteractiveServerRenderMode();
        app.UseSerilogRequestLogging();


        // Executa o SQL especial de sincronização de movimentação de estoque na inicialização
        using (var scope = app.Services.CreateScope())
        {
            try
            {
                var db = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                db.Database.ExecuteSqlRaw(Application.Constants.Application.InitialSQL.SyncItensCompraWithMovimentacaoEstoque);
                
            }
            catch (Exception) { }


            try
            {
                var db = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                db.Database.ExecuteSqlRaw(Application.Constants.Application.InitialSQL.SyncProdutoServicoVendidoWithMovimentacaoEstoque);
                
            }
            catch (Exception) { }

        }

        // Configura jobs recorrentes do Hangfire
        using (var scope = app.Services.CreateScope())
        {
            try
            {
                var hangfireJobsService = scope.ServiceProvider.GetRequiredService<Otikka.Infrastructure.Services.HangfireJobsService>();
                hangfireJobsService.ConfigurarJobsRecorrentes();
            }
            catch (Exception ex)
            {
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
                logger.LogError(ex, "Erro ao configurar jobs recorrentes do Hangfire");
            }
        }

        return app;
    }

}
