using Microsoft.Extensions.Logging;
using Gestao.Dominio.Repositorios;

namespace Otikka.Application.Features.Pessoa.Queries.GetCliente;

public class GetClienteHandler(
    IPessoaRepositorio pessoaRepository,
    ILogger<GetClienteHandler> logger)
{
    public async Task<Result<Otikka.Domain.Entities.PessoaModulo.Pessoa>> Handle(GetCliente req)
    {
        logger.LogInformation("Buscando cliente - ID: {Id} para empresa: {EmpresaId}", req.Id, req.EmpresaId);

        try
        {
            var cliente = await pessoaRepository.Obter(req.Id);

            if (cliente == null)
            {
                logger.LogWarning("Cliente não encontrado - ID: {Id}", req.Id);
                return Result.Fail<Otikka.Domain.Entities.PessoaModulo.Pessoa>("Cliente não encontrado");
            }

            // Verificar se o cliente pertence à empresa
            if (cliente.EmpresaId != req.EmpresaId)
            {
                logger.LogWarning("Tentativa de acessar cliente {ClienteId} que não pertence à empresa {EmpresaId}", req.Id, req.EmpresaId);
                return Result.Fail<Otikka.Domain.Entities.PessoaModulo.Pessoa>("O cliente não pertence à empresa");
            }

            logger.LogInformation("Cliente encontrado - ID: {Id}, Nome: {Nome}", req.Id, cliente.Nome);
            return Result.Ok(cliente);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao buscar cliente - ID: {Id}: {Message}", req.Id, ex.Message);
            return Result.Fail<Otikka.Domain.Entities.PessoaModulo.Pessoa>($"Erro ao buscar cliente: {ex.Message}");
        }
    }
} 