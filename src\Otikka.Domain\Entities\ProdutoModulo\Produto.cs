using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo;

namespace Otikka.Domain.Entities.ProdutoModulo;

public class Produto : EmpresaEntidadeBase
{
    public Guid? CategoriaProdutoId { get; set; }
    public CategoriaProduto? CategoriaProduto { get; set; }
    public Guid? MarcaProdutoId { get; set; }
    public MarcaProduto? MarcaProduto { get; set; }
    /// <summary>
    /// Encomenda = true
    /// Significa que este produto será vendido na OS como uma lente oftamologica e passará por laboratório etc para encomenda.
    /// </summary>
    public bool Encomenda { get; set; }

    public bool ControleEstoque { get; set; }

    public int? EstoqueTotal { get; set; }
    //TODO - Adicionar Imagens (implementar no futuro caso tenha ecommerce)
    //public string Imagem { get; set; }

    /// <summary>
    /// Preço de venda do produto
    /// </summary>
    public decimal PrecoVenda { get; set; }

    /// <summary>
    /// Quantidade atual em estoque
    /// </summary>
    public int QuantidadeEstoqueCorrente { get; set; }

    /// <summary>
    /// Quantidade mínima de estoque para alerta
    /// </summary>
    public int QuantidadeEstoqueMinimo { get; set; }

    



    /// <summary>
    /// Movimentações de estoque do produto
    /// </summary>
    public ICollection<MovimentacaoEstoque>? MovimentacoesEstoque { get; set; }

    public string? SKU { get; set; }
    public string? EAN { get; set; }
    public string Codigo { get; set; } = string.Empty;
    public string Nome { get; set; } = string.Empty;
    public Guid? FornecedorId { get; set; }
    public Pessoa? Fornecedor { get; set; }

    public bool Ativo { get; set; } = true;

    #region Informações NFe/NFCe

    /// <summary>
    /// Origem da mercadoria conforme especificação da NFe/NFCe
    /// </summary>
    [Display(Name = "Origem da Mercadoria")]
    public OrigemMercadoria? OrigemMercadoria { get; set; }

    /// <summary>
    /// Nomenclatura Comum do Mercosul - Código de classificação fiscal
    /// </summary>
    [Display(Name = "NCM")]
    public string? NCM { get; set; }

    #endregion
}