﻿@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Cliente.Componentes
@using Otikka.Domain.Entities.OrdemServicoModulo
@using Otikka.Domain.Entities.PessoaModulo
@using Otikka.Domain.Entities.TransacaoFinanceiraModulo
@using Otikka.Application.Features.Pessoa.Queries.GetAllLaboratorios
@using Otikka.Application.Features.Pessoa.Queries.GetAllClientes
@using Otikka.Application.Features.Colaborador.Queries.GetColaboradoresByEmpresa

@using Otikka.Application.Features.FormaPagamento.Queries.GetAllFormasPagamento
@using FluentResults
@inherits PageBase

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header align-items-center d-flex">
                <h4 class="card-title mb-0 flex-grow-1">
                    Ordem de serviço
                    
                    @if(EhEdicao){
                        <span class="badge bg-primary-subtle text-primary p-1 fs-4">@OrdemServico!.NumeroIdentificador</span>
                    }
                </h4>
            </div>
            <div class="card-body">
                <div class="row gy-4">
                    @if (Empresa is not null)
                    {
                        @if (Empresa.TipoNumeracaoOrdemServico == TipoAlgoritmoGeradorId.Manual)
                        {
                            <div class="col-xxl-3 col-md-6">
                                <div>
                                    <label for="SallerId" class="form-label">Número da O.S.</label>
                                    <InputText class="form-control" @bind-Value="OrdemServico!.NumeroIdentificador" />
                                    <ValidationMessage For="() => OrdemServico!.NumeroIdentificador" />
                                </div>
                            </div>
                        }
                    }

                    <div class="col-xxl-3 col-md-6">
                        <div>
                            <label for="vendedor" class="form-label">Vendedor</label>
                            <SfComboBox CssClass="sync-adapt-select" TValue="Guid?" TItem="Usuario" @bind-Value="OrdemServico!.VendedorId" DataSource="@Colaboradores" Placeholder="Escolho o vendedor" FilterType="FilterType.Contains" AllowFiltering="true">
                                <ComboBoxFieldSettings Text="Nome" Value="Id"></ComboBoxFieldSettings>
                                <ComboBoxTemplates TItem="Usuario">
                                    <NoRecordsTemplate>
                                        <span class='norecord'>Nenhum vendedor cadastrado</span>
                                    </NoRecordsTemplate>
                                </ComboBoxTemplates>
                            </SfComboBox>
                            <ValidationMessage For="() => OrdemServico!.VendedorId" />
                        </div>
                    </div>

                    <div class="col-xxl-3 col-md-6">
                        <div>
                            <label for="cliente" class="form-label">Cliente</label>
                            <div class="input-group flex-nowrap">
                                @if (Clientes is not null)
                                {
                                    <SfComboBox CssClass="sync-adapt-select" TValue="Guid" TItem="Pessoa" @bind-Value="OrdemServico!.ClienteId" DataSource="@Clientes" Placeholder="Escolha o cliente" FilterType="FilterType.Contains" AllowFiltering="true">
                                        <ComboBoxFieldSettings Text="Nome" Value="Id"></ComboBoxFieldSettings>
                                        <ComboBoxTemplates TItem="Pessoa">
                                            <NoRecordsTemplate>
                                                <span class='norecord'>Nenhum cliente cadastrado</span>
                                            </NoRecordsTemplate>
                                        </ComboBoxTemplates>
                                    </SfComboBox>
                                }
                                <button class="btn btn-primary" type="button" @onclick="AbrirModal">+</button>
                            </div>
                            <ValidationMessage For="() => OrdemServico!.ClienteId" />
                        </div>
                    </div>
                    <div class="col-xxl-3 col-md-6">
                        <div>
                            <label for="cliente" class="form-label">Laboratório</label>
                            <div class="input-group flex-nowrap">
                                @if (Laboradorios is not null)
                                {
                                    <SfComboBox CssClass="sync-adapt-select" TValue="Guid?" TItem="Pessoa" @bind-Value="OrdemServico!.LaboratorioId" DataSource="@Laboradorios" Placeholder="Escolha o laboratório" FilterType="FilterType.Contains" AllowFiltering="true">
                                        <ComboBoxFieldSettings Text="Nome" Value="Id"></ComboBoxFieldSettings>
                                        <ComboBoxTemplates TItem="Pessoa">
                                            <ItemTemplate Context="pessoa">
                                                <div> @pessoa.Nome @(pessoa.Documento is not null ? $"({pessoa.Documento})" : "")</div>
                                            </ItemTemplate>
                                            <NoRecordsTemplate>
                                                <span class='norecord'>Nenhum laboratório cadastrado</span>
                                            </NoRecordsTemplate>
                                        </ComboBoxTemplates>
                                    </SfComboBox>
                                }
                            </div>
                            <ValidationMessage For="() => OrdemServico!.LaboratorioId" />
                        </div>
                    </div>
                </div>
                <div class="row gy-4 mt-1">
                    <div class="col-xxl-3 col-md-6">
                        <div>
                            <label for="ExpectedDeliveryDate" class="form-label">Data de registro</label>
                            <InputDate Type="InputDateType.DateTimeLocal" @bind-Value="OrdemServico.RegistroData" class="form-control" />
                            <ValidationMessage For="() => OrdemServico!.RegistroData" />
                        </div>
                    </div>
                    <div class="col-xxl-3 col-md-6">
                        <div>
                            <label for="ExpectedDeliveryDate" class="form-label">Data prevista de entrega</label>
                            <div class="input-group">
                                <InputDate Type="InputDateType.DateTimeLocal" @bind-Value="OrdemServico.PrevisaoEntregaData" class="form-control" />
                                <button class="btn btn-outline-primary" type="button" @onclick="() => AddBusinessDays(3)">+3</button>
                                <button class="btn btn-outline-primary" type="button" @onclick="() => AddBusinessDays(7)">+7</button>
                                <button class="btn btn-outline-primary" type="button" @onclick="() => AddBusinessDays(10)">+10</button>
                            </div>
                            <ValidationMessage For="() => OrdemServico!.PrevisaoEntregaData" />
                        </div>
                    </div>
                </div>
                <div class="row gy-4 mt-1">
                    <div class="col-xxl-12 col-md-12">
                        <div>
                            <label for="Observation" class="form-label">Observação</label>
                            <InputTextArea @bind-Value="OrdemServico.Observacao" class="form-control" id="Observation"></InputTextArea>
                            <ValidationMessage For="() => OrdemServico!.Observacao" />
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

@code {
    [Parameter] public bool EhEdicao { get; set; }
    [Parameter] public OrdemServico? OrdemServico { get; set; } = default!;

    private List<Usuario>? Colaboradores { get; set; }

    private List<Pessoa>? Clientes { get; set; }
    private List<Pessoa>? Laboradorios { get; set; }
    private Empresa? Empresa { get; set; }

    protected override async Task OnInitializedAsync()
    {
        Empresa = await GetEmpresaAsync();

        try
        {
            // Carregar colaboradores usando handler
            var colaboradoresQuery = new GetColaboradoresByEmpresa(Empresa.Id);
            var colaboradoresResult = await MessageBus.InvokeAsync<Result<List<Usuario>>>(colaboradoresQuery);

            if (colaboradoresResult.IsSuccess)
            {
                Colaboradores = colaboradoresResult.Value;
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", colaboradoresResult.Errors.Select(e => e.Message)));
            }



            // Carregar clientes usando handler
            var clientesQuery = new GetAllClientes(await GetEmpresaIdAsync());
            var clientesResult = await MessageBus.InvokeAsync<Result<List<Pessoa>>>(clientesQuery);

            if (clientesResult.IsSuccess)
            {
                Clientes = clientesResult.Value.Select(a => new Pessoa
                {
                    Id = a.Id,
                    Nome = $"{a.Nome} " + (a.Documento != null ? $"({a.Documento.FormatarCpfCnpj()})" : "")
                }).ToList();
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", clientesResult.Errors.Select(e => e.Message)));
            }

            // Carregar laboratórios usando handler
            var laboratoriosQuery = new GetAllLaboratorios(await GetEmpresaIdAsync());
            var laboratoriosResult = await MessageBus.InvokeAsync<Result<List<Pessoa>>>(laboratoriosQuery);

            if (laboratoriosResult.IsSuccess)
            {
                Laboradorios = laboratoriosResult.Value;
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", laboratoriosResult.Errors.Select(e => e.Message)));
            }

            // Carregar formas de pagamento usando handler
            var formasPagamentoQuery = new GetAllFormasPagamento() { EmpresaId = Empresa.Id };
            var formasPagamentoResult = await MessageBus.InvokeAsync<Result<List<FormaPagamento>>>(formasPagamentoQuery);

            if (!formasPagamentoResult.IsSuccess || formasPagamentoResult.Value.Count == 0)
            {
                await AlertService.ShowAlert("É necessário ter pelo menos uma forma de pagamento para continuar com o cadastro da ordem de serviço!");
                NavigationManager.NavigateTo(Application.Routes.FormaPagamentoCadastrar);
                return;
            }

            // Validações
            if (Colaboradores is not null && Colaboradores.Count == 0)
            {
                await AlertService.ShowAlert("É necessário ter um colaborador cadastrado para continuar com o cadastro da ordem de serviço!");
                NavigationManager.NavigateTo(Application.Routes.ColaboradorCadastrar);
                return;
            }



            if (OrdemServico is not null)
            {
                // Definir valores padrão
                if (Colaboradores?.Count > 0) OrdemServico.VendedorId = await GetUsuarioIdLoggedAsync();
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowAlert("Ocorreu um erro ao carregar os dados da ordem de serviço", ex.Message);
        }
    }
    
    private void AddBusinessDays(int days)
    {
        if (OrdemServico is not null)
        {
            OrdemServico.PrevisaoEntregaData = CalculateBusinessDays(OrdemServico.RegistroData, days);
        }
    }

    private DateTimeOffset? CalculateBusinessDays(DateTimeOffset startDate, int days)
    {
        var date = startDate.DateTime;
        int addedDays = 0;
        while (addedDays < days)
        {
            date = date.AddDays(1);
            if (date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday)
            {
                addedDays++;
            }
        }
        return new DateTimeOffset(date);
    }

    private async Task AbrirModal()
    {
        var options = new ModalOptions()
        {
            Size = ModalSize.Large
        };
        var modal = Modal.Show<ClienteFormModal>("Cliente - Cadastro rápido", options);
        var result = await modal.Result;

        if (result.Confirmed && result.Data is Pessoa cliente)
        {
            Clientes ??= new List<Pessoa>();
            Clientes.Add(cliente);

            if (OrdemServico is not null)
                OrdemServico.ClienteId = cliente.Id;
        }
    }
}