---
type: "always_apply"
---

O agente sempre ***e responder no idioma Português (Brazil).

# Descrição do Projeto

Otikka é um sistema ERP completo para uma rede de óticas, desenvolvido para gerenciar todos os aspectos do negócio óptico, incluindo:

- **Gestão de Clientes**: Cadastro completo com histórico de compras e receitas oftalmológicas
- **Ordens de Serviço**: Controle completo do processo de atendimento e produção
- **Vendas e Faturamento**: Sistema completo de vendas com múltiplas formas de pagamento
- **Gestão de Produtos**: Catálogo completo com controle de estoque por loja
- **Controle de Estoque**: Movimentações, transferências entre lojas e inventário
- **Gestão de Lojas**: Controle multi-loja com configurações específicas
- **Recursos Humanos**: Cadastro de funcionários e controle de acesso
- **Receitas Oftalmológicas**: Gestão completa de prescrições médicas dos clientes
- **Fornecedores**: Cadastro e gestão de relacionamento com fornecedores
- **Financeiro**: Contas a pagar e receber, fluxo de caixa e conciliação
- **Relatórios**: Dashboard executivo e relatórios operacionais
- **Pagamentos**: Integração com gateways de pagamento e controle financeiro

O sistema foi desenvolvido seguindo os princípios de **Arquitetura Limpa (Clean Architecture)** e **Domain-Driven Design (DDD)**, garantindo alta manutenibilidade, testabilidade e escalabilidade.

# Pilha de Tecnologia

## Backend
- **.NET 9**: Framework principal da aplicação
- **Entity Framework Core 9**: ORM para acesso a dados com abordagem Code First
- **PostgreSQL**: Banco de dados relacional principal
- **Wolverine .NET**: Framework CQRS/Mediator para arquitetura orientada a mensagens
- **FluentValidation**: Biblioteca para validação de modelos e regras de negócio
- **FluentResults**: Implementação do padrão Result para tratamento de erros
- **Hangfire**: Processamento de tarefas em segundo plano e jobs recorrentes

## Frontend
- **Blazor Server**: Framework para desenvolvimento de interfaces web interativas
- **AKSoftware.Blazor.Utilities**: Biblioteca para gerenciamento de estado entre componentes

## Infraestrutura
- **AWS S3**: Armazenamento de arquivos, imagens e documentos
- **MinIO**: Alternativa local para desenvolvimento (compatível com S3)

## Testes
- **xUnit**: Framework de testes unitários
- **bUnit**: Framework para testes de componentes Blazor
- **FluentAssertions**: Biblioteca para assertions mais expressivas

# Arquitetura do Sistema

## Padrões Arquiteturais

### 1. **Clean Architecture (Arquitetura Limpa)**
- **Separação de Responsabilidades**: Cada camada tem responsabilidades bem definidas
- **Inversão de Dependências**: Camadas internas não dependem de camadas externas
- **Testabilidade**: Facilita a criação de testes unitários e de integração
- **Independência de Framework**: Regras de negócio independentes de tecnologias específicas

### 2. **Domain-Driven Design (DDD)**
- **Entidades de Domínio**: Modelagem rica do domínio de negócio
- **Agregados**: Agrupamento de entidades relacionadas
- **Value Objects**: Objetos de valor imutáveis
- **Domain Services**: Serviços de domínio para lógicas complexas

### 3. **CQRS (Command Query Responsibility Segregation)**
- **Commands**: Operações que modificam o estado do sistema
- **Queries**: Operações de consulta que não modificam estado
- **Handlers**: Processadores específicos para cada comando/consulta
- **Wolverine .NET**: Framework para implementação do padrão CQRS

### 4. **Repository Pattern**
- **Abstração de Dados**: Interface única para acesso a dados
- **Testabilidade**: Facilita a criação de mocks para testes
- **Flexibilidade**: Permite mudança de tecnologia de persistência

## Estrutura de Camadas

### **Camada de Domínio (Otikka.Domain)**
```
Otikka.Domain/
├── Entities/           # Entidades de negócio
├── Interfaces/         # Contratos do domínio
├── Common/            # Classes base e utilitários
├── Enums/             # Enumeradores do sistema
└── ValueObjects/      # Objetos de valor
```

**Responsabilidades:**
- Definir entidades de negócio
- Implementar regras de domínio
- Definir interfaces de repositório
- Modelar objetos de valor

### **Camada de Aplicação (Otikka.Application)**
```
Otikka.Application/
├── Features/          # Casos de uso (CQRS)
│   ├── Commands/      # Comandos (operações de escrita)
│   └── Queries/       # Consultas (operações de leitura)
├── Contracts/         # Interfaces de infraestrutura
├── Models/            # DTOs e ViewModels
├── Validators/        # Validações com FluentValidation
├── Constants/         # Constantes do sistema
├── Common/            # Extensões e utilitários
└── Utilidades/        # Classes de apoio
```

**Responsabilidades:**
- Implementar casos de uso
- Coordenar operações entre domínio e infraestrutura
- Validar dados de entrada
- Definir contratos de infraestrutura

### **Camada de Infraestrutura (Otikka.Infrastructure)**
```
Otikka.Infrastructure/
├── Storage/           # Serviços de armazenamento (S3, MinIO)
├── Services/          # Serviços externos (APIs, SDKs)
├── Email/             # Serviços de email
├── Notifications/     # Serviços de notificação
└── External/          # Integrações externas
```

**Responsabilidades:**
- Implementar serviços de infraestrutura
- Integrar com APIs externas
- Gerenciar armazenamento de arquivos
- Implementar serviços de comunicação

### **Camada de Persistência (Otikka.Persistence)**
```
Otikka.Persistence/
├── Context/           # DbContext do Entity Framework
├── Configurations/    # Configurações de entidades
├── Repositories/      # Implementações de repositórios
├── Migrations/        # Migrações do banco de dados
└── Seed/             # Dados iniciais
```

**Responsabilidades:**
- Implementar acesso a dados
- Configurar mapeamento objeto-relacional
- Gerenciar migrações de banco
- Implementar repositórios

### **Camada de Apresentação (Otikka.App)**
```
Otikka.App/
├── Pages/             # Páginas Blazor
├── Components/        # Componentes reutilizáveis
├── Shared/            # Layouts e componentes compartilhados
├── Services/          # Serviços específicos da UI
├── wwwroot/           # Arquivos estáticos
└── Models/            # ViewModels específicos da UI
```

**Responsabilidades:**
- Implementar interface do usuário
- Gerenciar estado da aplicação
- Validar entrada do usuário
- Coordenar com a camada de aplicação

# Política de Armazenamento de Arquivos

**REGRA FUNDAMENTAL: Todos os arquivos, fotos, documentos, imagens e qualquer tipo de conteúdo estático ***em ser armazenados EXCLUSIVAMENTE na AWS S3.**

## Serviço de Armazenamento AWS S3

O projeto utiliza o `S3StorageService` como implementação principal do `IFileStorageService`, garantindo:

- **Escalabilidade**: Capacidade ilimitada de armazenamento
- **Segurança**: Criptografia AES256 para todos os arquivos
- **Disponibilidade**: Alta disponibilidade e durabilidade dos dados
- **Performance**: CDN integrada para entrega rápida de conteúdo

### Configuração Obrigatória

```json
{
  "AWS": {
    "Region": "us-east-1",
    "AccessKey": "sua-access-key",
    "SecretKey": "sua-secret-key"
  }
}
```

### Estrutura de Buckets Padronizada

```
otikka-{ambiente}/
├── usuarios/
│   ├── fotos/           # Fotos de perfil
│   └── documentos/      # Documentos pessoais
├── empresas/
│   ├── logotipos/       # Logotipos das empresas
│   └── documentos/      # Documentos corporativos
├── produtos/
│   ├── imagens/         # Imagens de produtos
│   └── catalogos/       # Catálogos e manuais
├── ordens-servico/
│   ├── anexos/          # Anexos das OS
│   └── receitas/        # Receitas oftalmológicas
├── vendas/
│   └── comprovantes/    # Comprovantes de venda
└── documentos/
    ├── contratos/       # Contratos diversos
    └── relatorios/      # Relatórios gerados
```

### Regras de Nomenclatura

- **GUID obrigatório**: `{Guid.NewGuid()}-{nomeOriginal}`
- **Organização por módulo**: `usuarios/fotos/`, `produtos/imagens/`
- **Nomes descritivos**: `receita-oftalmologica-{clienteId}-{data}.pdf`

# Gerenciamento de Estado entre Componentes Blazor

**REGRA OBRIGATÓRIA: Todo gerenciamento de estado entre componentes/telas do Blazor ***e ser realizado usando a biblioteca AKSoftware.Blazor.Utilities.**

## MessagingCenter Pattern

### Configuração Obrigatória

```csharp
// Program.cs
builder.Services.AddMessagingCenter();
```

### Padrão de Implementação

```csharp
// Componente que envia
MessagingCenter.Send<ComponenteOrigem, TipoDado>(this, "nome_da_mensagem", dados);

// Componente que recebe
MessagingCenter.Subscribe<ComponenteOrigem, TipoDado>(this, "nome_da_mensagem", async (sender, dados) =>
{
    await InvokeAsync(() =>
    {
        // Processar dados
        StateHasChanged();
    });
});

// Sempre implementar IDisposable
public void Dispose()
{
    MessagingCenter.Unsubscribe<ComponenteOrigem>(this, "nome_da_mensagem");
}
```

### Mensagens Padronizadas

```csharp
public static class SystemMessages
{
    public const string EmpresaSelecionada = "empresa_selecionada";
    public const string DadosAtualizados = "dados_atualizados";
    public const string FiltroAplicado = "filtro_aplicado";
    public const string UsuarioLogado = "usuario_logado";
    public const string NotificacaoExibida = "notificacao_exibida";
}
```

# Padronização de Rotas Blazor

**REGRA OBRIGATÓRIA: Todas as páginas Blazor ***em usar as constantes de rota definidas em `Application.Routes`.**

## Padrão Obrigatório

```csharp
// ✅ CORRETO
@attribute [Route(Application.Routes.ClienteCadastrar)]

// ❌ INCORRETO
@page "/Dashboard/Cliente/Cadastrar"
```

## Estrutura de Rotas por Módulo

### Módulo Cliente
- `ClienteListar`: `/Dashboard/Cliente/Listar`
- `ClienteEditar`: `/Dashboard/Cliente/Editar/{id:guid}`
- `ClienteCadastrar`: `/Dashboard/Cliente/Cadastrar`
- `ReceitaListar`: `/Dashboard/Cliente/{clienteId:guid}/Receitas`

### Módulo Produtos
- `ProdutoListar`: `/Dashboard/ProdutoModulo/Produto/Listar`
- `ProdutoEditar`: `/Dashboard/ProdutoModulo/Produto/Editar/{id:guid}`
- `ProdutoCadastrar`: `/Dashboard/ProdutoModulo/Produto/Cadastrar`

### Módulo Financeiro
- `TransacaoFinanceiraListar`: `/Dashboard/Financeiro/Transacoes`
- `ContasReceberListar`: `/Dashboard/Financeiro/ContasReceber`
- `ContasPagarListar`: `/Dashboard/Financeiro/ContasPagar`

## Navegação com Parâmetros

```csharp
// Usar método GerarRota para rotas parametrizadas
var rota = Application.Routes.GerarRota(Application.Routes.ClienteEditar, clienteId.ToString());
NavigationManager.NavigateTo(rota);
```

# Convenções de Nomenclatura

## Classes e Interfaces
- **PascalCase** para classes, métodos e propriedades públicas
- **Interface**: Prefixo "I" (ex: `IClienteRepository`, `IEmailService`)
- **Entidades**: Nome singular (ex: `Cliente`, `Produto`, `OrdemServico`)
- **Repositórios**: `{Entidade}Repository` (ex: `ClienteRepository`)
- **Serviços**: `{Funcionalidade}Service` (ex: `EmailService`, `S3StorageService`)

## Variáveis e Campos
- **camelCase** para campos privados e variáveis locais
- **Campos privados**: Prefixo "_" (ex: `_clienteRepository`, `_logger`)
- **Parâmetros**: camelCase (ex: `clienteId`, `nomeCompleto`)

## Métodos
- **Verbos descritivos**: `ObterClientePorId`, `CriarNovaOrdemServico`
- **Async**: Sufixo "Async" (ex: `ObterClienteAsync`, `SalvarAsync`)
- **Handlers**: `{Operacao}Handler` (ex: `CriarClienteHandler`, `ObterProdutosHandler`)

## Constantes
- **UPPER_CASE** com underscore (ex: `MAX_TENTATIVAS_LOGIN`, `TIMEOUT_PADRAO`)
- **Rotas**: PascalCase (ex: `ClienteListar`, `ProdutoEditar`)

# Estrutura de Testes

## Testes Unitários (Otikka.Application.UnitTests)
```
Application.UnitTests/
├── Features/
│   ├── Clientes/      # Testes de handlers de cliente
│   ├── Produtos/      # Testes de handlers de produto
│   └── Vendas/        # Testes de handlers de venda
├── Validators/        # Testes de validadores
└── Common/            # Utilitários de teste
```

## Testes de Integração (Otikka.Persistence.IntegrationTests)
```
Persistence.IntegrationTests/
├── Repositories/      # Testes de repositórios
├── Context/           # Testes de contexto
└── Migrations/        # Testes de migrações
```

## Testes de Componentes (Otikka.App.IntegrationTests)
```
App.IntegrationTests/
├── Pages/             # Testes de páginas Blazor
├── Components/        # Testes de componentes
└── Services/          # Testes de serviços da UI
```

# Workflow de Desenvolvimento

## Fluxo de Operações

1. **Interface do Usuário (Blazor)**
   - Usuário interage com componente
   - Validação client-side com FluentValidation
   - Envio de comando/consulta via Wolverine

2. **Camada de Aplicação**
   - Handler recebe comando/consulta
   - Executa validações de negócio
   - Coordena operações com repositórios

3. **Camada de Domínio**
   - Aplicação de regras de negócio
   - Validação de invariantes
   - Eventos de domínio

4. **Camada de Persistência**
   - Operações de banco de dados
   - Mapeamento objeto-relacional
   - Transações e consistência

## Padrão de Implementação

### 1. Criar Comando/Consulta
```csharp
public record CriarClienteCommand(string Nome, string Email, string Telefone);
```

### 2. Implementar Handler
```csharp
public class CriarClienteHandler
{
    private readonly IClienteRepository _repository;
    
    public async Task<Result<Guid>> Handle(CriarClienteCommand command)
    {
        // Lógica de negócio
        // Validações
        // Persistência
    }
}
```

### 3. Criar Validador
```csharp
public class CriarClienteValidator : AbstractValidator<CriarClienteCommand>
{
    public CriarClienteValidator()
    {
        RuleFor(x => x.Nome).NotEmpty().MaximumLength(100);
        RuleFor(x => x.Email).EmailAddress();
    }
}
```

### 4. Implementar na UI
```csharp
@attribute [Route(Application.Routes.ClienteCadastrar)]

@code {
    private async Task SalvarCliente()
    {
        var command = new CriarClienteCommand(nome, email, telefone);
        var result = await MessageBus.InvokeAsync(command);
        
        if (result.IsSuccess)
        {
            MessagingCenter.Send<CadastrarCliente>(this, SystemMessages.ClienteCriado);
            NavigationManager.NavigateTo(Application.Routes.ClienteListar);
        }
    }
}
```

# Regras de Qualidade de Código

## Princípios SOLID
- **S**ingle Responsibility: Uma classe, uma responsabilidade
- **O**pen/Closed: Aberto para extensão, fechado para modificação
- **L**iskov Substitution: Subtipos ***em ser substituíveis
- **I**nterface Segregation: Interfaces específicas e coesas
- **D**ependency Inversion: Dependa de abstrações, não de implementações

## Clean Code
- **Nomes expressivos**: Código auto-documentado
- **Funções pequenas**: Máximo 20 linhas por método
- **Comentários em português**: Quando necessários
- **Tratamento de erros**: Usar Result Pattern
- **Testes**: Cobertura mínima de 80%

## Padrões Obrigatórios
- **Result Pattern**: Para tratamento de erros
- **Repository Pattern**: Para acesso a dados
- **CQRS**: Para separação de comandos e consultas
- **Dependency Injection**: Para inversão de controle
- **Async/Await**: Para operações assíncronas

# Configurações de Ambiente

## Desenvolvimento
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=otikka_***;Username=***;Password=***"
  },
  "AWS": {
    "Region": "us-east-1",
    "AccessKey": "***-key",
    "SecretKey": "***-secret"
  },
  "Hangfire": {
    "ConnectionString": "Host=localhost;Database=otikka_hangfire_***"
  }
}
```

## Produção
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=prod-db;Database=otikka_prod;Username=prod;Password=***"
  },
  "AWS": {
    "Region": "us-east-1",
    "AccessKey": "prod-key",
    "SecretKey": "***"
  },
  "Hangfire": {
    "ConnectionString": "Host=prod-db;Database=otikka_hangfire_prod"
  }
}
```

# Segurança

## Autenticação e Autorização
- **JWT Tokens**: Para autenticação de usuários
- **Role-based Access**: Controle baseado em perfis
- **Claims**: Permissões granulares
- **Session Management**: Controle de sessões ativas

## Proteção de Dados
- **Criptografia**: Dados sensíveis criptografados
- **HTTPS**: Comunicação segura obrigatória
- **Sanitização**: Validação e sanitização de inputs
- **Audit Trail**: Log de todas as operações críticas

# Performance

## Otimizações Obrigatórias
- **Lazy Loading**: Carregamento sob demanda
- **Caching**: Cache de consultas frequentes
- **Pagination**: Paginação em listagens
- **Compression**: Compressão de responses
- **CDN**: Uso de CDN para arquivos estáticos

## Monitoramento
- **Application Insights**: Monitoramento de performance
- **Health Checks**: Verificação de saúde da aplicação
- **Logging**: Log estruturado com Serilog
- **Metrics**: Métricas de negócio e técnicas

# Oculta a pasta de documentação, que não faz parte do aplicativo principal.
exclude: docs/**

# Oculta arquivos de bibliotecas de terceiros que não ***em ser modificados.
exclude: **/wwwroot/vendor/**

# Oculta arquivos de fontes.
exclude: **/wwwroot/fonts/**

# Oculta arquivos minificados, que são gerados e não ***em ser editados diretamente.
exclude: **/*.min.css
exclude: **/*.min.js