using Otikka.Application.Models;
using Otikka.Application.Models.Requests;
using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo;
using Otikka.Domain.Enums;

namespace Otikka.Application.Contracts.Persistence;

public interface IMovimentacaoEstoqueRepositorio
{
    Task<List<MovimentacaoEstoque>> ObterTudo(Guid empresaId);
    Task<PaginatedList<MovimentacaoEstoque>> ObterTudo(Guid empresaId, MovimentacaoEstoqueFiltroRequest filtro, PaginationParameters paginationParams);
    Task<List<MovimentacaoEstoque>> ObterPorProduto(Guid produtoId, Guid empresaId);
    Task<List<MovimentacaoEstoque>> ObterPorTipo(TipoMovimentacaoEstoque tipo, Guid empresaId);
    Task<List<MovimentacaoEstoque>> ObterPorPeriodo(DateTime dataInicio, DateTime dataFim, Guid empresaId);
    Task<MovimentacaoEstoque?> Obter(Guid id);
    Task<MovimentacaoEstoque?> ObterSemTracking(Guid id);
    Task<List<MovimentacaoEstoque>> ObterPorTransacaoComercial(Guid transacaoComercialId);
    Task Cadastrar(MovimentacaoEstoque entity);
    Task CadastrarSemAtualizarEstoque(MovimentacaoEstoque entity);
    Task Atualizar(MovimentacaoEstoque entity);
    Task Excluir(Guid id);
    Task Excluir(MovimentacaoEstoque entity);
}
