<!DOCTYPE html>
<html lang="pt-br" data-menu-color="dark">

<head>
    <base href="/" />
    <meta charset="utf-8" />
    <title>#Title | Osen - Responsive Bootstrap 5 Admin Dashboard</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="images/favicon.ico">

    <!-- Theme Config Js -->
    <script src="js/config.js"></script>

    <!-- Vendor css -->
    <link href="vendor/css/vendors.min.css" rel="stylesheet" type="text/css" />

    <!-- App css -->
    <link href="css/app.min.css" rel="stylesheet" type="text/css" id="app-style" />

    <!-- Icons css -->
    <link href="css/velzon.icons.min.css" rel="stylesheet" type="text/css" />
    <link href="css/icons.min.css" rel="stylesheet" type="text/css" />

    <!-- Toastify -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- Syncfusion -->
    <link href="_content/Syncfusion.Blazor.Themes/bootstrap5.css" rel="stylesheet" />    
    <link href="custom/custom.css" rel="stylesheet" />    
    <link href="Otikka.App.styles.css" rel="stylesheet">
    <HeadOutlet />
</head>

<body>
    <Routes @rendermode="RenderModeForPage" />

    <!-- Vendor js -->
    <script src="vendor/js/vendors.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- App js -->
    <script src="js/app.js"></script>    

    <script src="_framework/blazor.web.js"></script>

    <script src="custom/custom.js"></script>
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    
</body>

</html>
@code {
    private IComponentRenderMode? RenderModeForPage => new InteractiveServerRenderMode(prerender: false);
}