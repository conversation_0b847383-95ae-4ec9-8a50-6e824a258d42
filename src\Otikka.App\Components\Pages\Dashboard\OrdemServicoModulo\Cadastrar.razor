@using Otikka.App.Components.Pages.Dashboard.OrdemServicoModulo.Componentes
@using Otikka.Application.Features.OrdemServico.Commands.CreateOrdemServico
@using Otikka.Application.Features.OrdemServico.Queries.GetOrdemServico
@using Otikka.Application.Features.Produto.Queries.GetAllProdutos
@using Otikka.Domain.Entities.Common
@using Otikka.Domain.Entities.OrdemServicoModulo
@using FluentResults
@using System.Text
@using Otikka.Domain.Entities.PessoaModulo
@using Otikka.Domain.Entities.ProdutoModulo
@using Otikka.Domain.Entities.TransacaoFinanceiraModulo
@using Wolverine

@attribute [Route(Application.Routes.OrdemServicoCadastrar)]

@inherits PageBase

<PageTitle>
    Ordem de Serviço - Cadastro
</PageTitle>
<OrdemServicoForm EhEdicao="@false" OrdemServico="OrdemServico" OnSave="Save" OnFileSelected="@HandleReceitaImageFileSelected" />

@code {
    [Parameter] public Guid? Id { get; set; }

    [SupplyParameterFromForm] private OrdemServico OrdemServico { get; set; } = new() { RegistroData = DateTimeOffset.Now, PrevisaoEntregaData = DateTimeOffset.Now, ProdutoServicoVendidos = new List<ProdutoServicoVendido>(), TransacaoFinanceira = new TransacaoFinanceira() { TipoTransacaoFinanceira = TipoTransacaoFinanceira.Recebimento } };

    private IBrowserFile? selectedReceitaImageFile;

    private void HandleReceitaImageFileSelected(IBrowserFile file)
    {
        selectedReceitaImageFile = file;
    }


    protected override async Task OnParametersSetAsync()
    {
        if (Id is null) return;

        StringBuilder mensagemProblema = new StringBuilder();
        //Duplicar Ordem de Serviço
        var query = new GetOrdemServico(Id.Value);
        var resultado = await MessageBus.InvokeAsync<Result<OrdemServico?>>(query);

        if (!resultado.IsSuccess || resultado.Value is null)
        {
            await AlertService.ShowAlert("Atenção!", "Ordem de Serviço não encontrada!");
            NavigationManager.NavigateTo(Application.Routes.OrdemServicoListar);
            return;
        }

        var outraReceita = resultado.Value;

        OrdemServico.ClienteId = outraReceita.ClienteId;
        OrdemServico.VendedorId = outraReceita.VendedorId;

        OrdemServico.EmpresaId = outraReceita.EmpresaId;
        OrdemServico.Observacao = outraReceita.Observacao;
        OrdemServico.Receita = outraReceita.Receita;

        if (outraReceita.ProdutoServicoVendidos is not null)
        {
            // Carregar produtos usando handler
            try
            {
                var produtosQuery = new GetAllProdutos { IncluirProdutoPorEncomendado = false, EmpresaId = await GetEmpresaIdAsync() };
                var produtosResult = await MessageBus.InvokeAsync<FluentResults.Result<List<Produto>>>(produtosQuery);
                
                if (produtosResult.IsSuccess)
                {
                    var produtos = produtosResult.Value;
                    
                    foreach (var produtoVendido in outraReceita.ProdutoServicoVendidos)
                    {
                        var produto = produtos.FirstOrDefault(a => a.Id == produtoVendido.ProdutoId);
                        if (produto is not null)
                        {
                            var produtoVendidoNovo = new ProdutoServicoVendido()
                            {
                                Id = Guid.NewGuid(),
                                ProdutoId = produto.Id,
                                Produto = produto,
                                Quantidade = produtoVendido.Quantidade,
                                PrecoVenda = produtoVendido.PrecoVenda,
                                PrecoProduto = produtoVendido.PrecoProduto,
                                EmpresaId = await GetEmpresaIdAsync()
                            };

                            OrdemServico.ProdutoServicoVendidos!.Add(produtoVendidoNovo);
                        }
                        else
                        {
                            mensagemProblema.AppendLine($"Produto não encontrado: {produtoVendido.Produto?.Nome ?? "Nome não disponível"}");
                        }
                    }
                }
                else
                {
                    await AlertService.ShowAlert("Erro", string.Join("; ", produtosResult.Errors.Select(e => e.Message ?? "Erro desconhecido")));
                }
            }
            catch (Exception ex)
            {
                await AlertService.ShowAlert("Erro", $"Erro ao carregar produtos: {ex.Message}");
            }
        }

        if (mensagemProblema.Length > 0)
        {
            await AlertService.ShowAlert("Atenção!", mensagemProblema.ToString());
        }
    }

    private async Task Save()
    {
        if (OrdemServico is null) return;

        try
        {
            var entidade = (OrdemServico)OrdemServico.Clone();
            
            var empresa = await GetEmpresaAsync();
            if (empresa is null)
            {
                await AlertService.ShowAlert("Erro", "Empresa não encontrada!");
                return;
            }
            
            if (entidade.Receita is not null)
            {
                entidade.Receita.ClienteId = entidade.ClienteId;
                entidade.Receita.EmpresaId = empresa.Id;
            }

            entidade.EmpresaId = empresa.Id;

            if (string.IsNullOrWhiteSpace(entidade.NumeroIdentificador))
            {
                // Gerar número identificador usando repositório (pode ser movido para um handler específico)
                // Por enquanto, vamos usar um número temporário
                entidade.NumeroIdentificador = Guid.NewGuid().ToString("N")[..8];
            }

            if (entidade.ProdutoServicoVendidos is not null)
            {
                foreach (var produto in entidade.ProdutoServicoVendidos)
                {
                    produto.EmpresaId = empresa.Id;
                }
            }

            // Cria o comando
            var comando = new CreateOrdemServico
            {
                RegistroData = entidade.RegistroData,
                PrevisaoEntregaData = entidade.PrevisaoEntregaData,
                EntregaData = entidade.EntregaData,
                LaboratorioId = entidade.LaboratorioId,
                LaboratorioDataEnvio = entidade.LaboratorioDataEnvio,
                LaboratorioDataEntrega = entidade.LaboratorioDataEntrega,
                BaixaDataEntrega = entidade.BaixaDataEntrega,
                BaixaObservacao = entidade.BaixaObservacao,
                OrdemServicoUltimoStatus = entidade.OrdemServicoUltimoStatus,
                OrdemServicoUltimoStatusObservacao = entidade.OrdemServicoUltimoStatusObservacao,
                Receita = entidade.Receita,
                ImagemReceitaBytes = selectedReceitaImageFile != null ? await GetBytesFromBrowserFile(selectedReceitaImageFile) : null,
                ImagemReceitaContentType = selectedReceitaImageFile?.ContentType,
                
                // Propriedades herdadas de TransacaoComercial
                NumeroIdentificador = entidade.NumeroIdentificador,
                ClienteId = entidade.ClienteId,
                VendedorId = entidade.VendedorId,

                EmpresaId = entidade.EmpresaId,
                Observacao = entidade.Observacao,
                ProdutoServicoVendidos = entidade.ProdutoServicoVendidos,
                TransacaoFinanceira = entidade.TransacaoFinanceira,
                
                // Campos de auditoria
                CriadoPorId = await GetUsuarioIdLoggedAsync(),
                DataCriacao = DateTime.UtcNow
            };

            // Envia o comando via MessageBus
            var resultado = await MessageBus.InvokeAsync<Result>(comando);

            if (resultado.IsSuccess)
            {
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.OrdemServicoListar);
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", resultado.Errors.Select(e => e.Message ?? "Erro desconhecido")));
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowAlert("Erro", $"Erro ao salvar ordem de serviço: {ex.Message}");
        }
    }

    private async Task<byte[]> GetBytesFromBrowserFile(IBrowserFile file)
    {
        using var memoryStream = new MemoryStream();
        await file.OpenReadStream().CopyToAsync(memoryStream);
        return memoryStream.ToArray();
    }
}