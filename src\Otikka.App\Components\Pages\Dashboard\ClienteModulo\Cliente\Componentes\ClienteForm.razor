﻿@using System.Diagnostics
@using Otikka.App.Components.Generics
@using Otikka.Domain.Entities.PessoaModulo
@using Otikka.Domain.Enums
@using Otikka.Domain.Common.ExtensionMethods
@inherits PageBase
@if (<PERSON>essoa is not null)
{
    <div class="page-title-head d-flex align-items-sm-center flex-sm-row flex-column gap-2">
        <div class="flex-grow-1">
            <h4 class="fs-18 fw-semibold mb-0">@(EhEdicao ? "Editar" : "Cadastrar") Cliente</h4>
            <p class="text-muted mb-0">@(EhEdicao ? "Atualize as informações do cliente" : "Preencha os dados para cadastrar um novo cliente")</p>
        </div>
        <div class="d-flex gap-2">
            <a href="@Application.Routes.ClienteListar" class="btn btn-light">
                <i class="ti ti-arrow-left me-1"></i> Voltar
            </a>
        </div>
    </div>

    <EditForm Model="Pessoa" FormName="Register" OnValidSubmit="Submit">
        <FluentValidationValidator Options="@(options => options.IncludeRuleSets("ClienteValidacao"))" />
        <ValidationSummary />

        <!-- Card: Informações Básicas -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm me-3">
                        <span class="avatar-title bg-primary bg-opacity-10 rounded">
                            <i class="ti ti-user text-primary fs-20"></i>
                        </span>
                    </div>
                    <div>
                        <h5 class="card-title mb-0">Informações Básicas</h5>
                        <p class="text-muted mb-0">Dados principais do cliente</p>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row gy-4">
                    <div class="col-lg-3 col-md-6">
                        <div>
                            <label for="tipo" class="form-label">Tipo de Pessoa <span class="text-danger">*</span></label>
                            <InputSelect class="form-select" @bind-Value="Pessoa.Tipo" id="tipo" @onchange="OnTipoChanged">
                                @foreach (TipoPessoa tipo in Enum.GetValues(typeof(TipoPessoa)))
                                {
                                    <option value="@tipo">@tipo.GetDisplayName()</option>
                                }
                            </InputSelect>
                            <ValidationMessage For="() => Pessoa.Tipo" />
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div>
                            <label for="nome" class="form-label">@(Pessoa.Tipo == TipoPessoa.Juridica ? "Razão Social" : "Nome Completo") <span class="text-danger">*</span></label>
                            <InputText @bind-Value="Pessoa.Nome" autocomplete="not" class="form-control" id="nome" placeholder="@(Pessoa.Tipo == TipoPessoa.Juridica ? "Digite a razão social" : "Digite o nome completo")" />
                            <ValidationMessage For="() => Pessoa.Nome" />
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div>
                            <label for="documento" class="form-label">@(Pessoa.Tipo == TipoPessoa.Juridica ? "CNPJ" : "CPF")</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-id"></i></span>
                                <SfMaskedTextBox @bind-Value="Pessoa.Documento"
                                                 Mask="@(Pessoa.Tipo == TipoPessoa.Juridica ? "00.000.000/0000-00" : "000.000.000-00")"
                                                 ID="documento"
                                                 CssClass="input-group-masked"
                                                 Placeholder="@(Pessoa.Tipo == TipoPessoa.Juridica ? "00.000.000/0000-00" : "000.000.000-00")" />
                            </div>
                            <ValidationMessage For="() => Pessoa.Documento" />
                        </div>
                    </div>
                </div>

                @if (Pessoa.Tipo == TipoPessoa.Juridica)
                {
                    <div class="row gy-4 mt-3">
                        <div class="col-lg-6 col-md-6">
                            <div>
                                <label for="nomeFantasia" class="form-label">Nome Fantasia</label>
                                <InputText @bind-Value="Pessoa.NomeFantasia" autocomplete="not" class="form-control" id="nomeFantasia" placeholder="Digite o nome fantasia" />
                                <ValidationMessage For="() => Pessoa.NomeFantasia" />
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="row gy-4 mt-3">
                        <div class="col-lg-4 col-md-6">
                            <div>
                                <label for="nascimento" class="form-label">Data de Nascimento</label>
                                <InputDate @bind-Value="Pessoa.NascimentoData" autocomplete="not" class="form-control" id="nascimento" />
                                <ValidationMessage For="() => Pessoa.NascimentoData" />
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Card: Informações de Contato -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm me-3">
                        <span class="avatar-title bg-success bg-opacity-10 rounded">
                            <i class="ti ti-phone text-success fs-20"></i>
                        </span>
                    </div>
                    <div>
                        <h5 class="card-title mb-0">Informações de Contato</h5>
                        <p class="text-muted mb-0">E-mail e telefones para contato</p>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row gy-4">
                    <div class="col-lg-6 col-md-12">
                        <div>
                            <label for="email" class="form-label">E-mail</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-mail"></i></span>
                                <InputText @bind-Value="Pessoa.Email" autocomplete="not" class="form-control" id="email" placeholder="<EMAIL>" />
                            </div>
                            <ValidationMessage For="() => Pessoa.Email" />
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div>
                            <label for="telefone1" class="form-label">Celular</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-device-mobile"></i></span>
                                <SfMaskedTextBox @bind-Value="Pessoa.Telefone1" 
                                                 EnableLiterals=@true 
                                                 Mask="(00) 0 0000-0000" 
                                                 ID="telefone1" 
                                                 CssClass="input-group-masked"
                                                 Placeholder="(00) 0 0000-0000" />
                            </div>
                            <ValidationMessage For="() => Pessoa.Telefone1" />
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div>
                            <label for="telefone2" class="form-label">Telefone Fixo</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-phone"></i></span>
                                <SfMaskedTextBox @bind-Value="Pessoa.Telefone2" 
                                                 EnableLiterals=@true 
                                                 Mask="(00) 0000-0000" 
                                                 ID="telefone2" 
                                                 CssClass="input-group-masked"
                                                 Placeholder="(00) 0000-0000" />
                            </div>
                            <ValidationMessage For="() => Pessoa.Telefone2" />
                        </div>
                    </div>
                </div>

                <div class="row gy-4 mt-3">
                    <div class="col-12">
                        <div>
                            <label for="obs" class="form-label">Observações</label>
                            <InputTextArea @bind-Value="Pessoa.Observacao" autocomplete="not" class="form-control" id="obs" rows="4" placeholder="Informações adicionais sobre o cliente..." />
                            <ValidationMessage For="() => Pessoa.Observacao" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card: Endereço -->
        @if (Pessoa.Enderecos is not null)
        {
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm me-3">
                            <span class="avatar-title bg-info bg-opacity-10 rounded">
                                <i class="ti ti-map-pin text-info fs-20"></i>
                            </span>
                        </div>
                        <div>
                            <h5 class="card-title mb-0">Endereço</h5>
                            <p class="text-muted mb-0">Localização do cliente</p>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <EnderecoForm Endereco="Pessoa.Enderecos[0]" AoProcessar="AoProcessar" />
                </div>
            </div>
        }

        <!-- Card: Informações Fiscais (apenas para Pessoa Jurídica) -->
        @if (Pessoa.Tipo == TipoPessoa.Juridica)
        {
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-warning bg-opacity-10 rounded me-3">
                            <i class="ti ti-receipt-tax text-warning fs-20"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0">Informações Fiscais</h5>
                            <p class="text-muted mb-0">Dados tributários da empresa</p>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row gy-4">
                        <div class="col-lg-6 col-md-6">
                            <div>
                                <label for="inscricaoEstadual" class="form-label">Inscrição Estadual</label>
                                <InputText @bind-Value="Pessoa.InscricaoEstadualNumero" autocomplete="not" class="form-control" id="inscricaoEstadual" placeholder="Digite a inscrição estadual" />
                                <ValidationMessage For="() => Pessoa.InscricaoEstadualNumero" />
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div>
                                <label for="inscricaoMunicipal" class="form-label">Inscrição Municipal</label>
                                <InputText @bind-Value="Pessoa.InscricaoMunicipalNumero" autocomplete="not" class="form-control" id="inscricaoMunicipal" placeholder="Digite a inscrição municipal" />
                                <ValidationMessage For="() => Pessoa.InscricaoMunicipalNumero" />
                            </div>
                        </div>
                    </div>
                    <div class="row gy-4 mt-3">
                        <div class="col-lg-6 col-md-6">
                            <div>
                                <label for="suframa" class="form-label">Suframa</label>
                                <InputText @bind-Value="Pessoa.InscricaoSuframa" autocomplete="not" class="form-control" id="suframa" placeholder="Digite o código Suframa" />
                                <ValidationMessage For="() => Pessoa.InscricaoSuframa" />
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="d-flex align-items-center h-100">
                                <div class="form-check form-switch">
                                    <InputCheckbox @bind-Value="Pessoa.ContribuinteICMS" class="form-check-input" id="contribuinteICMS" />
                                    <label class="form-check-label" for="contribuinteICMS">
                                        <strong>Contribuinte ICMS</strong>
                                        <small class="d-block text-muted">Empresa contribuinte do ICMS</small>
                                    </label>
                                    <ValidationMessage For="() => Pessoa.ContribuinteICMS" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Card: Ações -->
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">@(EhEdicao ? "Atualizar Cliente" : "Cadastrar Cliente")</h6>
                        <p class="text-muted mb-0">@(EhEdicao ? "Confirme as alterações realizadas" : "Verifique os dados antes de salvar")</p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="@Application.Routes.ClienteListar" class="btn btn-light">
                            <i class="ti ti-arrow-left me-1"></i> Cancelar
                        </a>
                        <SubmitButton IsProcessing="Processing" ButtonClass="btn-primary">
                            <i class="ti ti-device-floppy me-1"></i>@(EhEdicao ? "Atualizar Cliente" : "Cadastrar Cliente")
                        </SubmitButton>
                    </div>
                </div>
            </div>
        </div>
    </EditForm>
}

<style>
    /* Estilos para integração do SfMaskedTextBox com input-group do Bootstrap */
    .input-group .input-group-masked {
        flex: 1 1 auto;
        width: 1%;
        min-width: 0;
        display: flex !important;
    }

    .input-group .input-group-masked .e-control.e-maskedtextbox {
        border-left: 0;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        height: calc(2.25rem + 2px);
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        line-height: 1.5;
        width: 100%;
        flex: 1 1 auto;
    }

    .input-group .input-group-masked .e-control.e-maskedtextbox:focus {
        box-shadow: none;
        border-color: #86b7fe;
    }

    .input-group:focus-within .input-group-text {
        border-color: #86b7fe;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075), 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    /* Remover espaçamentos e quebras de linha indesejadas */
    .input-group .input-group-masked {
        margin: 0;
        padding: 0;
    }

    /* Garantir que o container do SfMaskedTextBox não quebre a linha */
    .input-group .e-control-container.input-group-masked {
        display: flex !important;
        flex: 1 1 auto;
        width: 1%;
        min-width: 0;
        margin: 0;
        padding: 0;
    }
</style>

@code {
    [Parameter] public bool EhEdicao { get; set; }
    [Parameter] public Pessoa? Pessoa { get; set; }
    [Parameter] public EventCallback OnSave { get; set; }

    private async Task Submit()
    {
        await ProcessingChange(true);
        await OnSave.InvokeAsync();
        await ProcessingChange(false);
    }

    private async Task AoProcessar(bool status)
    {
        await ProcessingChange(status);
    }

    private void OnTipoChanged(ChangeEventArgs e)
    {
        if (Enum.TryParse<TipoPessoa>(e.Value?.ToString(), out var novoTipo))
        {
            Pessoa!.Tipo = novoTipo;

            // Limpar documento quando mudar o tipo
            Pessoa.Documento = string.Empty;

            // Se mudou para pessoa física, limpar campos específicos de PJ
            if (novoTipo == TipoPessoa.Fisica)
            {
                Pessoa.NomeFantasia = null;
                Pessoa.InscricaoEstadualNumero = null;
                Pessoa.InscricaoMunicipalNumero = null;
                Pessoa.InscricaoSuframa = null;
                Pessoa.ContribuinteICMS = false;
            }
            // Se mudou para pessoa jurídica, limpar data de nascimento
            else if (novoTipo == TipoPessoa.Juridica)
            {
                Pessoa.NascimentoData = null;
            }

            StateHasChanged();
        }
    }
}

