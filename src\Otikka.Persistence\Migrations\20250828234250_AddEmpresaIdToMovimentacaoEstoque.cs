﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Otikka.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddEmpresaIdToMovimentacaoEstoque : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "EmpresaId",
                table: "MovimentacoesEstoque",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_MovimentacoesEstoque_EmpresaId",
                table: "MovimentacoesEstoque",
                column: "EmpresaId");

            migrationBuilder.AddForeignKey(
                name: "FK_MovimentacoesEstoque_Empresas_EmpresaId",
                table: "MovimentacoesEstoque",
                column: "EmpresaId",
                principalTable: "Empresas",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MovimentacoesEstoque_Empresas_EmpresaId",
                table: "MovimentacoesEstoque");

            migrationBuilder.DropIndex(
                name: "IX_MovimentacoesEstoque_EmpresaId",
                table: "MovimentacoesEstoque");

            migrationBuilder.DropColumn(
                name: "EmpresaId",
                table: "MovimentacoesEstoque");
        }
    }
}
