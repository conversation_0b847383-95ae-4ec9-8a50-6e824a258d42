using Microsoft.EntityFrameworkCore;
using Otikka.Application.Contracts.Persistence;
using Otikka.Application.Models;
using Otikka.Application.Models.Requests;
using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo;
using Otikka.Domain.Enums;

namespace Otikka.Persistence.Repositories;

public class MovimentacaoEstoqueRepositorio : IMovimentacaoEstoqueRepositorio
{
    private readonly IDbContextFactory<ApplicationDbContext> _factory;

    public MovimentacaoEstoqueRepositorio(IDbContextFactory<ApplicationDbContext> factory)
    {
        _factory = factory;
    }

    public async Task<List<MovimentacaoEstoque>> ObterTudo(Guid empresaId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.MovimentacoesEstoque.AsQueryable();

            var items = await query
                .Include(m => m.Produto)
                .Where(m => m.Produto.EmpresaId == empresaId)
                .OrderByDescending(m => m.DataMovimentacao)
                .ToListAsync();

            return items;
        }
    }

    public async Task<PaginatedList<MovimentacaoEstoque>> ObterTudo(Guid empresaId, MovimentacaoEstoqueFiltroRequest filtro, PaginationParameters paginationParams)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.MovimentacoesEstoque
                .Include(m => m.Produto)
                .Where(m => m.Produto.EmpresaId == empresaId)
                .AsQueryable();

            // Aplicar filtros
            if (!string.IsNullOrWhiteSpace(filtro.PalavraBusca))
            {
                query = query.Where(m =>
                    m.Produto.Nome.Contains(filtro.PalavraBusca) ||
                    (m.Descricao != null && m.Descricao.Contains(filtro.PalavraBusca))
                );
            }

            if (filtro.ProdutoId.HasValue && filtro.ProdutoId != Guid.Empty)
            {
                query = query.Where(m => m.ProdutoId == filtro.ProdutoId);
            }

            if (filtro.TipoMovimentacao.HasValue)
            {
                query = query.Where(m => m.TipoMovimentacao == filtro.TipoMovimentacao);
            }

            if (filtro.DataMovimentacaoInicio.HasValue)
            {
                query = query.Where(m => m.DataMovimentacao >= filtro.DataMovimentacaoInicio.Value);
            }

            if (filtro.DataMovimentacaoFim.HasValue)
            {
                query = query.Where(m => m.DataMovimentacao <= filtro.DataMovimentacaoFim.Value);
            }

            // Aplicar ordenação
            query = ApplySortMovimentacaoEstoque(query, paginationParams.SortColumn, paginationParams.Ascending);

            var items = await query
                .Skip((paginationParams.PageIndex - 1) * paginationParams.PageSize)
                .Take(paginationParams.PageSize)
                .ToListAsync();

            var count = await _db.MovimentacoesEstoque
                .Include(m => m.Produto)
                .Where(m => m.Produto.EmpresaId == empresaId)
                .Where(m => string.IsNullOrWhiteSpace(filtro.PalavraBusca) ||
                           m.Produto.Nome.Contains(filtro.PalavraBusca) ||
                           (m.Descricao != null && m.Descricao.Contains(filtro.PalavraBusca)))
                .CountAsync();

            int totalPages = (int)Math.Ceiling((decimal)count / paginationParams.PageSize);

            return new PaginatedList<MovimentacaoEstoque>(items, paginationParams.PageIndex, totalPages, paginationParams.SortColumn, paginationParams.Ascending);
        }
    }

    public async Task<List<MovimentacaoEstoque>> ObterPorEstoque(Guid produtoId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.MovimentacoesEstoque.AsQueryable();

            var items = await query
                .Include(m => m.Produto)
                .Where(m => m.ProdutoId == produtoId)
                .OrderByDescending(m => m.DataMovimentacao)
                .ToListAsync();

            return items;
        }
    }

    public async Task<List<MovimentacaoEstoque>> ObterPorProduto(Guid produtoId, Guid empresaId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.MovimentacoesEstoque.AsQueryable();

            var items = await query
                .Include(m => m.Produto)
                .Where(m => m.ProdutoId == produtoId && 
                           m.Produto.EmpresaId == empresaId)
                .OrderByDescending(m => m.DataMovimentacao)
                .ToListAsync();

            return items;
        }
    }

    public async Task<List<MovimentacaoEstoque>> ObterPorTipo(TipoMovimentacaoEstoque tipo, Guid empresaId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.MovimentacoesEstoque.AsQueryable();

            var items = await query
                .Include(m => m.Produto)
                .Where(m => m.TipoMovimentacao == tipo && 
                           m.Produto.EmpresaId == empresaId)
                .OrderByDescending(m => m.DataMovimentacao)
                .ToListAsync();

            return items;
        }
    }

    public async Task<List<MovimentacaoEstoque>> ObterPorPeriodo(DateTime dataInicio, DateTime dataFim, Guid empresaId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.MovimentacoesEstoque.AsQueryable();

            var items = await query
                .Include(m => m.Produto)
                .Where(m => m.DataMovimentacao >= dataInicio && 
                           m.DataMovimentacao <= dataFim &&
                           m.Produto.EmpresaId == empresaId)
                .OrderByDescending(m => m.DataMovimentacao)
                .ToListAsync();

            return items;
        }
    }

    public async Task<MovimentacaoEstoque?> Obter(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            return await _db.MovimentacoesEstoque
                .Include(m => m.Produto)
                .SingleOrDefaultAsync(m => m.Id == id);
        }
    }

    public async Task<MovimentacaoEstoque?> ObterSemTracking(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            return await _db.MovimentacoesEstoque
                .AsNoTracking()
                .Include(m => m.Produto)
                .SingleOrDefaultAsync(m => m.Id == id);
        }
    }

    public async Task<List<MovimentacaoEstoque>> ObterPorTransacaoComercial(Guid transacaoComercialId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            return await _db.MovimentacoesEstoque
                .Include(m => m.Produto)
                .Where(m => m.TransacaoComercialId == transacaoComercialId)
                .ToListAsync();
        }
    }

    public async Task Cadastrar(MovimentacaoEstoque entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();
        try
        {
            _db.MovimentacoesEstoque.Add(entity);
            
            // Atualizar o estoque corrente do produto
            var produto = await _db.Produtos.FindAsync(entity.ProdutoId);
            if (produto != null)
            {
                produto.QuantidadeEstoqueCorrente += entity.Quantidade;
                _db.Produtos.Update(produto);
            }

            await _db.SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// Cadastra uma movimentação de estoque sem atualizar o estoque corrente.
    /// Usado especificamente para registros de balanço inicial onde o estoque já foi definido.
    /// </summary>
    /// <param name="entity">Movimentação de estoque a ser cadastrada</param>
    public async Task CadastrarSemAtualizarEstoque(MovimentacaoEstoque entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        try
        {
            _db.MovimentacoesEstoque.Add(entity);
            await _db.SaveChangesAsync();
        }
        catch
        {
            throw;
        }
    }

    public async Task Atualizar(MovimentacaoEstoque entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();
        try
        {
            // Obter a movimentação original para reverter o estoque
            var movimentacaoOriginal = await _db.MovimentacoesEstoque
                .AsNoTracking()
                .SingleOrDefaultAsync(m => m.Id == entity.Id);

            if (movimentacaoOriginal != null)
            {
                var produto = await _db.Produtos.FindAsync(entity.ProdutoId);
                if (produto != null)
                {
                    // Reverter a movimentação original
                    produto.QuantidadeEstoqueCorrente -= movimentacaoOriginal.Quantidade;
                    // Aplicar a nova movimentação
                    produto.QuantidadeEstoqueCorrente += entity.Quantidade;
                    _db.Produtos.Update(produto);
                }
            }

            _db.MovimentacoesEstoque.Update(entity);
            await _db.SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task Excluir(Guid id)
    {
        var entity = await Obter(id);
        if (entity is not null)
        {
            await Excluir(entity);
        }
    }

    public async Task Excluir(MovimentacaoEstoque entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();
        try
        {
            // Reverter a movimentação do estoque
            var produto = await _db.Produtos.FindAsync(entity.ProdutoId);
            if (produto != null)
            {
                produto.QuantidadeEstoqueCorrente -= entity.Quantidade;
                _db.Produtos.Update(produto);
            }

            _db.MovimentacoesEstoque.Remove(entity);
            await _db.SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    private static IQueryable<MovimentacaoEstoque> ApplySortMovimentacaoEstoque(IQueryable<MovimentacaoEstoque> query, string sortColumn, bool ascending)
    {
        return sortColumn?.ToLower() switch
        {
            "produto" => ascending ? query.OrderBy(x => x.Produto.Nome) : query.OrderByDescending(x => x.Produto.Nome),
            "quantidade" => ascending ? query.OrderBy(x => x.Quantidade) : query.OrderByDescending(x => x.Quantidade),
            "datamovimentacao" => ascending ? query.OrderBy(x => x.DataMovimentacao) : query.OrderByDescending(x => x.DataMovimentacao),
            "tipomovimentacao" => ascending ? query.OrderBy(x => x.TipoMovimentacao) : query.OrderByDescending(x => x.TipoMovimentacao),
            _ => ascending ? query.OrderByDescending(x => x.DataMovimentacao) : query.OrderBy(x => x.DataMovimentacao)
        };
    }
}
