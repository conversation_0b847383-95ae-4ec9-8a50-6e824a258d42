namespace Otikka.Domain.Entities.Common;

/// <summary>
/// Entidade responsável por armazenar logs de auditoria de todas as mudanças no sistema
/// </summary>
public class AuditLog
{
    /// <summary>
    /// Identificador único do log de auditoria
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Nome da tabela que foi modificada
    /// </summary>
    public string NomeTabela { get; set; } = string.Empty;

    /// <summary>
    /// ID da entidade que foi modificada
    /// </summary>
    public string EntidadeId { get; set; } = string.Empty;

    /// <summary>
    /// Tipo de operação realizada (INSERT, UPDATE, DELETE)
    /// </summary>
    public string TipoOperacao { get; set; } = string.Empty;

    /// <summary>
    /// Valores antigos da entidade (JSON) - apenas para UPDATE e DELETE
    /// </summary>
    public string? ValoresAntigos { get; set; }

    /// <summary>
    /// Valores novos da entidade (JSON) - apenas para INSERT e UPDATE
    /// </summary>
    public string? ValoresNovos { get; set; }

    /// <summary>
    /// Campos que foram modificados (separados por vírgula) - apenas para UPDATE
    /// </summary>
    public string? CamposModificados { get; set; }

    /// <summary>
    /// ID do usuário que realizou a operação
    /// </summary>
    public Guid? UsuarioId { get; set; }

    /// <summary>
    /// Nome do usuário que realizou a operação
    /// </summary>
    public string? NomeUsuario { get; set; }

    /// <summary>
    /// ID da empresa (para sistemas multi-tenant)
    /// </summary>
    public Guid? EmpresaId { get; set; }

    /// <summary>
    /// Data e hora da operação
    /// </summary>
    public DateTimeOffset DataOperacao { get; set; } = DateTimeOffset.UtcNow;

    /// <summary>
    /// Endereço IP do usuário
    /// </summary>
    public string? EnderecoIP { get; set; }

    /// <summary>
    /// User Agent do navegador
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// Informações adicionais sobre a operação
    /// </summary>
    public string? InformacoesAdicionais { get; set; }

    /// <summary>
    /// Indica se o log foi processado com sucesso
    /// </summary>
    public bool Processado { get; set; } = false;

    /// <summary>
    /// Data de processamento do log
    /// </summary>
    public DateTimeOffset? DataProcessamento { get; set; }

    /// <summary>
    /// Mensagem de erro caso o processamento falhe
    /// </summary>
    public string? MensagemErro { get; set; }

    /// <summary>
    /// Número de tentativas de processamento
    /// </summary>
    public int TentativasProcessamento { get; set; } = 0;
}