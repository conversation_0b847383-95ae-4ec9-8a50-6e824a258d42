@using Otikka.App.Components.Pages.Dashboard.ProdutoModulo.MovimentacaoEstoqueModulo.Componentes
@using Otikka.Application.Features.MovimentacaoEstoque.Queries.GetMovimentacaoEstoque
@using Otikka.Application.Features.MovimentacaoEstoque.Commands.UpdateMovimentacaoEstoque
@using FluentResults
@using Wolverine
@attribute [Route(Rota)]
@inherits PageBase

<PageTitle>
    Movimentação de Estoque - Atualizar
</PageTitle>

<MovimentacaoEstoqueForm EhEdicao="@true" MovimentacaoEstoque="MovimentacaoEstoque" OnSave="Save" />

@code {
    private const string Rota = Application.Routes.MovimentacaoEstoqueEditar;
    [Parameter] public Guid Id { get; set; }

    [SupplyParameterFromForm] private UpdateMovimentacaoEstoque? MovimentacaoEstoque { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (MovimentacaoEstoque is null)
        {
            await LoadMovimentacaoEstoque();
        }
    }

    private async Task LoadMovimentacaoEstoque()
    {
        try
        {
            var result = await MessageBus.InvokeAsync<Result<Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo.MovimentacaoEstoque>>(new GetMovimentacaoEstoque(Id));
            
            if (result.IsSuccess && result.Value != null)
            {
                var movimentacao = result.Value;
                MovimentacaoEstoque = new UpdateMovimentacaoEstoque
                {
                    Id = movimentacao.Id,
                    ProdutoId = movimentacao.ProdutoId,
                    TipoMovimentacao = movimentacao.TipoMovimentacao,
                    DataMovimentacao = movimentacao.DataMovimentacao,
                    Quantidade = movimentacao.Quantidade,
                    CustoUnitario = movimentacao.CustoUnitario,
                    Descricao = movimentacao.Descricao,
                    MovimentacaoOriginal = movimentacao
                };
            }
            else
            {
                await AlertService.ShowError("Erro!", result.Errors.FirstOrDefault()?.Message ?? "Movimentação de estoque não encontrada");
                NavigationManager.NavigateTo(Application.Routes.MovimentacaoEstoqueListar);
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro!", $"Erro inesperado: {ex.Message}");
            NavigationManager.NavigateTo(Application.Routes.MovimentacaoEstoqueListar);
        }
    }

    private async Task Save()
    {
        try
        {
            var result = await MessageBus.InvokeAsync<Result<Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo.MovimentacaoEstoque>>(MovimentacaoEstoque!);

            if (result.IsSuccess)
            {
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.MovimentacaoEstoqueListar);
            }
            else
            {
                await AlertService.ShowError("Opps!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao atualizar movimentação de estoque");
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Opps!", $"Erro inesperado: {ex.Message}");
        }
    }
}
