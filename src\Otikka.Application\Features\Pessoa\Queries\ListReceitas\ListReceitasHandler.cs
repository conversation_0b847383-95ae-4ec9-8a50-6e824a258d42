using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;
using Otikka.Application.Models;
using FluentResults;
using Otikka.Domain.Entities.PessoaModulo;

namespace Otikka.Application.Features.Pessoa.Queries.ListReceitas;

public class ListReceitasHandler(
    IReceitaRepositorio receitaRepository,
    ILogger<ListReceitasHandler> logger)
{
    public async Task<Result<PaginatedList<Receita>>> <PERSON><PERSON>(ListReceitas req)
    {
        logger.LogInformation("Listando receitas do cliente: {ClienteId} - Empresa: {EmpresaId}, Página: {PageIndex}, Tamanho: {PageSize}, Busca: {SearchWord}", 
            req.ClienteId, req.EmpresaId, req.PageIndex, req.PageSize, req.SearchWord);

        try
        {
            var paginationParams = new PaginationParameters(
                req.PageIndex,
                req.PageSize,
                req.SearchWord ?? string.Empty,
                req.Sort<PERSON>olumn,
                req.Ascending);

            var receitas = await receitaRepository.ObterTudo(req.ClienteId, paginationParams);

            logger.LogInformation("Receitas listadas com sucesso. Total de itens: {Count}", receitas.Items.Count);
            return Result.Ok(receitas);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao listar receitas do cliente {ClienteId}: {Message}", req.ClienteId, ex.Message);
            return Result.Fail<PaginatedList<Receita>>($"Erro ao listar receitas: {ex.Message}");
        }
    }
}
