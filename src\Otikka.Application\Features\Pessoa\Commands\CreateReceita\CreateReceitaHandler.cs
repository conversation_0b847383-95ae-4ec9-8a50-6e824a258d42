using FluentValidation;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;
using FluentResults;

namespace Otikka.Application.Features.Pessoa.Commands.CreateReceita;

public class CreateReceitaHandler(
    IReceitaRepositorio receitaRepository,
    IValidator<CreateReceita> validator,
    ILogger<CreateReceitaHandler> logger)
{
    public async Task<Result> Handle(CreateReceita req)
    {
        logger.LogInformation("Iniciando cadastro de receita para cliente: {ClienteId}", req.ClienteId);

        // Validar o comando antes de processar
        var validationResult = await validator.ValidateAsync(req);
        if (!validationResult.IsValid)
        {
            var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
            logger.LogWarning("Falha na validação da receita para cliente {ClienteId}: {Errors}", req.ClienteId, string.Join("; ", errors));
            return Result.Fail(string.Join("; ", errors));
        }

        try
        {
            // Definir campos de auditoria
            req.DataCriacao = DateTimeOffset.Now;
            req.DataValidade = req.DataValidade ?? DateTime.Now.AddYears(1);

            // Cadastrar a receita
            await receitaRepository.Cadastrar(req);

            logger.LogInformation("Receita cadastrada com sucesso para cliente {ClienteId} - ID: {Id}", req.ClienteId, req.Id);

            return Result.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao cadastrar receita para cliente {ClienteId}: {Message}", req.ClienteId, ex.Message);
            return Result.Fail($"Erro ao cadastrar receita: {ex.Message}");
        }
    }
}
