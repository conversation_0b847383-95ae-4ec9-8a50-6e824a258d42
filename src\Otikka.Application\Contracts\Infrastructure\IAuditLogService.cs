using Otikka.Domain.Entities.Common;
using FluentResults;

namespace Otikka.Application.Contracts.Infrastructure;

/// <summary>
/// Interface para o serviço de auditoria que processa logs em background usando Hangfire
/// </summary>
public interface IAuditLogService
{
    /// <summary>
    /// Enfileira um log de auditoria para processamento em background
    /// </summary>
    /// <param name="auditLog">Dados do log de auditoria</param>
    /// <returns>Resultado da operação</returns>
    Task<Result> EnfileirarLogAsync(AuditLog auditLog);

    /// <summary>
    /// Processa um log de auditoria (executado pelo Hangfire)
    /// </summary>
    /// <param name="auditLogId">ID do log a ser processado</param>
    /// <returns>Resultado do processamento</returns>
    Task<Result> ProcessarLogAsync(Guid auditLogId);

    /// <summary>
    /// Processa múltiplos logs de auditoria em lote
    /// </summary>
    /// <param name="auditLogIds">IDs dos logs a serem processados</param>
    /// <returns>Resultado do processamento em lote</returns>
    Task<Result> ProcessarLogsEmLoteAsync(List<Guid> auditLogIds);

    /// <summary>
    /// Reprocessa logs que falharam no processamento
    /// </summary>
    /// <param name="maxTentativas">Número máximo de tentativas permitidas</param>
    /// <returns>Resultado do reprocessamento</returns>
    Task<Result> ReprocessarLogsFalhadosAsync(int maxTentativas = 3);

    /// <summary>
    /// Limpa logs antigos baseado em critérios de retenção
    /// </summary>
    /// <param name="diasRetencao">Número de dias para manter os logs</param>
    /// <returns>Resultado da limpeza</returns>
    Task<Result> LimparLogsAntigosAsync(int diasRetencao = 365);

    /// <summary>
    /// Obtém estatísticas dos logs de auditoria
    /// </summary>
    /// <param name="dataInicio">Data de início do período</param>
    /// <param name="dataFim">Data de fim do período</param>
    /// <returns>Estatísticas dos logs</returns>
    Task<Result<AuditLogEstatisticas>> ObterEstatisticasAsync(DateTimeOffset dataInicio, DateTimeOffset dataFim);
}

/// <summary>
/// Classe para retornar estatísticas dos logs de auditoria
/// </summary>
 public class AuditLogEstatisticas
{
    public int TotalLogs { get; set; }
    public int LogsProcessados { get; set; }
    public int LogsPendentes { get; set; }
    public int LogsFalhados { get; set; }
    public Dictionary<string, int> LogsPorTabela { get; set; } = new();
    public Dictionary<string, int> LogsPorOperacao { get; set; } = new();
    public Dictionary<string, int> LogsPorUsuario { get; set; } = new();
    public TimeSpan TempoMedioProcessamento { get; set; }
}