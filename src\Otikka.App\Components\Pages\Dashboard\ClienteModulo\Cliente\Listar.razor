@using Otikka.Application.Models
@using Otikka.Domain.Entities.PessoaModulo
@attribute [Route(Application.Routes.ClienteListar)]
@inherits PageBase
@inject Gestao.Dominio.Repositorios.IPessoaRepositorio PessoaRepositorio


<PageTitle>
    Cliente - Lista
</PageTitle>
<div class="card">
    <div class="card-header align-items-center d-flex">
        <h4 class="card-title mb-0 flex-grow-1">Cliente</h4>
        <div class="flex-shrink-0">
            <a href="@Application.Routes.ClienteCadastrar" class="btn btn-primary btn-sm material-shadow-none">
                <i class="ri-file-list-3-line align-middle"></i> Cadastrar
            </a>
        </div>
    </div><!-- end card header -->

    <div class="card-body">
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="Digite sua pesquisa" @bind="SearchWord">
                <button class="btn btn-outline-success material-shadow-none" type="button" @onclick="OnSearch">Pesquisar</button>
            </div>
        </div>
        <div class="table-responsive mt-4 table-margin-width-24">
            <table class="table table-borderless table-centered align-middle table-nowrap mb-0">
                <thead class="text-muted table-light">
                    <tr>
                        <th scope="col" style="cursor: pointer;" @onclick='() => OnSort("InstanciaNome")'>
                            Nome
                            @if (SortColumn == "InstanciaNome")
                            {
                                <i class="@(Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line") ms-1"></i>
                            }
                        </th>
                        <th scope="col" style="cursor: pointer;" @onclick='() => OnSort("Documento")'>
                            CPF
                            @if (SortColumn == "Documento")
                            {
                                <i class="@(Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line") ms-1"></i>
                            }
                        </th>
                        <th scope="col" style="cursor: pointer;" @onclick='() => OnSort("Email")'>
                            Email
                            @if (SortColumn == "Email")
                            {
                                <i class="@(Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line") ms-1"></i>
                            }
                        </th>
                        <th scope="col">Ação</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Paginated == null)
                    {
                        <tr>
                            <td colspan="4">Carregando...</td>
                        </tr>
                    }
                    else if (Paginated.Items.Count == 0)
                    {
                        <tr>
                            <td colspan="4">Nenhum registro!</td>
                        </tr>
                    }

                    else
                    {
                        @foreach (var item in Paginated.Items)
                        {
                            <tr>
                                <td>
                                    @item.Nome
                                </td>
                                <td>
                                    @if (!string.IsNullOrWhiteSpace(item.Documento))
                                    {
                                        @item.Documento.FormatarCpfCnpj()
                                    }
                                </td>
                                <td>
                                    @item.Email
                                </td>
                                <td>

                                    <a href="@Application.Routes.GerarRota(Application.Routes.ReceitaDoClienteListar, item.Id.ToString())" class="btn btn-sm btn-soft-secondary"><i class="bx bx-receipt me-1"></i> Receitas</a>

                                    <a href="@Application.Routes.GerarRota(Application.Routes.OrdemServicoVendasListar, item.Id.ToString())" class="btn btn-sm btn-soft-success ms-1"><i class="bx bx-receipt me-1"></i> O.S. e Venda</a>
                                    
                                    <div class="btn-group ms-2">
                                        <a href="@Application.Routes.GerarRota(Application.Routes.ClienteEditar, item.Id.ToString())" class="btn btn-sm btn-soft-info"><i class="ri-edit-line"></i></a>
                                        <a class="btn btn-sm btn-soft-danger" @onclick="() => Excluir(item)"><i class="ri-delete-bin-line"></i></a>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
        <Pagination Paginated="@Paginated" OnPageChanged="@OnPageChanged" />
    </div>
</div>

@code {

    private int PageIndex = 1;
    private int PageSize;
    private string SearchWord = string.Empty;
    private string SortColumn = string.Empty;
    private bool Ascending = true;
    private PaginatedList<Pessoa>? Paginated;

    protected override async Task OnInitializedAsync()
    {
        PageSize = Configuration.GetValue<int>("Pagination:PageSize");
        await LoadDataAsync();
    }

    private async Task Excluir(Pessoa entidade)
    {
        if (await AlertService.ShowConfirmDelete())
        {
            await PessoaRepositorio.Excluir(entidade);
            await LoadDataAsync();
        }
    }

    private async Task OnSearch()
    {
        PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task OnPageChanged(int pageNumber)
    {
        PageIndex = pageNumber;
        await LoadDataAsync();
    }

    private async Task OnSort(string columnName)
    {
        if (SortColumn == columnName)
        {
            // Se já está ordenando por esta coluna, inverte a direção
            Ascending = !Ascending;
        }
        else
        {
            // Se é uma nova coluna, define como crescente
            SortColumn = columnName;
            Ascending = true;
        }

        // Volta para a primeira página ao ordenar
        PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        var EmpresaId = await GetEmpresaIdAsync();
        var paginationParams = new PaginationParameters(PageIndex, PageSize, SearchWord, SortColumn, Ascending);
        Paginated = await PessoaRepositorio.ObterClientes(EmpresaId, paginationParams);
        StateHasChanged();
    }
}