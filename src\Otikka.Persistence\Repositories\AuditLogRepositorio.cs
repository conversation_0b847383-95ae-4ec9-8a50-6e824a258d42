using FluentResults;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Infrastructure;
using Otikka.Application.Contracts.Persistence;
using Otikka.Domain.Entities.Common;
namespace Otikka.Persistence.Repositories;

/// <summary>
/// Implementação do repositório para operações com logs de auditoria
/// </summary>
public class AuditLogRepositorio : IAuditLogRepositorio
{
    private readonly IDbContextFactory<ApplicationDbContext> _factory;
    private readonly ILogger<AuditLogRepositorio> _logger;

    public AuditLogRepositorio(IDbContextFactory<ApplicationDbContext> factory, ILogger<AuditLogRepositorio> logger)
    {
        _factory = factory;
        _logger = logger;
    }

    /// <summary>
    /// Adiciona um novo log de auditoria
    /// </summary>
    public async Task<Result> AdicionarAsync(AuditLog auditLog)
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            await _context.AuditLogs.AddAsync(auditLog);
            await _context.SaveChangesAsync();
            
            _logger.LogDebug("Log de auditoria adicionado - ID: {AuditLogId}", auditLog.Id);
            return Result.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao adicionar log de auditoria - ID: {AuditLogId}", auditLog.Id);
            return Result.Fail($"Erro ao adicionar log de auditoria: {ex.Message}");
        }
    }

    /// <summary>
    /// Atualiza um log de auditoria existente
    /// </summary>
    public async Task<Result> AtualizarAsync(AuditLog auditLog)
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            _context.AuditLogs.Update(auditLog);
            await _context.SaveChangesAsync();
            
            _logger.LogDebug("Log de auditoria atualizado - ID: {AuditLogId}", auditLog.Id);
            return Result.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar log de auditoria - ID: {AuditLogId}", auditLog.Id);
            return Result.Fail($"Erro ao atualizar log de auditoria: {ex.Message}");
        }
    }

    /// <summary>
    /// Obtém um log de auditoria por ID
    /// </summary>
    public async Task<AuditLog?> ObterPorIdAsync(Guid id)
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            return await _context.AuditLogs
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter log de auditoria por ID - ID: {AuditLogId}", id);
            return null;
        }
    }

    /// <summary>
    /// Obtém logs de auditoria que falharam no processamento
    /// </summary>
    public async Task<List<AuditLog>> ObterLogsFalhadosAsync(int maxTentativas = 3)
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            return await _context.AuditLogs
                .AsNoTracking()
                .Where(a => !a.Processado && 
                           a.TentativasProcessamento < maxTentativas && 
                           !string.IsNullOrEmpty(a.MensagemErro))
                .OrderBy(a => a.DataOperacao)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter logs falhados");
            return new List<AuditLog>();
        }
    }

    /// <summary>
    /// Remove logs antigos baseado na data limite
    /// </summary>
    public async Task<int> RemoverLogsAntigosAsync(DateTimeOffset dataLimite)
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            var logsAntigos = await _context.AuditLogs
                .Where(a => a.DataOperacao < dataLimite && a.Processado)
                .ToListAsync();

            if (logsAntigos.Any())
            {
                _context.AuditLogs.RemoveRange(logsAntigos);
                await _context.SaveChangesAsync();
            }

            return logsAntigos.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover logs antigos");
            return 0;
        }
    }

    /// <summary>
    /// Obtém estatísticas dos logs de auditoria em um período
    /// </summary>
    public async Task<AuditLogEstatisticas> ObterEstatisticasAsync(DateTimeOffset dataInicio, DateTimeOffset dataFim)
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            var query = _context.AuditLogs
                .AsNoTracking()
                .Where(a => a.DataOperacao >= dataInicio && a.DataOperacao <= dataFim);

            var totalLogs = await query.CountAsync();
            var logsProcessados = await query.CountAsync(a => a.Processado);
            var logsPendentes = await query.CountAsync(a => !a.Processado && string.IsNullOrEmpty(a.MensagemErro));
            var logsFalhados = await query.CountAsync(a => !a.Processado && !string.IsNullOrEmpty(a.MensagemErro));

            var operacoesPorTipo = await query
                .GroupBy(a => a.TipoOperacao)
                .Select(g => new { Tipo = g.Key, Quantidade = g.Count() })
                .ToDictionaryAsync(x => x.Tipo, x => x.Quantidade);

            var tabelasMaisAuditadas = await query
                .GroupBy(a => a.NomeTabela)
                .Select(g => new { Tabela = g.Key, Quantidade = g.Count() })
                .OrderByDescending(x => x.Quantidade)
                .Take(10)
                .ToDictionaryAsync(x => x.Tabela, x => x.Quantidade);

            return new AuditLogEstatisticas
            {
                TotalLogs = totalLogs,
                LogsProcessados = logsProcessados,
                LogsPendentes = logsPendentes,
                LogsFalhados = logsFalhados,
                LogsPorOperacao = operacoesPorTipo,
                LogsPorTabela = tabelasMaisAuditadas,
                LogsPorUsuario = new Dictionary<string, int>(),
                TempoMedioProcessamento = TimeSpan.Zero
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas de auditoria");
            return new AuditLogEstatisticas();
        }
    }

    /// <summary>
    /// Obtém logs de auditoria com filtros
    /// </summary>
    public async Task<(List<AuditLog> Logs, int Total)> ObterComFiltrosAsync(
        string? nomeTabela = null,
        string? tipoOperacao = null,
        Guid? usuarioId = null,
        Guid? empresaId = null,
        DateTimeOffset? dataInicio = null,
        DateTimeOffset? dataFim = null,
        int pagina = 1,
        int tamanhoPagina = 50)
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            var query = _context.AuditLogs.AsNoTracking();

            // Aplicar filtros
            if (!string.IsNullOrEmpty(nomeTabela))
                query = query.Where(a => a.NomeTabela.Contains(nomeTabela));

            if (!string.IsNullOrEmpty(tipoOperacao))
                query = query.Where(a => a.TipoOperacao == tipoOperacao);

            if (usuarioId.HasValue)
                query = query.Where(a => a.UsuarioId == usuarioId.Value);

            if (empresaId.HasValue)
                query = query.Where(a => a.EmpresaId == empresaId.Value);

            if (dataInicio.HasValue)
                query = query.Where(a => a.DataOperacao >= dataInicio.Value);

            if (dataFim.HasValue)
                query = query.Where(a => a.DataOperacao <= dataFim.Value);

            var total = await query.CountAsync();

            var logs = await query
                .OrderByDescending(a => a.DataOperacao)
                .Skip((pagina - 1) * tamanhoPagina)
                .Take(tamanhoPagina)
                .ToListAsync();

            return (logs, total);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter logs com filtros");
            return (new List<AuditLog>(), 0);
        }
    }

    /// <summary>
    /// Obtém logs de auditoria por entidade
    /// </summary>
    public async Task<List<AuditLog>> ObterPorEntidadeAsync(string nomeTabela, string entidadeId)
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            return await _context.AuditLogs
                .AsNoTracking()
                .Where(a => a.NomeTabela == nomeTabela && a.EntidadeId == entidadeId)
                .OrderByDescending(a => a.DataOperacao)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter logs por entidade - Tabela: {Tabela}, ID: {EntidadeId}", 
                nomeTabela, entidadeId);
            return new List<AuditLog>();
        }
    }

    /// <summary>
    /// Verifica se existem logs não processados
    /// </summary>
    public async Task<bool> ExistemLogsNaoProcessadosAsync()
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            return await _context.AuditLogs
                .AsNoTracking()
                .AnyAsync(a => !a.Processado);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar logs não processados");
            return false;
        }
    }

    /// <summary>
    /// Obtém contagem de logs por status de processamento
    /// </summary>
    public async Task<Dictionary<string, int>> ObterContagemPorStatusAsync()
    {
        try
        {
            using var _context = _factory.CreateDbContext();
            var resultado = await _context.AuditLogs
                .AsNoTracking()
                .GroupBy(a => a.Processado)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            return resultado.ToDictionary(kvp => kvp.Key ? "Processado" : "Pendente", kvp => kvp.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter contagem por status");
            return new Dictionary<string, int>();
        }
    }
}