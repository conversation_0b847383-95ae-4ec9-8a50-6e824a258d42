using FluentValidation;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;
using FluentResults;

namespace Otikka.Application.Features.Pessoa.Commands.DeleteReceita;

public class DeleteReceitaHandler(
    IReceitaRepositorio receitaRepository,
    IValidator<DeleteReceita> validator,
    ILogger<DeleteReceitaHandler> logger)
{
    public async Task<Result> Handle(DeleteReceita req)
    {
        logger.LogInformation("Iniciando exclusão de receita: {ReceitaId}", req.Id);

        // Validar o comando antes de processar
        var validationResult = await validator.ValidateAsync(req);
        if (!validationResult.IsValid)
        {
            var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
            logger.LogWarning("Falha na validação da exclusão da receita {ReceitaId}: {Errors}", req.Id, string.Join("; ", errors));
            return Result.Fail(string.Join("; ", errors));
        }

        try
        {
            // Verificar se a receita existe
            var receitaExistente = await receitaRepository.Obter(req.Id);
            if (receitaExistente == null)
            {
                logger.LogWarning("Receita não encontrada para exclusão: {ReceitaId}", req.Id);
                return Result.Fail("Receita não encontrada");
            }

            // Verificar se a receita pertence à empresa
            if (receitaExistente.EmpresaId != req.EmpresaId)
            {
                logger.LogWarning("Tentativa de excluir receita {ReceitaId} que não pertence à empresa {EmpresaId}", req.Id, req.EmpresaId);
                return Result.Fail("A receita não pertence à empresa");
            }

            // Excluir a receita
            await receitaRepository.Excluir(receitaExistente);

            logger.LogInformation("Receita excluída com sucesso: {ReceitaId}", req.Id);

            return Result.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao excluir receita {ReceitaId}: {Message}", req.Id, ex.Message);
            return Result.Fail($"Erro ao excluir receita: {ex.Message}");
        }
    }
}
