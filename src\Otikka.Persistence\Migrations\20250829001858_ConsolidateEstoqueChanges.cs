using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Otikka.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class ConsolidateEstoqueChanges : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // <PERSON><PERSON>, limpar dados órfãos antes de fazer as mudanças estruturais
            migrationBuilder.Sql(@"
                DELETE FROM ""MovimentacoesEstoque"" 
                WHERE ""EstoqueProdutoId"" NOT IN (
                    SELECT ""Id"" FROM ""EstoqueProdutos""
                );
                
                DELETE FROM ""EstoqueProdutos"" 
                WHERE ""ProdutoId"" NOT IN (
                    SELECT ""Id"" FROM ""Produtos""
                );
            ");

            migrationBuilder.DropForeignKey(
                name: "FK_MovimentacoesEstoque_EstoqueProdutos_EstoqueProdutoId",
                table: "MovimentacoesEstoque");

            // Primeiro, adicionar as novas colunas à tabela Produtos
            migrationBuilder.AddColumn<decimal>(
                name: "PrecoCusto",
                table: "Produtos",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "PrecoVenda",
                table: "Produtos",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "QuantidadeEstoqueCorrente",
                table: "Produtos",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "QuantidadeEstoqueMinimo",
                table: "Produtos",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            // Agora migrar dados de EstoqueProdutos para Produtos
            migrationBuilder.Sql(@"
                UPDATE ""Produtos"" 
                SET 
                    ""PrecoVenda"" = COALESCE(ep.""PrecoVenda"", 0),
                    ""PrecoCusto"" = ep.""PrecoCusto"",
                    ""QuantidadeEstoqueCorrente"" = COALESCE(ep.""QuantidadeEstoqueCorrente"", 0),
                    ""QuantidadeEstoqueMinimo"" = COALESCE(ep.""QuantidadeEstoqueMinimo"", 0)
                FROM ""EstoqueProdutos"" ep
                WHERE ""Produtos"".""Id"" = ep.""ProdutoId""
                AND ep.""DataExclusao"" IS NULL;
            ");

            migrationBuilder.DropTable(
                name: "EstoqueProdutos");

            migrationBuilder.RenameColumn(
                name: "EstoqueProdutoId",
                table: "MovimentacoesEstoque",
                newName: "ProdutoId");

            migrationBuilder.RenameIndex(
                name: "IX_MovimentacoesEstoque_EstoqueProdutoId",
                table: "MovimentacoesEstoque",
                newName: "IX_MovimentacoesEstoque_ProdutoId");

            // Limpar movimentações órfãs após renomear a coluna
            migrationBuilder.Sql(@"
                DELETE FROM ""MovimentacoesEstoque"" 
                WHERE ""ProdutoId"" NOT IN (
                    SELECT ""Id"" FROM ""Produtos""
                );
            ");

            migrationBuilder.AddForeignKey(
                name: "FK_MovimentacoesEstoque_Produtos_ProdutoId",
                table: "MovimentacoesEstoque",
                column: "ProdutoId",
                principalTable: "Produtos",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MovimentacoesEstoque_Produtos_ProdutoId",
                table: "MovimentacoesEstoque");

            migrationBuilder.DropColumn(
                name: "PrecoCusto",
                table: "Produtos");

            migrationBuilder.DropColumn(
                name: "PrecoVenda",
                table: "Produtos");

            migrationBuilder.DropColumn(
                name: "QuantidadeEstoqueCorrente",
                table: "Produtos");

            migrationBuilder.DropColumn(
                name: "QuantidadeEstoqueMinimo",
                table: "Produtos");

            migrationBuilder.RenameColumn(
                name: "ProdutoId",
                table: "MovimentacoesEstoque",
                newName: "EstoqueProdutoId");

            migrationBuilder.RenameIndex(
                name: "IX_MovimentacoesEstoque_ProdutoId",
                table: "MovimentacoesEstoque",
                newName: "IX_MovimentacoesEstoque_EstoqueProdutoId");

            migrationBuilder.CreateTable(
                name: "EstoqueProdutos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProdutoId = table.Column<Guid>(type: "uuid", nullable: false),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    PrecoCusto = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    PrecoVenda = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    QuantidadeEstoqueCorrente = table.Column<int>(type: "integer", nullable: false),
                    QuantidadeEstoqueMinimo = table.Column<int>(type: "integer", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EstoqueProdutos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Produtos_ProdutoId",
                        column: x => x.ProdutoId,
                        principalTable: "Produtos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_AtualizadorPorId",
                table: "EstoqueProdutos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_CriadorPorId",
                table: "EstoqueProdutos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_EmpresaId",
                table: "EstoqueProdutos",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_ExcluidoPorId",
                table: "EstoqueProdutos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_ProdutoId_EmpresaId",
                table: "EstoqueProdutos",
                columns: new[] { "ProdutoId", "EmpresaId" },
                unique: true,
                filter: "\"DataExclusao\" IS NULL");

            migrationBuilder.AddForeignKey(
                name: "FK_MovimentacoesEstoque_EstoqueProdutos_EstoqueProdutoId",
                table: "MovimentacoesEstoque",
                column: "EstoqueProdutoId",
                principalTable: "EstoqueProdutos",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
