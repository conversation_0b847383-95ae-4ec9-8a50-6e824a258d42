using FluentValidation;
using Microsoft.Extensions.Logging;
using Gestao.Dominio.Repositorios;

namespace Otikka.Application.Features.Pessoa.Commands.UpdateCliente;

public class UpdateClienteHandler(
    IPessoaRepositorio pessoaRepository,
    IValidator<UpdateCliente> validator,
    ILogger<UpdateClienteHandler> logger)
{
    public async Task<Result> Handle(UpdateCliente req)
    {
        logger.LogInformation("Iniciando atualização de cliente: {InstanciaNome} - ID: {Id}", req.Nome, req.Id);

        // Validar o comando antes de processar
        var validationResult = await validator.ValidateAsync(req, options => options.IncludeRuleSets("ClienteValidacao"));
        if (!validationResult.IsValid)
        {
            var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
            logger.LogWarning("Falha na validação do cliente {InstanciaNome}: {Errors}", req.Nome, string.Join("; ", errors));
            return Result.Fail(string.Join("; ", errors));
        }

        try
        {
            // Verificar se o cliente existe e buscar dados atuais para controle de concorrência
            var clienteExistente = await pessoaRepository.ObterParaEdicao(req.Id);
            if (clienteExistente == null)
            {
                logger.LogWarning("Cliente não encontrado para atualização - ID: {Id}", req.Id);
                return Result.Fail("Cliente não encontrado");
            }

            // Preservar valores importantes para controle de concorrência e auditoria
            req.Version = clienteExistente.Version; // CRÍTICO: preservar o Version atual
            req.DataCriacao = clienteExistente.DataCriacao;
            req.CriadoPorId = clienteExistente.CriadoPorId;
            req.EmpresaId = clienteExistente.EmpresaId;
            
            // Configurar propriedades de atualização
            req.DataAtualizacao = DateTimeOffset.Now;

            // Limpar endereços vazios
            if (req.Enderecos != null)
            {
                var enderecosValidos = req.Enderecos.Where(e => 
                    !string.IsNullOrWhiteSpace(e.CodigoPostal) || 
                    !string.IsNullOrWhiteSpace(e.Logradouro)).ToList();
                
                req.Enderecos = enderecosValidos.Any() ? enderecosValidos : null;
                
                if (req.Enderecos != null)
                {
                    foreach (var endereco in req.Enderecos)
                    {
                        if (endereco.Id == Guid.Empty)
                        {
                            endereco.DataCriacao = DateTimeOffset.Now;
                        }
                        else
                        {
                            endereco.DataAtualizacao = DateTimeOffset.Now;
                        }
                    }
                }
            }

            // Atualizar o cliente
            await pessoaRepository.Atualizar(req);
            logger.LogInformation("Cliente {InstanciaNome} atualizado com sucesso - ID: {Id}", req.Nome, req.Id);

            return Result.Ok();
        }
        catch (Exception ex) when (ex.GetType().Name == "DbUpdateConcurrencyException")
        {
            logger.LogWarning(ex, "Conflito de concorrência ao atualizar cliente {InstanciaNome}", req.Nome);
            return Result.Fail("O cliente foi modificado por outro usuário. Recarregue a página e tente novamente.");
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Entidade não encontrada"))
        {
            logger.LogWarning(ex, "Cliente não encontrado durante atualização: {InstanciaNome}", req.Nome);
            return Result.Fail("Cliente não encontrado ou foi removido por outro usuário.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao atualizar cliente {InstanciaNome}: {Message}", req.Nome, ex.Message);
            return Result.Fail($"Erro ao atualizar cliente: {ex.Message}");
        }
    }
} 