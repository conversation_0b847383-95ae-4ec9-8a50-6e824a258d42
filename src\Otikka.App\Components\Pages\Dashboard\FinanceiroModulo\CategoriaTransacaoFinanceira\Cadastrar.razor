@using Otikka.Application.Features.CategoriaFinanceira.Commands.CreateCategoriaFinanceira

@attribute [Route(Rota)]

@rendermode InteractiveServer

@inherits PageBase

<PageTitle>
    Categoria - Cadastro
</PageTitle>

<CategoriaTransacaoFinanceiraForm EhEdicao="@false" CategoriaTransacaoFinanceira="CategoriaTransacaoFinanceira" OnSave="Save"/>

@code {
    private const string Rota = Application.Routes.FinanceiroCategoriaCadastrar;
    
    [SupplyParameterFromForm]
    private Otikka.Domain.Entities.TransacaoFinanceiraModulo.CategoriaTransacaoFinanceira CategoriaTransacaoFinanceira { get; set; } = new() { };
    
    private async Task Save()
    {
        CategoriaTransacaoFinanceira.DataCriacao = DateTimeOffset.Now;
        CategoriaTransacaoFinanceira.EmpresaId = await GetEmpresaIdAsync();

        var comando = new CreateCategoriaFinanceira
        {
            Nome = CategoriaTransacaoFinanceira.Nome,
            EmpresaId = CategoriaTransacaoFinanceira.EmpresaId,
            DataCriacao = CategoriaTransacaoFinanceira.DataCriacao,
            CriadoPorId = await GetUsuarioIdLoggedAsync()
        };

        var resultado = await MessageBus.InvokeAsync<FluentResults.Result>(comando);

        if (resultado.IsSuccess)
        {
            await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
            NavigationManager.NavigateTo(Application.Routes.FinanceiroCategoriaListar);
        }
        else
        {
            await AlertService.ShowAlert("Erro", string.Join("; ", resultado.Errors.Select(e => e.Message)));
        }
    }
}