@attribute [Route(Application.Routes.ProdutoEditar)]
@inherits PageBase
@rendermode InteractiveServer

@using Otikka.App.Components.Pages.Dashboard.ProdutoModulo.Produto.Componentes
@using Otikka.Application.Features.Produto.Commands.UpdateProduto
@using Otikka.Application.Features.Produto.Queries.GetProduto
@using Otikka.Domain.Entities.ProdutoModulo

@inject ILogger<Editar> Logger

<PageTitle>
    Produto - Atualizar
</PageTitle>

@if (IsLoading)
{
    <div class="d-flex justify-content-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
    </div>
}
else
{
    <ProdutoForm EhEdicao="@true" Produto="Produto" OnSave="Save" @ref="produtoFormRef" />
}

@code {
    
    [Parameter] public Guid Id { get; set; }
    [SupplyParameterFromForm] private UpdateProduto Produto { get; set; } = new();
    private bool IsLoading = true;
    private ProdutoForm? produtoFormRef;

    protected override async Task OnParametersSetAsync()
    {
        Logger.LogInformation("Carregando produto para edição: {Id}", Id);

        var result = await MessageBus.InvokeAsync<Result<Produto>>(new GetProduto { Id = Id });

        if (result.IsSuccess && result.Value != null)
        {
            var produto = result.Value;
            
            // Mapear para UpdateProduto
            Produto = new UpdateProduto
            {
                Id = produto.Id,
                Version = produto.Version,
                Nome = produto.Nome,
                Codigo = produto.Codigo,
                SKU = produto.SKU,
                EAN = produto.EAN,
                CategoriaProdutoId = produto.CategoriaProdutoId,
                MarcaProdutoId = produto.MarcaProdutoId,
                FornecedorId = produto.FornecedorId,
                EmpresaId = produto.EmpresaId,
                Encomenda = produto.Encomenda,
                ControleEstoque = produto.ControleEstoque,
                EstoqueTotal = produto.EstoqueTotal,
                // Propriedades de estoque agora estão diretamente no produto
                QuantidadeEstoqueCorrente = produto.QuantidadeEstoqueCorrente,
                QuantidadeEstoqueMinimo = produto.QuantidadeEstoqueMinimo,
                PrecoVenda = produto.PrecoVenda,
                DataCriacao = produto.DataCriacao,
                DataAtualizacao = produto.DataAtualizacao,
                AtualizadoPorId = produto.AtualizadoPorId
            };

            // Verificar se pertence à empresa
            var empresaId = await GetEmpresaIdAsync();
            if (Produto.EmpresaId != empresaId)
            {
                await AlertService.ShowError("Opps!",$"O produto não pertence à empresa!");
                NavigationManager.NavigateTo(Application.Routes.ProdutoListar);
                return;
            }
        }
        else
        {
            await AlertService.ShowError("Opps!", "Produto não encontrado!");
            NavigationManager.NavigateTo(Application.Routes.ProdutoListar);
            return;
        }

        IsLoading = false;
    }

    private async Task Save()
    {
        Logger.LogInformation("Iniciando atualização de produto: {InstanciaNome} - ID: {Id}", Produto.Nome, Produto.Id);

        // Incluir o CustoUnitario do formulário no comando
        if (produtoFormRef != null)
        {
            Produto.CustoUnitario = produtoFormRef.CustoUnitario;
        }

        // Limpar dados de estoque durante a edição para evitar duplicação
        // Limpar propriedades de estoque se necessário
            Produto.QuantidadeEstoqueCorrente = 0;
            Produto.QuantidadeEstoqueMinimo = 0;
            Produto.PrecoVenda = 0;

        var result = await MessageBus.InvokeAsync<Result>(Produto);
        
        if (result.IsSuccess)
        {
            await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
            NavigationManager.NavigateTo(Application.Routes.ProdutoListar);
        }
        else
        {
            await AlertService.ShowError("Opps!",result.Errors.FirstOrDefault()?.Message ?? "Erro ao atualizar produto");
        }
    }
}