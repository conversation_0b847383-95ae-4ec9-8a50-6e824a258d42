@attribute [Route(Application.Routes.ReceitaListar)]
@using Otikka.Application.Features.Pessoa.Queries.ListAllReceitas
@using Otikka.Application.Features.Pessoa.Commands.DeleteReceita
@using Otikka.Application.Models
@using Otikka.Domain.Entities.PessoaModulo
@using FluentResults
@using Wolverine
@inherits PageBase
@inject ILogger<Listar> Logger

<PageTitle>Receitas - Otikka</PageTitle>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4 class="header-title">Receitas</h4>
        <div class="d-flex gap-2">
            <a href="@Application.Routes.ReceitaCadastrar" class="btn btn-primary btn-sm">
                <i class="ti ti-plus me-1"></i> Adicionar
            </a>
            <button type="button" class="btn btn-secondary btn-sm" @onclick="AtualizarLista">
                <i class="ti ti-refresh me-1"></i> Atualizar
            </button>
        </div>
    </div>
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Buscar por cliente ou profissional..." 
                           @bind="SearchWord" @onkeypress="OnKeyPress">
                    <button class="btn btn-primary" type="button" @onclick="Buscar">
                        <i class="ti ti-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex justify-content-end">
                    <select class="form-select" style="max-width: 200px;" @onchange="OnPageSizeChanged">
                        <option value="10" selected="@(PageSize == 10)">10 por página</option>
                        <option value="25" selected="@(PageSize == 25)">25 por página</option>
                        <option value="50" selected="@(PageSize == 50)">50 por página</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        @if (carregando)
        {
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Carregando...</span>
                </div>
                <p class="text-muted mt-2 mb-0">Carregando receitas...</p>
            </div>
        }
        else if (Paginated == null || !Paginated.Items.Any())
        {
            <div class="text-center py-5">
                <div class="avatar-lg mx-auto mb-4">
                    <div class="avatar-title bg-primary bg-opacity-10 text-primary rounded-circle">
                        <i class="ti ti-file-text fs-24"></i>
                    </div>
                </div>
                <h4 class="text-muted">Nenhuma receita encontrada</h4>
                <p class="text-muted mb-4">Não há receitas cadastradas no momento.</p>
                <a href="@Application.Routes.ReceitaCadastrar" class="btn btn-primary">
                    <i class="ti ti-plus me-1"></i> Cadastrar Primeira Receita
                </a>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-striped table-centered mb-0">
                    <thead>
                        <tr>
                            <th style="cursor: pointer;" @onclick="() => OnSort(nameof(Receita.Cliente.Nome))">
                                Cliente
                                @if (SortColumn == nameof(Receita.Cliente.Nome))
                                {
                                    <i class="ti @(Ascending ? "ti-chevron-up" : "ti-chevron-down")"></i>
                                }
                            </th>
                            <th style="cursor: pointer;" @onclick="() => OnSort(nameof(Receita.NomeProfissional))">
                                Profissional
                                @if (SortColumn == nameof(Receita.NomeProfissional))
                                {
                                    <i class="ti @(Ascending ? "ti-chevron-up" : "ti-chevron-down")"></i>
                                }
                            </th>
                            <th style="cursor: pointer;" @onclick="() => OnSort(nameof(Receita.DataValidade))">
                                Validade
                                @if (SortColumn == nameof(Receita.DataValidade))
                                {
                                    <i class="ti @(Ascending ? "ti-chevron-up" : "ti-chevron-down")"></i>
                                }
                            </th>
                            <th style="cursor: pointer;" @onclick="() => OnSort(nameof(Receita.DataCriacao))">
                                Data Criação
                                @if (SortColumn == nameof(Receita.DataCriacao))
                                {
                                    <i class="ti @(Ascending ? "ti-chevron-up" : "ti-chevron-down")"></i>
                                }
                            </th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var receita in Paginated.Items)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary bg-opacity-10 rounded me-3">
                                            <i class="ti ti-user text-primary fs-16"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">@receita.Cliente.Nome</h6>
                                            <small class="text-muted">@receita.Cliente.Email</small>
                                        </div>
                                    </div>
                                </td>
                                <td>@(receita.NomeProfissional ?? "-")</td>
                                <td>
                                    @if (receita.DataValidade.HasValue)
                                    {
                                        @receita.DataValidade.Value.ToString("dd/MM/yyyy")
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>@receita.DataCriacao.ToString("dd/MM/yyyy")</td>
                                <td>
                                    @if (receita.DataValidade.HasValue)
                                    {
                                        @if (receita.DataValidade.Value < DateTimeOffset.Now)
                                        {
                                            <span class="badge bg-danger">Vencida</span>
                                        }
                                        else if (receita.DataValidade.Value < DateTimeOffset.Now.AddDays(30))
                                        {
                                            <span class="badge bg-warning">Vence em breve</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-success">Válida</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Sem validade</span>
                                    }
                                </td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <a href="@Application.Routes.GerarRota(Application.Routes.ReceitaEditar, receita.Id.ToString())" 
                                           class="btn btn-sm btn-soft-primary" title="Editar">
                                            <i class="ti ti-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-soft-danger" 
                                                @onclick="() => ConfirmarExclusao(receita)" title="Excluir">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
    </div>
    @if (Paginated != null && Paginated.Items.Any())
    {
        <div class="card-footer d-flex justify-content-between align-items-center">
            <div>
                <span class="text-muted">
                    Mostrando @((PageIndex - 1) * PageSize + 1) a @(Math.Min(PageIndex * PageSize, Paginated.TotalCount)) 
                    de @Paginated.TotalCount registros
                </span>
            </div>
            <nav aria-label="Page navigation">
                <ul class="pagination pagination-sm mb-0">
                    <li class="page-item @(PageIndex == 1 ? "disabled" : "")">
                        <button class="page-link" @onclick="() => ChangePage(PageIndex - 1)" disabled="@(PageIndex == 1)">
                            <span aria-hidden="true">&laquo;</span>
                        </button>
                    </li>
                    @for (int i = Math.Max(1, PageIndex - 2); i <= Math.Min(Paginated.TotalPages, PageIndex + 2); i++)
                    {
                        <li class="page-item @(i == PageIndex ? "active" : "")">
                            <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                        </li>
                    }
                    <li class="page-item @(PageIndex == Paginated.TotalPages ? "disabled" : "")">
                        <button class="page-link" @onclick="() => ChangePage(PageIndex + 1)" disabled="@(PageIndex == Paginated.TotalPages)">
                            <span aria-hidden="true">&raquo;</span>
                        </button>
                    </li>
                </ul>
            </nav>
        </div>
    }
</div>

@code {
    private PaginatedList<Receita>? Paginated;
    private bool carregando = false;
    private string SearchWord = "";
    private int PageIndex = 1;
    private int PageSize = 10;
    private string SortColumn = "DataCriacao";
    private bool Ascending = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        carregando = true;
        StateHasChanged();

        try
        {
            var empresaId = await GetEmpresaIdAsync();
            var query = new ListAllReceitas
            {
                EmpresaId = empresaId,
                PageIndex = PageIndex,
                PageSize = PageSize,
                SearchWord = SearchWord,
                SortColumn = SortColumn,
                Ascending = Ascending
            };

            var result = await MessageBus.InvokeAsync<Result<PaginatedList<Receita>>>(query);

            if (result.IsSuccess)
            {
                Paginated = result.Value;
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", result.Errors.Select(e => e.Message)));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao carregar receitas");
            await AlertService.ShowAlert("Erro", "Erro ao carregar receitas");
        }
        finally
        {
            carregando = false;
            StateHasChanged();
        }
    }

    private async Task OnSort(string columnName)
    {
        if (SortColumn == columnName)
        {
            Ascending = !Ascending;
        }
        else
        {
            SortColumn = columnName;
            Ascending = true;
        }

        PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task ChangePage(int newPageIndex)
    {
        if (newPageIndex >= 1 && newPageIndex <= (Paginated?.TotalPages ?? 1))
        {
            PageIndex = newPageIndex;
            await LoadDataAsync();
        }
    }

    private async Task OnPageSizeChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out int newPageSize))
        {
            PageSize = newPageSize;
            PageIndex = 1;
            await LoadDataAsync();
        }
    }

    private async Task Buscar()
    {
        PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await Buscar();
        }
    }

    private async Task AtualizarLista()
    {
        await LoadDataAsync();
    }

    private async Task ConfirmarExclusao(Receita receita)
    {
        var confirmacao = await AlertService.ShowConfirm(
            "Confirmar Exclusão",
            $"Tem certeza que deseja excluir a receita do cliente {receita.Cliente.Nome}?",
            "Sim, Excluir",
            "Cancelar");

        if (confirmacao)
        {
            await ExcluirReceita(receita.Id);
        }
    }

    private async Task ExcluirReceita(Guid receitaId)
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();
            var command = new DeleteReceita
            {
                Id = receitaId,
                EmpresaId = empresaId
            };

            var result = await MessageBus.InvokeAsync<Result>(command);

            if (result.IsSuccess)
            {
                await AlertService.ShowAlert("Sucesso", "Receita excluída com sucesso!");
                await LoadDataAsync();
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", result.Errors.Select(e => e.Message)));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao excluir receita {ReceitaId}", receitaId);
            await AlertService.ShowAlert("Erro", "Erro ao excluir receita");
        }
    }
}
