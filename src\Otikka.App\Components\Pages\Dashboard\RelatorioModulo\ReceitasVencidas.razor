@using Otikka.Application.Models
@using Otikka.Domain.Entities.PessoaModulo
@using Otikka.Application.Features.Pessoa.Queries.ListClientesComReceitasVencidas
@attribute [Route(Application.Routes.RelatorioReceitasVencidas)]
@inherits PageBase

<PageTitle>
    Relatório - Clientes com Receitas Vencidas
</PageTitle>

<div class="card">
    <div class="card-header align-items-center d-flex">
        <h4 class="card-title mb-0 flex-grow-1">Clientes com Receitas Vencidas</h4>
        <div class="flex-shrink-0">
            <span class="badge bg-warning">
                <i class="ri-alarm-warning-line align-middle"></i> 
                @(Paginated?.Items.Count ?? 0) cliente(s) com receitas vencidas
            </span>
        </div>
    </div><!-- end card header -->

    <div class="card-body">
        <div class="row">
            <div class="col-lg-6">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Buscar por nome ou CPF" @bind="SearchWord">
                    <button class="btn btn-outline-success material-shadow-none" type="button" @onclick="OnSearch">
                        <i class="ri-search-line"></i> Pesquisar
                    </button>
                </div>
            </div>
        </div>
        
        <div class="table-responsive mt-4 table-margin-width-24">
            <table class="table table-borderless table-centered align-middle table-nowrap mb-0">
                <thead class="text-muted table-light">
                    <tr>
                        <th scope="col">Nome</th>
                        <th scope="col">CPF</th>
                        <th scope="col">Email</th>
                        <th scope="col">Telefone</th>
                        <th scope="col">Ação</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Paginated == null)
                    {
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Carregando...</span>
                                </div>
                            </td>
                        </tr>
                    }
                    else if (Paginated.Items.Count == 0)
                    {
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="ri-check-line fs-1 text-success"></i>
                                    <p class="mb-0">Nenhum cliente com receitas vencidas encontrado!</p>
                                </div>
                            </td>
                        </tr>
                    }
                    else
                    {
                        @foreach (var cliente in Paginated.Items)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0 me-2">
                                            <div class="avatar-xs">
                                                <div class="avatar-title bg-warning text-white rounded-circle fs-16">
                                                    <i class="ri-user-3-line"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-0">@cliente.Nome</h6>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if (!string.IsNullOrWhiteSpace(cliente.Documento))
                                    {
                                        @cliente.Documento.FormatarCpfCnpj()
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrWhiteSpace(cliente.Email))
                                    {
                                        @cliente.Email
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrWhiteSpace(cliente.Telefone1))
                                    {
                                        @cliente.Telefone1
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="@Application.Routes.GerarRota(Application.Routes.ReceitaDoClienteListar, cliente.Id.ToString())" 
                                           class="btn btn-sm btn-soft-warning">
                                            <i class="ri-prescription-line me-1"></i> Ver Receitas
                                        </a>
                                        <a href="@Application.Routes.GerarRota(Application.Routes.ClienteEditar, cliente.Id.ToString())" 
                                           class="btn btn-sm btn-soft-info">
                                            <i class="ri-edit-line me-1"></i> Editar
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
        <Pagination Paginated="@Paginated" OnPageChanged="@OnPageChanged" />
    </div>
</div>

@code {
    private int PageIndex = 1;
    private int PageSize;
    private string SearchWord = string.Empty;
    private PaginatedList<Pessoa>? Paginated;

    protected override async Task OnInitializedAsync()
    {
        PageSize = Configuration.GetValue<int>("Pagination:PageSize");
        await LoadDataAsync();
    }

    private async Task OnSearch()
    {
        PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task OnPageChanged(int pageNumber)
    {
        PageIndex = pageNumber;
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();
            
            var request = new ListClientesComReceitasVencidas
            {
                EmpresaId = empresaId,
                PageIndex = PageIndex,
                PageSize = PageSize,
                SearchWord = SearchWord
            };

            var result = await MessageBus.InvokeAsync<Result<PaginatedList<Pessoa>>>(request);

            if (result.IsSuccess)
            {
                Paginated = result.Value;
            }
            else
            {
                await AlertService.ShowError("Erro", $"Erro ao carregar clientes: {result.Errors.FirstOrDefault()?.Message}");
                Paginated = new PaginatedList<Pessoa>(new List<Pessoa>(), 1, 1);
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
            Paginated = new PaginatedList<Pessoa>(new List<Pessoa>(), 1, 1);
        }
        
        StateHasChanged();
    }
} 