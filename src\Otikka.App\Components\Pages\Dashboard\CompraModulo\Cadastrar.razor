@using Otikka.App.Components.Pages.Dashboard.CompraModulo.Componentes
@using Otikka.Application.Features.Compra.Commands.CreateCompra
@using Otikka.Domain.Entities.CompraModule
@using FluentResults
@using Wolverine
@attribute [Route(Application.Routes.CompraCadastrar)]
@inherits PageBase

<PageTitle>
    Compra - Cadastro
</PageTitle>

<CompraForm EhEdicao="@false" Compra="Compra" OnSave="Save" />

@code {

    [SupplyParameterFromForm] 
    private CreateCompra Compra { get; set; } = new() 
    {
        DataCompra = DateTime.Now, 
        ItensCompra = new List<ItemCompra>() 
    };

    private async Task Save()
    {
        try
        {
            // Campos de auditoria
            Compra.CriadoPorId = await GetUsuarioIdLoggedAsync();
            Compra.DataCriacao = DateTime.UtcNow;
            
            var result = await MessageBus.InvokeAsync<Result>(Compra);

            if (result.IsSuccess)
            {
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.CompraListar);
            }
            else
            {
                await AlertService.ShowError("Opps!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao cadastrar compra");
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Opps!", $"Erro inesperado: {ex.Message}");
        }
    }
}
