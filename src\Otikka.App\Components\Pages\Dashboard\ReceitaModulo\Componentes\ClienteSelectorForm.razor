@using Otikka.Application.Features.Pessoa.Queries.GetAllClientes
@using Otikka.Application.Features.Pessoa.Commands.CreateCliente
@using Otikka.Domain.Entities.PessoaModulo
@using Otikka.Domain.Entities.Common
@using Otikka.Domain.Common.ExtensionMethods
@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Cliente.Componentes
@using FluentResults
@using Wolverine
@using Blazored.Modal
@using Blazored.Modal.Services
@using Syncfusion.Blazor.DropDowns
@inherits PageBase
@inject ILogger<ClienteSelectorForm> Logger
@inject IModalService ModalService

<div class="col-12">
    <div>
        <label for="cliente" class="form-label">Cliente *</label>
        <div class="input-group flex-nowrap">
            @if (Clientes is not null)
            {
                <SfComboBox CssClass="sync-adapt-select" TValue="Guid" TItem="Pessoa" @bind-Value="ClienteId" 
                           DataSource="@Clientes" Placeholder="Escolha o cliente" FilterType="FilterType.Contains" 
                           AllowFiltering="true" ValueChange="OnClienteChanged">
                    <ComboBoxFieldSettings Text="Nome" Value="Id"></ComboBoxFieldSettings>
                    <ComboBoxTemplates TItem="Pessoa">
                        <NoRecordsTemplate>
                            <span class='norecord'>Nenhum cliente cadastrado</span>
                        </NoRecordsTemplate>
                    </ComboBoxTemplates>
                </SfComboBox>
            }
            <button class="btn btn-primary" type="button" @onclick="AbrirModalCliente" title="Cadastrar novo cliente">
                <i class="ti ti-plus"></i>
            </button>
        </div>
        <ValidationMessage For="() => ClienteId" />
        @if (!string.IsNullOrEmpty(ErrorMessage))
        {
            <div class="text-danger mt-1">
                <small>@ErrorMessage</small>
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public Guid ClienteId { get; set; }
    [Parameter] public EventCallback<Guid> ClienteIdChanged { get; set; }
    [Parameter] public EventCallback<Pessoa> OnClienteSelected { get; set; }

    private List<Pessoa>? Clientes;
    private new string? ErrorMessage;

    protected override async Task OnInitializedAsync()
    {
        await CarregarClientes();
    }

    private async Task CarregarClientes()
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();
            var clientesQuery = new GetAllClientes(empresaId);
            var clientesResult = await MessageBus.InvokeAsync<Result<List<Pessoa>>>(clientesQuery);

            if (clientesResult.IsSuccess)
            {
                Clientes = clientesResult.Value.Select(a => new Pessoa
                {
                    Id = a.Id,
                    Nome = $"{a.Nome} " + (a.Documento != null ? $"({a.Documento.FormatarCpfCnpj()})" : ""),
                    Email = a.Email
                }).ToList();
            }
            else
            {
                ErrorMessage = "Erro ao carregar clientes: " + string.Join("; ", clientesResult.Errors.Select(e => e.Message));
                Logger.LogError("Erro ao carregar clientes: {Errors}", string.Join("; ", clientesResult.Errors.Select(e => e.Message)));
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = "Erro ao carregar clientes";
            Logger.LogError(ex, "Erro ao carregar clientes");
        }
    }

    private async Task OnClienteChanged(ChangeEventArgs<Guid, Pessoa> args)
    {
        ClienteId = args.Value;
        await ClienteIdChanged.InvokeAsync(ClienteId);
        
        if (args.ItemData != null)
        {
            await OnClienteSelected.InvokeAsync(args.ItemData);
        }
    }

    private async Task AbrirModalCliente()
    {
        try
        {
            var modalParameters = new ModalParameters();
            modalParameters.Add(nameof(ClienteFormModal.Pessoa), new CreateCliente
            {
                Enderecos = new List<Endereco>() { new Endereco() }
            });

            var modalOptions = new ModalOptions()
            {
                Size = ModalSize.ExtraLarge,
                HideCloseButton = false,
                DisableBackgroundCancel = true
            };

            var modalReference = ModalService.Show<ClienteFormModal>("Cadastrar Cliente", modalParameters, modalOptions);
            var result = await modalReference.Result;

            if (result.Confirmed && result.Data is CreateCliente novoCliente)
            {
                Logger.LogInformation("Cliente cadastrado via modal: {Nome}", novoCliente.Nome);
                
                // Recarregar lista de clientes
                await CarregarClientes();
                
                // Selecionar o cliente recém-cadastrado
                ClienteId = novoCliente.Id;
                await ClienteIdChanged.InvokeAsync(ClienteId);
                
                // Encontrar o cliente na lista e notificar
                var clienteSelecionado = Clientes?.FirstOrDefault(c => c.Id == novoCliente.Id);
                if (clienteSelecionado != null)
                {
                    await OnClienteSelected.InvokeAsync(clienteSelecionado);
                }
                
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao abrir modal de cliente");
            ErrorMessage = "Erro ao abrir formulário de cliente";
        }
    }

    public async Task RecarregarClientes()
    {
        await CarregarClientes();
        StateHasChanged();
    }
}
