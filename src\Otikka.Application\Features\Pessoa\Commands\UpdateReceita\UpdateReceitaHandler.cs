using FluentValidation;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;
using FluentResults;

namespace Otikka.Application.Features.Pessoa.Commands.UpdateReceita;

public class UpdateReceitaHandler(
    IReceitaRepositorio receitaRepository,
    IValidator<UpdateReceita> validator,
    ILogger<UpdateReceitaHandler> logger)
{
    public async Task<Result> Handle(UpdateReceita req)
    {
        logger.LogInformation("Iniciando atualização de receita: {ReceitaId} para cliente: {ClienteId}", req.Id, req.ClienteId);

        // Validar o comando antes de processar
        var validationResult = await validator.ValidateAsync(req);
        if (!validationResult.IsValid)
        {
            var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
            logger.LogWarning("Falha na validação da receita {ReceitaId}: {Errors}", req.Id, string.Join("; ", errors));
            return Result.Fail(string.Join("; ", errors));
        }

        try
        {
            // Definir campos de auditoria
            req.DataAtualizacao = DateTimeOffset.Now;

            // Atualizar a receita (o repositório fará as verificações necessárias)
            await receitaRepository.Atualizar(req);

            logger.LogInformation("Receita atualizada com sucesso: {ReceitaId}", req.Id);

            return Result.Ok();
        }
        catch (InvalidOperationException ex)
        {
            logger.LogWarning("Receita não encontrada: {ReceitaId} - {Message}", req.Id, ex.Message);
            return Result.Fail("Receita não encontrada");
        }
        catch (UnauthorizedAccessException ex)
        {
            logger.LogWarning("Acesso negado ao atualizar receita {ReceitaId}: {Message}", req.Id, ex.Message);
            return Result.Fail("A receita não pertence à empresa");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao atualizar receita {ReceitaId}: {Message}", req.Id, ex.Message);
            return Result.Fail($"Erro ao atualizar receita: {ex.Message}");
        }
    }
}
