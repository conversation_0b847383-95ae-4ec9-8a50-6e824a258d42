﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Otikka.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class Init : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Usuarios",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Nome = table.Column<string>(type: "character varying(70)", maxLength: 70, nullable: false),
                    Email = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Foto = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Senha = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Usuarios", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CepCache",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Cep = table.Column<string>(type: "character varying(8)", maxLength: 8, nullable: false, comment: "Código postal (CEP) sem formatação"),
                    Logradouro = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false, comment: "InstanciaNome do logradouro"),
                    Complemento = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false, comment: "Complemento do endereço"),
                    Bairro = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "InstanciaNome do bairro"),
                    Localidade = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "InstanciaNome da cidade/localidade"),
                    UF = table.Column<string>(type: "character(2)", fixedLength: true, maxLength: 2, nullable: false, comment: "Unidade federativa (estado)"),
                    IBGE = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, comment: "Código IBGE"),
                    GIA = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, comment: "Código GIA"),
                    DDD = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false, comment: "Código DDD"),
                    SIAFI = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, comment: "Código SIAFI"),
                    UltimaConsulta = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, comment: "Data da última consulta"),
                    CepValido = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true, comment: "Indica se o CEP é válido"),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    CriadoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CepCache", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CepCache_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CepCache_Usuarios_CriadoPorId",
                        column: x => x.CriadoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CepCache_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Empresas",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RazaoSocial = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    NomeFantasia = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    Documento = table.Column<string>(type: "character varying(18)", maxLength: 18, nullable: true),
                    Sigla = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Logo = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    LogoLargura = table.Column<int>(type: "integer", nullable: true),
                    LogoAltura = table.Column<int>(type: "integer", nullable: true),
                    TipoUnidade = table.Column<int>(type: "integer", nullable: false),
                    EmpresaPaiId = table.Column<Guid>(type: "uuid", nullable: true),
                    Email = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Telefone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    UsuarioId = table.Column<Guid>(type: "uuid", nullable: true),
                    TipoNumeracaoOrdemServico = table.Column<int>(type: "integer", nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Empresas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Empresas_Empresas_EmpresaPaiId",
                        column: x => x.EmpresaPaiId,
                        principalTable: "Empresas",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Empresas_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Empresas_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Empresas_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Empresas_Usuarios_UsuarioId",
                        column: x => x.UsuarioId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "NFesEntrada",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Numero = table.Column<string>(type: "text", nullable: false),
                    DataEmissao = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    FornecedorCNPJ = table.Column<string>(type: "text", nullable: false),
                    ValorTotal = table.Column<decimal>(type: "numeric", nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    CriadoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NFesEntrada", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NFesEntrada_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_NFesEntrada_Usuarios_CriadoPorId",
                        column: x => x.CriadoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_NFesEntrada_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CategoriasProdutos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Nome = table.Column<string>(type: "character varying(70)", maxLength: 70, nullable: false),
                    CategoriaProdutoPaiId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CategoriasProdutos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CategoriasProdutos_CategoriasProdutos_CategoriaProdutoPaiId",
                        column: x => x.CategoriaProdutoPaiId,
                        principalTable: "CategoriasProdutos",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CategoriasProdutos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CategoriasProdutos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CategoriasProdutos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CategoriasProdutos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CategoriasTransacoesFinanceiras",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Nome = table.Column<string>(type: "character varying(70)", maxLength: 70, nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CategoriasTransacoesFinanceiras", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CategoriasTransacoesFinanceiras_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CategoriasTransacoesFinanceiras_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CategoriasTransacoesFinanceiras_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CategoriasTransacoesFinanceiras_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "FormasPagamento",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Nome = table.Column<string>(type: "character varying(70)", maxLength: 70, nullable: false),
                    Prioridade = table.Column<int>(type: "integer", nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FormasPagamento", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FormasPagamento_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_FormasPagamento_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_FormasPagamento_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_FormasPagamento_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MarcasProdutos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Nome = table.Column<string>(type: "character varying(70)", maxLength: 70, nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MarcasProdutos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MarcasProdutos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MarcasProdutos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MarcasProdutos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MarcasProdutos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Pessoas",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Tipo = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    ehCliente = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ehFornecedor = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ehLaboratorio = table.Column<bool>(type: "boolean", nullable: false),
                    ehTransportadora = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    Nome = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    NomeFantasia = table.Column<string>(type: "text", nullable: true),
                    Documento = table.Column<string>(type: "character varying(18)", maxLength: 18, nullable: true),
                    NascimentoData = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    Observacao = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Telefone1 = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Telefone2 = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Email = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ehRegimeSimples = table.Column<bool>(type: "boolean", nullable: false),
                    InscricaoEstadualNumero = table.Column<string>(type: "character varying(15)", maxLength: 15, nullable: true),
                    InscricaoMunicipalNumero = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    InscricaoSuframa = table.Column<string>(type: "character varying(14)", maxLength: 14, nullable: true),
                    ContribuinteICMS = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pessoas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Pessoas_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Pessoas_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Pessoas_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Pessoas_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UnidadesMedidaProdutos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Nome = table.Column<string>(type: "character varying(70)", maxLength: 70, nullable: false),
                    Sigla = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnidadesMedidaProdutos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UnidadesMedidaProdutos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_UnidadesMedidaProdutos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_UnidadesMedidaProdutos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_UnidadesMedidaProdutos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UsuariosEmpresas",
                columns: table => new
                {
                    UsuarioId = table.Column<Guid>(type: "uuid", nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false),
                    Perfil = table.Column<string>(type: "text", nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UsuariosEmpresas", x => new { x.EmpresaId, x.UsuarioId });
                    table.ForeignKey(
                        name: "FK_UsuariosEmpresas_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UsuariosEmpresas_Usuarios_UsuarioId",
                        column: x => x.UsuarioId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Compras",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FornecedorId = table.Column<Guid>(type: "uuid", nullable: false),
                    DataCompra = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    NFeEntradaId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Compras", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Compras_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Compras_NFesEntrada_NFeEntradaId",
                        column: x => x.NFeEntradaId,
                        principalTable: "NFesEntrada",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Compras_Pessoas_FornecedorId",
                        column: x => x.FornecedorId,
                        principalTable: "Pessoas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Compras_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Compras_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Compras_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Enderecos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Pais = table.Column<string>(type: "character varying(2)", maxLength: 2, nullable: false),
                    CodigoPostal = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Estado = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Cidade = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Bairro = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Logradouro = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Complemento = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Numero = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    UsuarioId = table.Column<Guid>(type: "uuid", nullable: true),
                    PessoaId = table.Column<Guid>(type: "uuid", nullable: true),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Enderecos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Enderecos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Enderecos_Pessoas_PessoaId",
                        column: x => x.PessoaId,
                        principalTable: "Pessoas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Enderecos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Enderecos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Enderecos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Enderecos_Usuarios_UsuarioId",
                        column: x => x.UsuarioId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "Produtos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CategoriaProdutoId = table.Column<Guid>(type: "uuid", nullable: true),
                    MarcaProdutoId = table.Column<Guid>(type: "uuid", nullable: true),
                    Encomenda = table.Column<bool>(type: "boolean", nullable: false),
                    ControleEstoque = table.Column<bool>(type: "boolean", nullable: false),
                    EstoqueTotal = table.Column<int>(type: "integer", nullable: true),
                    SKU = table.Column<string>(type: "character varying(25)", maxLength: 25, nullable: true),
                    EAN = table.Column<string>(type: "character varying(25)", maxLength: 25, nullable: true),
                    Codigo = table.Column<string>(type: "character varying(25)", maxLength: 25, nullable: false),
                    Nome = table.Column<string>(type: "character varying(75)", maxLength: 75, nullable: false),
                    FornecedorId = table.Column<Guid>(type: "uuid", nullable: true),
                    Ativo = table.Column<bool>(type: "boolean", nullable: false),
                    OrigemMercadoria = table.Column<int>(type: "integer", nullable: true, comment: "Origem da mercadoria conforme especificação da NFe/NFCe"),
                    NCM = table.Column<string>(type: "character varying(8)", maxLength: 8, nullable: true, comment: "Nomenclatura Comum do Mercosul - Código de classificação fiscal"),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Produtos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Produtos_CategoriasProdutos_CategoriaProdutoId",
                        column: x => x.CategoriaProdutoId,
                        principalTable: "CategoriasProdutos",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Produtos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Produtos_MarcasProdutos_MarcaProdutoId",
                        column: x => x.MarcaProdutoId,
                        principalTable: "MarcasProdutos",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Produtos_Pessoas_FornecedorId",
                        column: x => x.FornecedorId,
                        principalTable: "Pessoas",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Produtos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Produtos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Produtos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "TransacoesComerciais",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    NumeroIdentificador = table.Column<string>(type: "character varying(30)", maxLength: 30, nullable: true),
                    ClienteId = table.Column<Guid>(type: "uuid", nullable: false),
                    Observacao = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Discriminator = table.Column<string>(type: "character varying(21)", maxLength: 21, nullable: false),
                    VendedorId = table.Column<Guid>(type: "uuid", nullable: true),
                    RegistroData = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    PrevisaoEntregaData = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    EntregaData = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LaboratorioId = table.Column<Guid>(type: "uuid", nullable: true),
                    LaboratorioDataEnvio = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LaboratorioDataEntrega = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LaboratorioObservacao = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    BaixaDataEntrega = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    BaixaObservacao = table.Column<string>(type: "text", nullable: true),
                    OrdemServicoUltimoStatus = table.Column<string>(type: "character varying(30)", maxLength: 30, nullable: true),
                    OrdemServicoUltimoStatusObservacao = table.Column<string>(type: "text", nullable: true),
                    DataVenda = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransacoesComerciais", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TransacoesComerciais_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TransacoesComerciais_Pessoas_ClienteId",
                        column: x => x.ClienteId,
                        principalTable: "Pessoas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TransacoesComerciais_Pessoas_LaboratorioId",
                        column: x => x.LaboratorioId,
                        principalTable: "Pessoas",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TransacoesComerciais_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TransacoesComerciais_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TransacoesComerciais_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TransacoesComerciais_Usuarios_VendedorId",
                        column: x => x.VendedorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "EstoqueProdutos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProdutoId = table.Column<Guid>(type: "uuid", nullable: false),
                    Ativo = table.Column<bool>(type: "boolean", nullable: false),
                    PrecoCusto = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    PrecoVenda = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    QuantidadeEstoqueCorrente = table.Column<int>(type: "integer", nullable: false),
                    QuantidadeEstoqueMinimo = table.Column<int>(type: "integer", nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EstoqueProdutos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Produtos_ProdutoId",
                        column: x => x.ProdutoId,
                        principalTable: "Produtos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EstoqueProdutos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ItensCompra",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CompraId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProdutoId = table.Column<Guid>(type: "uuid", nullable: false),
                    Quantidade = table.Column<int>(type: "integer", nullable: false),
                    PrecoUnitario = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ItensCompra", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ItensCompra_Compras_CompraId",
                        column: x => x.CompraId,
                        principalTable: "Compras",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ItensCompra_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ItensCompra_Produtos_ProdutoId",
                        column: x => x.ProdutoId,
                        principalTable: "Produtos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ItensCompra_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ItensCompra_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ItensCompra_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ItensNFeEntrada",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    NFeEntradaId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProdutoId = table.Column<Guid>(type: "uuid", nullable: false),
                    Quantidade = table.Column<int>(type: "integer", nullable: false),
                    PrecoUnitario = table.Column<decimal>(type: "numeric", nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    CriadoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ItensNFeEntrada", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ItensNFeEntrada_NFesEntrada_NFeEntradaId",
                        column: x => x.NFeEntradaId,
                        principalTable: "NFesEntrada",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ItensNFeEntrada_Produtos_ProdutoId",
                        column: x => x.ProdutoId,
                        principalTable: "Produtos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ItensNFeEntrada_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ItensNFeEntrada_Usuarios_CriadoPorId",
                        column: x => x.CriadoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ItensNFeEntrada_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "OrdensServicoSituacoes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Situacao = table.Column<string>(type: "character varying(30)", maxLength: 30, nullable: false),
                    OrdemServicoId = table.Column<Guid>(type: "uuid", nullable: false),
                    DataCriado = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrdensServicoSituacoes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrdensServicoSituacoes_TransacoesComerciais_OrdemServicoId",
                        column: x => x.OrdemServicoId,
                        principalTable: "TransacoesComerciais",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrdensServicoSituacoes_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_OrdensServicoSituacoes_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_OrdensServicoSituacoes_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ProdutoServicoVendidos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProdutoId = table.Column<Guid>(type: "uuid", nullable: false),
                    TransacaoComercialBaseId = table.Column<Guid>(type: "uuid", nullable: true),
                    TransacaoComercialId = table.Column<Guid>(type: "uuid", nullable: false),
                    PrecoVenda = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    PrecoProduto = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    PrecoCusto = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    Quantidade = table.Column<int>(type: "integer", nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProdutoServicoVendidos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProdutoServicoVendidos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ProdutoServicoVendidos_Produtos_ProdutoId",
                        column: x => x.ProdutoId,
                        principalTable: "Produtos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ProdutoServicoVendidos_TransacoesComerciais_TransacaoComerc~",
                        column: x => x.TransacaoComercialId,
                        principalTable: "TransacoesComerciais",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ProdutoServicoVendidos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ProdutoServicoVendidos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ProdutoServicoVendidos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Receitas",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    NomeProfissional = table.Column<string>(type: "text", nullable: true),
                    DataValidade = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    Adicao = table.Column<float>(type: "real", nullable: true),
                    EsfericoOlhoDireitoPerto = table.Column<float>(type: "real", nullable: true),
                    CilindricoOlhoDireitoPerto = table.Column<float>(type: "real", nullable: true),
                    EixoOlhoDireitoPerto = table.Column<int>(type: "integer", nullable: true),
                    AlturaOlhoDireitoPerto = table.Column<float>(type: "real", nullable: true),
                    DNPOlhoDireitoPerto = table.Column<float>(type: "real", nullable: true),
                    EsfericoOlhoDireitoLonge = table.Column<float>(type: "real", nullable: true),
                    CilindricoOlhoDireitoLonge = table.Column<float>(type: "real", nullable: true),
                    EixoOlhoDireitoLonge = table.Column<int>(type: "integer", nullable: true),
                    AlturaOlhoDireitoLonge = table.Column<float>(type: "real", nullable: true),
                    DNPOlhoDireitoLonge = table.Column<float>(type: "real", nullable: true),
                    EsfericoOlhoEsquerdoPerto = table.Column<float>(type: "real", nullable: true),
                    CilindricoOlhoEsquerdoPerto = table.Column<float>(type: "real", nullable: true),
                    EixoOlhoEsquerdoPerto = table.Column<int>(type: "integer", nullable: true),
                    AlturaOlhoEsquerdoPerto = table.Column<float>(type: "real", nullable: true),
                    DNPOlhoEsquerdoPerto = table.Column<float>(type: "real", nullable: true),
                    EsfericoOlhoEsquerdoLonge = table.Column<float>(type: "real", nullable: true),
                    CilindricoOlhoEsquerdoLonge = table.Column<float>(type: "real", nullable: true),
                    EixoOlhoEsquerdoLonge = table.Column<int>(type: "integer", nullable: true),
                    AlturaOlhoEsquerdoLonge = table.Column<float>(type: "real", nullable: true),
                    DNPOlhoEsquerdoLonge = table.Column<float>(type: "real", nullable: true),
                    ImagemReceitaUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true, comment: "URL da imagem da receita armazenada no AWS S3"),
                    ClienteId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrdemServicoId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Receitas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Receitas_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Receitas_Pessoas_ClienteId",
                        column: x => x.ClienteId,
                        principalTable: "Pessoas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Receitas_TransacoesComerciais_OrdemServicoId",
                        column: x => x.OrdemServicoId,
                        principalTable: "TransacoesComerciais",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Receitas_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Receitas_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Receitas_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "TransacoesFinanceiras",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TipoTransacaoFinanceira = table.Column<string>(type: "text", nullable: false),
                    Descricao = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    VencimentoData = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ValorTotal = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    ValorPago = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    ValorRestante = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    Repetir = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    RepetirVezes = table.Column<int>(type: "integer", nullable: true),
                    Observacao = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    CategoriaId = table.Column<Guid>(type: "uuid", nullable: true),
                    FornecedorId = table.Column<Guid>(type: "uuid", nullable: true),
                    TransacaoComercialId = table.Column<Guid>(type: "uuid", nullable: true),
                    TipoTransacaoComercial = table.Column<string>(type: "text", nullable: false),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransacoesFinanceiras", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TransacoesFinanceiras_CategoriasTransacoesFinanceiras_Categ~",
                        column: x => x.CategoriaId,
                        principalTable: "CategoriasTransacoesFinanceiras",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TransacoesFinanceiras_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TransacoesFinanceiras_Pessoas_FornecedorId",
                        column: x => x.FornecedorId,
                        principalTable: "Pessoas",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TransacoesFinanceiras_TransacoesComerciais_TransacaoComerci~",
                        column: x => x.TransacaoComercialId,
                        principalTable: "TransacoesComerciais",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TransacoesFinanceiras_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TransacoesFinanceiras_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TransacoesFinanceiras_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MovimentacoesEstoque",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EstoqueProdutoId = table.Column<Guid>(type: "uuid", nullable: false),
                    TipoMovimentacao = table.Column<string>(type: "text", nullable: false),
                    DataMovimentacao = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Quantidade = table.Column<int>(type: "integer", nullable: false),
                    PrecoUnitario = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    CompraId = table.Column<Guid>(type: "uuid", nullable: true),
                    ItemCompraId = table.Column<Guid>(type: "uuid", nullable: true),
                    TransacaoComercialId = table.Column<Guid>(type: "uuid", nullable: true),
                    TipoTransacaoComercial = table.Column<string>(type: "text", nullable: true),
                    Descricao = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MovimentacoesEstoque", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MovimentacoesEstoque_Compras_CompraId",
                        column: x => x.CompraId,
                        principalTable: "Compras",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_MovimentacoesEstoque_EstoqueProdutos_EstoqueProdutoId",
                        column: x => x.EstoqueProdutoId,
                        principalTable: "EstoqueProdutos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MovimentacoesEstoque_ItensCompra_ItemCompraId",
                        column: x => x.ItemCompraId,
                        principalTable: "ItensCompra",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_MovimentacoesEstoque_TransacoesComerciais_TransacaoComercia~",
                        column: x => x.TransacaoComercialId,
                        principalTable: "TransacoesComerciais",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_MovimentacoesEstoque_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MovimentacoesEstoque_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MovimentacoesEstoque_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Documentos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Nome = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Caminho = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    TransacaoFinanceiraId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Documentos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Documentos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Documentos_TransacoesFinanceiras_TransacaoFinanceiraId",
                        column: x => x.TransacaoFinanceiraId,
                        principalTable: "TransacoesFinanceiras",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Documentos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Documentos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Documentos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Pagamentos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FormaPagamentoId = table.Column<Guid>(type: "uuid", nullable: false),
                    Data = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    Valor = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    TransacaoComercialId = table.Column<Guid>(type: "uuid", nullable: true),
                    TransacaoFinanceiraId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataCriacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CriadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataAtualizacao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    AtualizadorPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DataExclusao = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    ExcluidoPorId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    EmpresaId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pagamentos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Pagamentos_Empresas_EmpresaId",
                        column: x => x.EmpresaId,
                        principalTable: "Empresas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Pagamentos_FormasPagamento_FormaPagamentoId",
                        column: x => x.FormaPagamentoId,
                        principalTable: "FormasPagamento",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Pagamentos_TransacoesComerciais_TransacaoComercialId",
                        column: x => x.TransacaoComercialId,
                        principalTable: "TransacoesComerciais",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Pagamentos_TransacoesFinanceiras_TransacaoFinanceiraId",
                        column: x => x.TransacaoFinanceiraId,
                        principalTable: "TransacoesFinanceiras",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Pagamentos_Usuarios_AtualizadorPorId",
                        column: x => x.AtualizadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Pagamentos_Usuarios_CriadorPorId",
                        column: x => x.CriadorPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Pagamentos_Usuarios_ExcluidoPorId",
                        column: x => x.ExcluidoPorId,
                        principalTable: "Usuarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CategoriasProdutos_AtualizadorPorId",
                table: "CategoriasProdutos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_CategoriasProdutos_CategoriaProdutoPaiId",
                table: "CategoriasProdutos",
                column: "CategoriaProdutoPaiId");

            migrationBuilder.CreateIndex(
                name: "IX_CategoriasProdutos_CriadorPorId",
                table: "CategoriasProdutos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_CategoriasProdutos_EmpresaId",
                table: "CategoriasProdutos",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_CategoriasProdutos_ExcluidoPorId",
                table: "CategoriasProdutos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_CategoriasTransacoesFinanceiras_AtualizadorPorId",
                table: "CategoriasTransacoesFinanceiras",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_CategoriasTransacoesFinanceiras_CriadorPorId",
                table: "CategoriasTransacoesFinanceiras",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_CategoriasTransacoesFinanceiras_EmpresaId",
                table: "CategoriasTransacoesFinanceiras",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_CategoriasTransacoesFinanceiras_ExcluidoPorId",
                table: "CategoriasTransacoesFinanceiras",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_CepCache_AtualizadorPorId",
                table: "CepCache",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_CepCache_Cep",
                table: "CepCache",
                column: "Cep",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CepCache_CriadoPorId",
                table: "CepCache",
                column: "CriadoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_CepCache_ExcluidoPorId",
                table: "CepCache",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_CepCache_UltimaConsulta",
                table: "CepCache",
                column: "UltimaConsulta");

            migrationBuilder.CreateIndex(
                name: "IX_Compras_AtualizadorPorId",
                table: "Compras",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Compras_CriadorPorId",
                table: "Compras",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Compras_EmpresaId",
                table: "Compras",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_Compras_ExcluidoPorId",
                table: "Compras",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Compras_FornecedorId",
                table: "Compras",
                column: "FornecedorId");

            migrationBuilder.CreateIndex(
                name: "IX_Compras_NFeEntradaId",
                table: "Compras",
                column: "NFeEntradaId");

            migrationBuilder.CreateIndex(
                name: "IX_Documentos_AtualizadorPorId",
                table: "Documentos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Documentos_CriadorPorId",
                table: "Documentos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Documentos_EmpresaId",
                table: "Documentos",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_Documentos_ExcluidoPorId",
                table: "Documentos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Documentos_TransacaoFinanceiraId",
                table: "Documentos",
                column: "TransacaoFinanceiraId");

            migrationBuilder.CreateIndex(
                name: "IX_Empresas_AtualizadorPorId",
                table: "Empresas",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Empresas_CriadorPorId",
                table: "Empresas",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Empresas_Documento",
                table: "Empresas",
                column: "Documento",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Empresas_EmpresaPaiId",
                table: "Empresas",
                column: "EmpresaPaiId");

            migrationBuilder.CreateIndex(
                name: "IX_Empresas_ExcluidoPorId",
                table: "Empresas",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Empresas_UsuarioId",
                table: "Empresas",
                column: "UsuarioId");

            migrationBuilder.CreateIndex(
                name: "IX_Enderecos_AtualizadorPorId",
                table: "Enderecos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Enderecos_CriadorPorId",
                table: "Enderecos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Enderecos_EmpresaId",
                table: "Enderecos",
                column: "EmpresaId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Enderecos_ExcluidoPorId",
                table: "Enderecos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Enderecos_PessoaId",
                table: "Enderecos",
                column: "PessoaId");

            migrationBuilder.CreateIndex(
                name: "IX_Enderecos_UsuarioId",
                table: "Enderecos",
                column: "UsuarioId");

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_AtualizadorPorId",
                table: "EstoqueProdutos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_CriadorPorId",
                table: "EstoqueProdutos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_EmpresaId",
                table: "EstoqueProdutos",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_ExcluidoPorId",
                table: "EstoqueProdutos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_EstoqueProdutos_ProdutoId_EmpresaId",
                table: "EstoqueProdutos",
                columns: new[] { "ProdutoId", "EmpresaId" },
                unique: true,
                filter: "\"DataExclusao\" IS NULL");

            migrationBuilder.CreateIndex(
                name: "IX_FormasPagamento_AtualizadorPorId",
                table: "FormasPagamento",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_FormasPagamento_CriadorPorId",
                table: "FormasPagamento",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_FormasPagamento_EmpresaId",
                table: "FormasPagamento",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_FormasPagamento_ExcluidoPorId",
                table: "FormasPagamento",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensCompra_AtualizadorPorId",
                table: "ItensCompra",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensCompra_CompraId",
                table: "ItensCompra",
                column: "CompraId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensCompra_CriadorPorId",
                table: "ItensCompra",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensCompra_EmpresaId",
                table: "ItensCompra",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensCompra_ExcluidoPorId",
                table: "ItensCompra",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensCompra_ProdutoId",
                table: "ItensCompra",
                column: "ProdutoId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensNFeEntrada_AtualizadorPorId",
                table: "ItensNFeEntrada",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensNFeEntrada_CriadoPorId",
                table: "ItensNFeEntrada",
                column: "CriadoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensNFeEntrada_ExcluidoPorId",
                table: "ItensNFeEntrada",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensNFeEntrada_NFeEntradaId",
                table: "ItensNFeEntrada",
                column: "NFeEntradaId");

            migrationBuilder.CreateIndex(
                name: "IX_ItensNFeEntrada_ProdutoId",
                table: "ItensNFeEntrada",
                column: "ProdutoId");

            migrationBuilder.CreateIndex(
                name: "IX_MarcasProdutos_AtualizadorPorId",
                table: "MarcasProdutos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_MarcasProdutos_CriadorPorId",
                table: "MarcasProdutos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_MarcasProdutos_EmpresaId",
                table: "MarcasProdutos",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_MarcasProdutos_ExcluidoPorId",
                table: "MarcasProdutos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_MovimentacoesEstoque_AtualizadorPorId",
                table: "MovimentacoesEstoque",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_MovimentacoesEstoque_CompraId",
                table: "MovimentacoesEstoque",
                column: "CompraId");

            migrationBuilder.CreateIndex(
                name: "IX_MovimentacoesEstoque_CriadorPorId",
                table: "MovimentacoesEstoque",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_MovimentacoesEstoque_EstoqueProdutoId",
                table: "MovimentacoesEstoque",
                column: "EstoqueProdutoId");

            migrationBuilder.CreateIndex(
                name: "IX_MovimentacoesEstoque_ExcluidoPorId",
                table: "MovimentacoesEstoque",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_MovimentacoesEstoque_ItemCompraId",
                table: "MovimentacoesEstoque",
                column: "ItemCompraId");

            migrationBuilder.CreateIndex(
                name: "IX_MovimentacoesEstoque_TransacaoComercialId",
                table: "MovimentacoesEstoque",
                column: "TransacaoComercialId");

            migrationBuilder.CreateIndex(
                name: "IX_NFesEntrada_AtualizadorPorId",
                table: "NFesEntrada",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_NFesEntrada_CriadoPorId",
                table: "NFesEntrada",
                column: "CriadoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_NFesEntrada_ExcluidoPorId",
                table: "NFesEntrada",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_OrdensServicoSituacoes_AtualizadorPorId",
                table: "OrdensServicoSituacoes",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_OrdensServicoSituacoes_CriadorPorId",
                table: "OrdensServicoSituacoes",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_OrdensServicoSituacoes_ExcluidoPorId",
                table: "OrdensServicoSituacoes",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_OrdensServicoSituacoes_OrdemServicoId",
                table: "OrdensServicoSituacoes",
                column: "OrdemServicoId");

            migrationBuilder.CreateIndex(
                name: "IX_Pagamentos_AtualizadorPorId",
                table: "Pagamentos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Pagamentos_CriadorPorId",
                table: "Pagamentos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Pagamentos_EmpresaId",
                table: "Pagamentos",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_Pagamentos_ExcluidoPorId",
                table: "Pagamentos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Pagamentos_FormaPagamentoId",
                table: "Pagamentos",
                column: "FormaPagamentoId");

            migrationBuilder.CreateIndex(
                name: "IX_Pagamentos_TransacaoComercialId",
                table: "Pagamentos",
                column: "TransacaoComercialId");

            migrationBuilder.CreateIndex(
                name: "IX_Pagamentos_TransacaoFinanceiraId",
                table: "Pagamentos",
                column: "TransacaoFinanceiraId");

            migrationBuilder.CreateIndex(
                name: "IX_Pessoas_AtualizadorPorId",
                table: "Pessoas",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Pessoas_CriadorPorId",
                table: "Pessoas",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Pessoas_EmpresaId",
                table: "Pessoas",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_Pessoas_ExcluidoPorId",
                table: "Pessoas",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Produtos_AtualizadorPorId",
                table: "Produtos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Produtos_CategoriaProdutoId",
                table: "Produtos",
                column: "CategoriaProdutoId");

            migrationBuilder.CreateIndex(
                name: "IX_Produtos_CriadorPorId",
                table: "Produtos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Produtos_EmpresaId",
                table: "Produtos",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_Produtos_ExcluidoPorId",
                table: "Produtos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Produtos_FornecedorId",
                table: "Produtos",
                column: "FornecedorId");

            migrationBuilder.CreateIndex(
                name: "IX_Produtos_MarcaProdutoId",
                table: "Produtos",
                column: "MarcaProdutoId");

            migrationBuilder.CreateIndex(
                name: "IX_ProdutoServicoVendidos_AtualizadorPorId",
                table: "ProdutoServicoVendidos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ProdutoServicoVendidos_CriadorPorId",
                table: "ProdutoServicoVendidos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ProdutoServicoVendidos_EmpresaId",
                table: "ProdutoServicoVendidos",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_ProdutoServicoVendidos_ExcluidoPorId",
                table: "ProdutoServicoVendidos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_ProdutoServicoVendidos_ProdutoId",
                table: "ProdutoServicoVendidos",
                column: "ProdutoId");

            migrationBuilder.CreateIndex(
                name: "IX_ProdutoServicoVendidos_TransacaoComercialId",
                table: "ProdutoServicoVendidos",
                column: "TransacaoComercialId");

            migrationBuilder.CreateIndex(
                name: "IX_Receitas_AtualizadorPorId",
                table: "Receitas",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Receitas_ClienteId",
                table: "Receitas",
                column: "ClienteId");

            migrationBuilder.CreateIndex(
                name: "IX_Receitas_CriadorPorId",
                table: "Receitas",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Receitas_EmpresaId",
                table: "Receitas",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_Receitas_ExcluidoPorId",
                table: "Receitas",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_Receitas_OrdemServicoId",
                table: "Receitas",
                column: "OrdemServicoId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesComerciais_AtualizadorPorId",
                table: "TransacoesComerciais",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesComerciais_ClienteId",
                table: "TransacoesComerciais",
                column: "ClienteId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesComerciais_CriadorPorId",
                table: "TransacoesComerciais",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesComerciais_EmpresaId",
                table: "TransacoesComerciais",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesComerciais_ExcluidoPorId",
                table: "TransacoesComerciais",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesComerciais_LaboratorioId",
                table: "TransacoesComerciais",
                column: "LaboratorioId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesComerciais_VendedorId",
                table: "TransacoesComerciais",
                column: "VendedorId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesFinanceiras_AtualizadorPorId",
                table: "TransacoesFinanceiras",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesFinanceiras_CategoriaId",
                table: "TransacoesFinanceiras",
                column: "CategoriaId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesFinanceiras_CriadorPorId",
                table: "TransacoesFinanceiras",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesFinanceiras_EmpresaId",
                table: "TransacoesFinanceiras",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesFinanceiras_ExcluidoPorId",
                table: "TransacoesFinanceiras",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesFinanceiras_FornecedorId",
                table: "TransacoesFinanceiras",
                column: "FornecedorId");

            migrationBuilder.CreateIndex(
                name: "IX_TransacoesFinanceiras_TransacaoComercialId",
                table: "TransacoesFinanceiras",
                column: "TransacaoComercialId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UnidadesMedidaProdutos_AtualizadorPorId",
                table: "UnidadesMedidaProdutos",
                column: "AtualizadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_UnidadesMedidaProdutos_CriadorPorId",
                table: "UnidadesMedidaProdutos",
                column: "CriadorPorId");

            migrationBuilder.CreateIndex(
                name: "IX_UnidadesMedidaProdutos_EmpresaId",
                table: "UnidadesMedidaProdutos",
                column: "EmpresaId");

            migrationBuilder.CreateIndex(
                name: "IX_UnidadesMedidaProdutos_ExcluidoPorId",
                table: "UnidadesMedidaProdutos",
                column: "ExcluidoPorId");

            migrationBuilder.CreateIndex(
                name: "IX_UsuariosEmpresas_UsuarioId",
                table: "UsuariosEmpresas",
                column: "UsuarioId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CepCache");

            migrationBuilder.DropTable(
                name: "Documentos");

            migrationBuilder.DropTable(
                name: "Enderecos");

            migrationBuilder.DropTable(
                name: "ItensNFeEntrada");

            migrationBuilder.DropTable(
                name: "MovimentacoesEstoque");

            migrationBuilder.DropTable(
                name: "OrdensServicoSituacoes");

            migrationBuilder.DropTable(
                name: "Pagamentos");

            migrationBuilder.DropTable(
                name: "ProdutoServicoVendidos");

            migrationBuilder.DropTable(
                name: "Receitas");

            migrationBuilder.DropTable(
                name: "UnidadesMedidaProdutos");

            migrationBuilder.DropTable(
                name: "UsuariosEmpresas");

            migrationBuilder.DropTable(
                name: "EstoqueProdutos");

            migrationBuilder.DropTable(
                name: "ItensCompra");

            migrationBuilder.DropTable(
                name: "FormasPagamento");

            migrationBuilder.DropTable(
                name: "TransacoesFinanceiras");

            migrationBuilder.DropTable(
                name: "Compras");

            migrationBuilder.DropTable(
                name: "Produtos");

            migrationBuilder.DropTable(
                name: "CategoriasTransacoesFinanceiras");

            migrationBuilder.DropTable(
                name: "TransacoesComerciais");

            migrationBuilder.DropTable(
                name: "NFesEntrada");

            migrationBuilder.DropTable(
                name: "CategoriasProdutos");

            migrationBuilder.DropTable(
                name: "MarcasProdutos");

            migrationBuilder.DropTable(
                name: "Pessoas");

            migrationBuilder.DropTable(
                name: "Empresas");

            migrationBuilder.DropTable(
                name: "Usuarios");
        }
    }
}
