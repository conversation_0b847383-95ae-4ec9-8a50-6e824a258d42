using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Otikka.Domain.Enums
{
    public enum TipoMovimentacaoEstoque
    {
        Entrada,    // A quantidade será positiva
        Saida,      // A quantidade será negativa
        Ajuste,     // A quantidade pode ser positiva ou negativa, dependendo da conferência
        Balanco     // Registro inicial de estoque quando produto é cadastrado pela primeira vez
    }
}
