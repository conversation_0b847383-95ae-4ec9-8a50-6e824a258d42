@using Microsoft.AspNetCore.Components.Web

<th scope="col" style="cursor: pointer;" @onclick="OnClick" class="@CssClass">
    @ChildContent
    @if (IsCurrentSortColumn)
    {
        <i class="@(Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line") ms-1"></i>
    }
</th>

@code {
    [Parameter] public string ColumnName { get; set; } = string.Empty;
    [Parameter] public string CurrentSortColumn { get; set; } = string.Empty;
    [Parameter] public bool Ascending { get; set; } = true;
    [Parameter] public EventCallback<string> OnSort { get; set; }
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public string CssClass { get; set; } = string.Empty;

    private bool IsCurrentSortColumn => CurrentSortColumn == ColumnName;

    private async Task OnClick()
    {
        await OnSort.InvokeAsync(ColumnName);
    }
}
