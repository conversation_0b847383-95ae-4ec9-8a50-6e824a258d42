﻿using Otikka.Application.Contracts.Persistence;
using Otikka.Application.Models;
using Otikka.Domain.Entities.PessoaModulo;

namespace Otikka.Persistence.Repositories;

public class ReceitaRepositorio : IReceitaRepositorio
{
    private readonly IDbContextFactory<ApplicationDbContext> _factory;

    public ReceitaRepositorio(IDbContextFactory<ApplicationDbContext> factory)
    {
        _factory = factory;
    }

    public async Task<List<Receita>> ObterTudo(Guid clienteId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.Receitas
                .Where(a => a.ClienteId == clienteId);

            var items = await query
                .OrderByDescending(a => a.DataCriacao)
                .ToListAsync();

            return items;
        }
    }

    public async Task<PaginatedList<Receita>> ObterTudo(Guid clienteId, PaginationParameters paginationParams)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.Receitas
                .Where(a => a.ClienteId == clienteId);

            // Aplicar ordenação
            query = ApplySortReceita(query, paginationParams.SortColumn, paginationParams.Ascending);

            var items = await query
                .Skip((paginationParams.PageIndex - 1) * paginationParams.PageSize)
                .Take(paginationParams.PageSize)
                .ToListAsync();

            var count = await query.CountAsync();

            int totalPages = (int)Math.Ceiling((decimal)count / paginationParams.PageSize);

            return new PaginatedList<Receita>(items, paginationParams.PageIndex, totalPages, count, paginationParams.SortColumn, paginationParams.Ascending);
        }
    }

    public async Task<PaginatedList<Receita>> ObterTodasReceitas(Guid empresaId, PaginationParameters paginationParams)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.Receitas
                .Include(r => r.Cliente)
                .Where(a => a.EmpresaId == empresaId);

            // Aplicar filtro de busca se fornecido
            if (!string.IsNullOrEmpty(paginationParams.SearchWord))
            {
                query = query.Where(r =>
                    r.Cliente.Nome.Contains(paginationParams.SearchWord) ||
                    r.NomeProfissional!.Contains(paginationParams.SearchWord));
            }

            // Aplicar ordenação
            query = ApplySortReceita(query, paginationParams.SortColumn, paginationParams.Ascending);

            var items = await query
                .Skip((paginationParams.PageIndex - 1) * paginationParams.PageSize)
                .Take(paginationParams.PageSize)
                .ToListAsync();

            var count = await query.CountAsync();

            int totalPages = (int)Math.Ceiling((decimal)count / paginationParams.PageSize);

            return new PaginatedList<Receita>(items, paginationParams.PageIndex, totalPages, count, paginationParams.SortColumn, paginationParams.Ascending);
        }
    }

    public async Task<Receita?> Obter(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            return await _db.Receitas.SingleOrDefaultAsync(a => a.Id == id);
        }
    }

    public async Task Cadastrar(Receita entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            _db.Receitas.Add(entity);
            await _db.SaveChangesAsync();
        }
    }

    public async Task Atualizar(Receita entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            _db.Receitas.Update(entity);
            await _db.SaveChangesAsync();
        }
    }

    public async Task Excluir(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var entity = await Obter(id);
            if (entity is not null)
                await Excluir(entity);
        }
    }

    public async Task Excluir(Receita entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        if (entity is not null)
        {
            _db.Receitas.Remove(entity);
            await _db.SaveChangesAsync();
        }
    }

    public async Task<PaginatedList<Pessoa>> ObterClientesComReceitasVencidas(Guid empresaId, PaginationParameters paginationParams)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var hoje = DateTimeOffset.Now;

            // Buscar clientes que possuem receitas
            var clientesComReceitas = _db.Pessoas
                .Where(p => p.EmpresaId == empresaId && p.ehCliente)
                .Where(p => _db.Receitas.Any(r => r.ClienteId == p.Id))
                .Select(p => new
                {
                    Cliente = p,
                    ReceitaMaisRecente = _db.Receitas
                        .Where(r => r.ClienteId == p.Id)
                        .OrderByDescending(r => r.DataCriacao)
                        .FirstOrDefault()
                })
                .Where(x => x.ReceitaMaisRecente != null &&
                           x.ReceitaMaisRecente.DataValidade.HasValue &&
                           x.ReceitaMaisRecente.DataValidade.Value < hoje);

            // Aplicar filtro de busca se fornecido
            if (!string.IsNullOrWhiteSpace(paginationParams.SearchWord))
            {
                clientesComReceitas = clientesComReceitas
                    .Where(x => x.Cliente.Nome.Contains(paginationParams.SearchWord) ||
                               (!string.IsNullOrWhiteSpace(x.Cliente.Documento) && x.Cliente.Documento.Contains(paginationParams.SearchWord)));
            }

            // Aplicar paginação
            var totalCount = await clientesComReceitas.CountAsync();

            var items = await clientesComReceitas
                .OrderBy(x => x.Cliente.Nome)
                .Skip((paginationParams.PageIndex - 1) * paginationParams.PageSize)
                .Take(paginationParams.PageSize)
                .Select(x => x.Cliente)
                .ToListAsync();

            int totalPages = (int)Math.Ceiling((decimal)totalCount / paginationParams.PageSize);

            return new PaginatedList<Pessoa>(items, paginationParams.PageIndex, totalPages, paginationParams.SortColumn, paginationParams.Ascending);
        }
    }

    private IQueryable<Receita> ApplySortReceita(IQueryable<Receita> query, string sortColumn, bool ascending)
    {
        if (string.IsNullOrWhiteSpace(sortColumn))
        {
            return query.OrderByDescending(a => a.DataCriacao);
        }

        return sortColumn.ToLower() switch
        {
            "datavalidade" => ascending ? query.OrderBy(a => a.DataValidade) : query.OrderByDescending(a => a.DataValidade),
            "datacriacao" => ascending ? query.OrderBy(a => a.DataCriacao) : query.OrderByDescending(a => a.DataCriacao),
            "nomeprofissional" => ascending ? query.OrderBy(a => a.NomeProfissional) : query.OrderByDescending(a => a.NomeProfissional),
            _ => query.OrderByDescending(a => a.DataCriacao)
        };
    }
}