using FluentValidation;

namespace Otikka.Application.Features.Pessoa.Commands.DeleteReceita;

public class DeleteReceitaValidator : AbstractValidator<DeleteReceita>
{
    public DeleteReceitaValidator()
    {
        // Validação do ID da receita
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("O ID da receita é obrigatório");

        // Validação do EmpresaId
        RuleFor(x => x.EmpresaId)
            .NotEmpty()
            .WithMessage("O ID da empresa é obrigatório");
    }
}
