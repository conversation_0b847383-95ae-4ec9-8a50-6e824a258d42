using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Otikka.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class RemovePrecoCustoFromProduto : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Remove a coluna PrecoCusto da tabela Produtos
            migrationBuilder.DropColumn(
                name: "PrecoCusto",
                table: "Produtos");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Adiciona a coluna PrecoCusto de volta à tabela Produtos
            migrationBuilder.AddColumn<decimal>(
                name: "PrecoCusto",
                table: "Produtos",
                type: "numeric",
                nullable: true);
        }
    }
}