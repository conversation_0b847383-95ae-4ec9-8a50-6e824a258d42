{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "EmailSender": {"Server": "smtp.gmail.com", "Port": 587, "SSL": true, "User": "<EMAIL>", "Password": "wprr qvev zpsm poyk"}, "AuditLog": {"Habilitado": true, "DiasRetencao": 90, "MaximoTentativas": 3, "IntervaloReprocessamento": 30, "TamanhoLote": 100, "CapturarValoresAntigos": true, "CapturarValoresNovos": true, "CapturarInformacoesUsuario": true, "CapturarInformacoesRequisicao": true, "TabelasIgnoradas": ["AuditLogs", "__EFMigrationsHistory", "HangfireJobs", "HangfireJobParameters", "HangfireJobQueue", "HangfireJobState", "HangfireServer", "HangfireSet", "HangfireCounter", "HangfireHash", "HangfireList", "HangfireAggregatedCounter"], "CamposIgnorados": ["DataCriacao", "DataAtualizacao", "CriadoPorId", "AtualizadoPorId", "<PERSON><PERSON>", "Hash", "Token", "RefreshToken"], "Operacoes": {"AuditarInsercoes": true, "AuditarAtualizacoes": true, "AuditarExclusoes": true, "AuditarExclusoesLogicas": true}}}