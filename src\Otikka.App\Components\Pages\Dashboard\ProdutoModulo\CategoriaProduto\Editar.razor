@attribute [Route(Application.Routes.CategoriaProdutoEditar)]
@inherits PageBase

@using Otikka.App.Components.Pages.Dashboard.ProdutoModulo.CategoriaProduto.Componentes
@using Otikka.Application.Features.CategoriaProduto.Commands.UpdateCategoriaProduto
@using Otikka.Application.Features.CategoriaProduto.Queries.GetCategoriaProduto

@inject ILogger<Editar> Logger

<PageTitle>
    Categoria de produtos - Atualizar
</PageTitle>

@if (IsLoading)
{
    <div class="d-flex justify-content-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
    </div>
}
else
{
    <CategoriaProdutoForm EhEdicao="@true" CategoriaProduto="CategoriaProduto" OnSave="Save" />
}

@code {
    [Parameter] public Guid Id { get; set; }
    [SupplyParameterFromForm] private UpdateCategoriaProduto CategoriaProduto { get; set; } = new();
    private bool IsLoading = true;

    protected override async Task OnParametersSetAsync()
    {
        Logger.LogInformation("Carregando categoria de produto para edição: {Id}", Id);

        var result = await MessageBus.InvokeAsync<Result<Domain.Entities.ProdutoModulo.CategoriaProduto>>(new GetCategoriaProduto { Id = Id });

        if (result.IsSuccess && result.Value != null)
        {
            var categoria = result.Value;
            
            // Mapear para UpdateCategoriaProduto
            CategoriaProduto = new UpdateCategoriaProduto
            {
                Id = categoria.Id,
                Version = categoria.Version,
                Nome = categoria.Nome,
                CategoriaProdutoPaiId = categoria.CategoriaProdutoPaiId,
                EmpresaId = categoria.EmpresaId,
                DataCriacao = categoria.DataCriacao,
                DataAtualizacao = categoria.DataAtualizacao,
                AtualizadoPorId = categoria.AtualizadoPorId
            };

            // Verificar se pertence à empresa
            var empresaId = await GetEmpresaIdAsync();
            if (CategoriaProduto.EmpresaId != empresaId)
            {
                await AlertService.ShowError("Opps!",$"A categoria não pertence à empresa!");
                NavigationManager.NavigateTo(Application.Routes.CategoriaProdutoListar);
                return;
            }
        }
        else
        {
            await AlertService.ShowError("Opps!", "Categoria não encontrada!");
            NavigationManager.NavigateTo(Application.Routes.CategoriaProdutoListar);
            return;
        }

        IsLoading = false;
    }

    private async Task Save()
    {
        Logger.LogInformation("Iniciando atualização de categoria de produto: {InstanciaNome} - ID: {Id}", CategoriaProduto.Nome, CategoriaProduto.Id);

        var result = await MessageBus.InvokeAsync<Result>(CategoriaProduto);
        
        if (result.IsSuccess)
        {
            await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
            NavigationManager.NavigateTo(Application.Routes.CategoriaProdutoListar);
        }
        else
        {
            await AlertService.ShowError("Opps!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao atualizar categoria");
        }
    }
}