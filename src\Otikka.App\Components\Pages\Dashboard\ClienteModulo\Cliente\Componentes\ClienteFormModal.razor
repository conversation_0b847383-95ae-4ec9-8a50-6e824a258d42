@using Otikka.Domain.Entities.Common
@using Otikka.Domain.Entities.PessoaModulo
@using Otikka.Domain.Enums
@using Otikka.Domain.Common.ExtensionMethods
@using Otikka.App.Components.Generics
@using Otikka.Application.Features.Pessoa.Commands.CreateCliente
@using FluentResults
@using Wolverine
@* @using Otikka.App.Components.Shared *@
@inherits PageBase
@inject ILogger<ClienteFormModal> Logger
@if (Pessoa is not null)
{
    <div class="row">
        <div class="col-lg-12">
            <div asp-validation-summary="All"></div>
            <EditForm Model="Pessoa" FormName="Register" OnValidSubmit="Submit">
                <FluentValidationValidator/>
                <div class="live-preview">
                    <!-- Informações Básicas -->
                    <div class="mb-4">
                        <h6 class="text-primary mb-3 border-bottom pb-2">
                            <i class="ti ti-user me-1"></i>Informações Básicas
                        </h6>
                        <div class="row gy-3">
                            <div class="col-md-4">
                                <div>
                                    <label for="tipo" class="form-label">Tipo <span class="text-danger">*</span></label>
                                    <InputSelect class="form-select" @bind-Value="Pessoa.Tipo" id="tipo" @onchange="OnTipoChanged">
                                        @foreach (TipoPessoa tipo in Enum.GetValues(typeof(TipoPessoa)))
                                        {
                                            <option value="@tipo">@tipo.GetDisplayName()</option>
                                        }
                                    </InputSelect>
                                    <ValidationMessage For="() => Pessoa.Tipo" />
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div>
                                    <label for="nome" class="form-label">@(Pessoa.Tipo == TipoPessoa.Juridica ? "Razão Social" : "Nome Completo") <span class="text-danger">*</span></label>
                                    <InputText @bind-Value="Pessoa.Nome" autocomplete="not" class="form-control" id="nome" placeholder="@(Pessoa.Tipo == TipoPessoa.Juridica ? "Digite a razão social" : "Digite o nome completo")"/>
                                    <ValidationMessage For="() => Pessoa.Nome"/>
                                </div>
                            </div>
                        </div>
                        <div class="row gy-3 mt-2">
                            <div class="col-md-6">
                                <div>
                                    <label for="documento" class="form-label">@(Pessoa.Tipo == TipoPessoa.Juridica ? "CNPJ" : "CPF")</label>
                                    <SfMaskedTextBox @bind-Value="Pessoa.Documento"
                                                     Mask="@(Pessoa.Tipo == TipoPessoa.Juridica ? "00.000.000/0000-00" : "000.000.000-00")"
                                                     autocomplete="not" class="form-control" id="documento"
                                                     Placeholder="@(Pessoa.Tipo == TipoPessoa.Juridica ? "00.000.000/0000-00" : "000.000.000-00")"/>
                                    <ValidationMessage For="() => Pessoa.Documento"/>
                                </div>
                            </div>
                            @if (Pessoa.Tipo == TipoPessoa.Juridica)
                            {
                                <div class="col-md-6">
                                    <div>
                                        <label for="nomeFantasia" class="form-label">Nome Fantasia</label>
                                        <InputText @bind-Value="Pessoa.NomeFantasia" autocomplete="not" class="form-control" id="nomeFantasia" placeholder="Digite o nome fantasia" />
                                        <ValidationMessage For="() => Pessoa.NomeFantasia" />
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="col-md-6">
                                    <div>
                                        <label for="nascimento" class="form-label">Data de Nascimento</label>
                                        <InputDate @bind-Value="Pessoa.NascimentoData" autocomplete="not" class="form-control" id="nascimento"/>
                                        <ValidationMessage For="() => Pessoa.NascimentoData"/>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Informações de Contato -->
                    <div class="mb-4">
                        <h6 class="text-success mb-3 border-bottom pb-2">
                            <i class="ti ti-phone me-1"></i>Informações de Contato
                        </h6>

                        <div class="row gy-3">
                            <div class="col-md-8">
                                <div>
                                    <label for="email" class="form-label">E-mail</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="ti ti-mail"></i></span>
                                        <InputText @bind-Value="Pessoa.Email" autocomplete="not" class="form-control" id="email" placeholder="<EMAIL>"/>
                                    </div>
                                    <ValidationMessage For="() => Pessoa.Email"/>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div>
                                    <label for="phone1" class="form-label">Telefone</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="ti ti-phone"></i></span>
                                        <SfMaskedTextBox Mask="(00) 00000-0000" @bind-Value="Pessoa.Telefone1" autocomplete="not" class="form-control" id="phone1" Placeholder="(00) 00000-0000"/>
                                    </div>
                                    <ValidationMessage For="() => Pessoa.Telefone1"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Endereço -->
                    @if (Pessoa.Enderecos is not null)
                    {
                        <div class="mb-4">
                            <h6 class="text-info mb-3 border-bottom pb-2">
                                <i class="ti ti-map-pin me-1"></i>Endereço
                            </h6>
                            <EnderecoForm Endereco="Pessoa.Enderecos[0]"/>
                        </div>
                    }

                    <!-- Informações Fiscais (apenas para Pessoa Jurídica) -->
                    @if (Pessoa.Tipo == TipoPessoa.Juridica)
                    {
                        <div class="mb-4">
                            <h6 class="text-warning mb-3 border-bottom pb-2">
                                <i class="ti ti-receipt-tax me-1"></i>Informações Fiscais
                            </h6>
                            <div class="row gy-3">
                                <div class="col-md-6">
                                    <div>
                                        <label for="inscricaoEstadual" class="form-label">Inscrição Estadual</label>
                                        <InputText @bind-Value="Pessoa.InscricaoEstadualNumero" autocomplete="not" class="form-control" id="inscricaoEstadual" placeholder="Digite a inscrição estadual" />
                                        <ValidationMessage For="() => Pessoa.InscricaoEstadualNumero" />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div>
                                        <label for="inscricaoMunicipal" class="form-label">Inscrição Municipal</label>
                                        <InputText @bind-Value="Pessoa.InscricaoMunicipalNumero" autocomplete="not" class="form-control" id="inscricaoMunicipal" placeholder="Digite a inscrição municipal" />
                                        <ValidationMessage For="() => Pessoa.InscricaoMunicipalNumero" />
                                    </div>
                                </div>
                            </div>
                            <div class="row gy-3 mt-2">
                                <div class="col-md-6">
                                    <div>
                                        <label for="suframa" class="form-label">Suframa</label>
                                        <InputText @bind-Value="Pessoa.InscricaoSuframa" autocomplete="not" class="form-control" id="suframa" placeholder="Digite o código Suframa" />
                                        <ValidationMessage For="() => Pessoa.InscricaoSuframa" />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center h-100">
                                        <div class="form-check form-switch">
                                            <InputCheckbox @bind-Value="Pessoa.ContribuinteICMS" class="form-check-input" id="contribuinteICMS" />
                                            <label class="form-check-label" for="contribuinteICMS">
                                                <strong>Contribuinte ICMS</strong>
                                                <small class="d-block text-muted">Empresa contribuinte do ICMS</small>
                                            </label>
                                            <ValidationMessage For="() => Pessoa.ContribuinteICMS" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" @onclick="Fechar">
                        <i class="ti ti-x me-1"></i> Cancelar
                    </button>
                    <SubmitButton IsProcessing="Processando" ButtonClass="btn-primary">
                        <i class="ti ti-device-floppy me-1"></i> Cadastrar Cliente
                    </SubmitButton>
                </div>
            </EditForm>
        </div>
    </div>
}

@code {
    public bool Processando { get; set; } = false;

    [Parameter] public CreateCliente Pessoa { get; set; } = new() { Enderecos = new List<Endereco>() { new Endereco() } };
    [Parameter] public EventCallback OnSave { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Garantir que Enderecos está inicializado
        if (Pessoa.Enderecos == null || !Pessoa.Enderecos.Any())
        {
            Pessoa.Enderecos = new List<Endereco>() { new Endereco() };
        }

        Pessoa.EmpresaId = await GetEmpresaIdAsync();
        Pessoa.CriadoPorId = await GetUsuarioIdLoggedAsync();
        Pessoa.ehCliente = true;
    }

    private async Task Submit()
    {
        try
        {
            await ProcessingChange(true);

            Logger.LogInformation("Iniciando cadastro de cliente via modal: {Nome}", Pessoa.Nome);

            // Preparar dados para o comando
            Pessoa.DataCriacao = DateTimeOffset.Now;

            // Se o endereço não tem CEP, remover da lista
            if (Pessoa.Enderecos != null && Pessoa.Enderecos.Any())
            {
                var primeiroEndereco = Pessoa.Enderecos.First();
                if (primeiroEndereco.CodigoPostal is null || primeiroEndereco.CodigoPostal.Trim().Length == 0)
                    Pessoa.Enderecos = null;
            }
            else
            {
                Pessoa.Enderecos = null;
            }

            var result = await MessageBus.InvokeAsync<Result>(Pessoa);

            if (result.IsSuccess)
            {
                Logger.LogInformation("Cliente cadastrado com sucesso via modal: {Nome}", Pessoa.Nome);
                await BlazoredModal.CloseAsync(ModalResult.Ok(Pessoa));
            }
            else
            {
                var errorMessage = result.Errors.FirstOrDefault()?.Message ?? "Erro ao cadastrar cliente";
                Logger.LogWarning("Falha ao cadastrar cliente via modal {Nome}: {Error}", Pessoa.Nome, errorMessage);
                await AlertService.ShowError("Erro de Validação", errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro inesperado ao cadastrar cliente via modal {Nome}: {Message}", Pessoa.Nome, ex.Message);
            await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
        }
        finally
        {
            await ProcessingChange(false);
        }
    }

    private async Task Fechar()
    {
        await BlazoredModal.CancelAsync();
    }

    private void OnTipoChanged(ChangeEventArgs e)
    {
        if (Enum.TryParse<TipoPessoa>(e.Value?.ToString(), out var novoTipo))
        {
            Pessoa!.Tipo = novoTipo;

            // Limpar documento quando mudar o tipo
            Pessoa.Documento = string.Empty;

            // Se mudou para pessoa física, limpar campos específicos de PJ
            if (novoTipo == TipoPessoa.Fisica)
            {
                Pessoa.NomeFantasia = null;
                Pessoa.InscricaoEstadualNumero = null;
                Pessoa.InscricaoMunicipalNumero = null;
                Pessoa.InscricaoSuframa = null;
                Pessoa.ContribuinteICMS = false;
            }
            // Se mudou para pessoa jurídica, limpar data de nascimento
            else if (novoTipo == TipoPessoa.Juridica)
            {
                Pessoa.NascimentoData = null;
            }

            StateHasChanged();
        }
    }
}