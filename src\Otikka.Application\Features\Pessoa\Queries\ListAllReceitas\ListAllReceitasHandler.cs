using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;
using Otikka.Application.Models;
using FluentResults;
using Otikka.Domain.Entities.PessoaModulo;

namespace Otikka.Application.Features.Pessoa.Queries.ListAllReceitas;

public class ListAllReceitasHandler(
    IReceitaRepositorio receitaRepository,
    ILogger<ListAllReceitasHandler> logger)
{
    public async Task<Result<PaginatedList<Receita>>> <PERSON>le(ListAllReceitas req)
    {
        logger.LogInformation("Listando todas as receitas - Empresa: {EmpresaId}, Página: {PageIndex}, Tamanho: {PageSize}, Busca: {SearchWord}", 
            req.EmpresaId, req.PageIndex, req.PageSize, req.SearchWord);

        try
        {
            var paginationParams = new PaginationParameters(
                req.PageIndex,
                req.PageSize,
                req.SearchWord ?? string.Empty,
                req.SortColumn,
                req.Ascending);

            var receitas = await receitaRepository.ObterTodasReceitas(req.EmpresaId, paginationParams);

            logger.LogInformation("Receitas listadas com sucesso. Total de itens: {Count}", receitas.Items.Count);
            return Result.Ok(receitas);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao listar receitas da empresa {EmpresaId}: {Message}", req.EmpresaId, ex.Message);
            return Result.Fail<PaginatedList<Receita>>($"Erro ao listar receitas: {ex.Message}");
        }
    }
}
