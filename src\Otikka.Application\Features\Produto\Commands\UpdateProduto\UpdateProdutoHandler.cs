using FluentValidation;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;
using FluentResults;
using Gestao.Dominio.Repositorios;

namespace Otikka.Application.Features.Produto.Commands.UpdateProduto;

public class UpdateProdutoHandler(
    IProdutoRepositorio produtoRepository,
    IValidator<UpdateProduto> validator,
    ILogger<UpdateProdutoHandler> logger)
{
    public async Task<Result> Handle(UpdateProduto req)
    {
        logger.LogInformation("Iniciando atualização de produto: {InstanciaNome} - ID: {Id}", req.Nome, req.Id);

        // Validar o comando antes de processar
        var validationResult = await validator.ValidateAsync(req, options => options.IncludeRuleSets("ProdutoValidacao"));
        if (!validationResult.IsValid)
        {
            var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
            logger.LogWarning("Falha na validação do produto {InstanciaNome}: {Errors}", req.Nome, string.Join("; ", errors));
            return Result.Fail(string.Join("; ", errors));
        }

        try
        {
            // Buscar o produto atual para garantir que o código não seja alterado
            var produtoAtual = await produtoRepository.Obter(req.Id);
            if (produtoAtual == null)
            {
                logger.LogWarning("Produto não encontrado para atualização - ID: {Id}", req.Id);
                return Result.Fail("Produto não encontrado");
            }

            // Criar entidade com os dados atualizados, mas mantendo o código original
            var produtoAtualizado = new Domain.Entities.ProdutoModulo.Produto
            {
                Id = req.Id,
                Nome = req.Nome,
                Codigo = produtoAtual.Codigo, // Mantém o código original
                SKU = req.SKU,
                EAN = req.EAN,
                CategoriaProdutoId = req.CategoriaProdutoId,
                MarcaProdutoId = req.MarcaProdutoId,
                FornecedorId = req.FornecedorId,
                Encomenda = req.Encomenda,
                ControleEstoque = req.ControleEstoque,
                DataAtualizacao = DateTimeOffset.Now,
                Ativo = req.Ativo, // Permite alteração do status ativo/inativo
                // Manter os valores atuais de estoque (não alterar durante edição)
                EstoqueTotal = produtoAtual.EstoqueTotal,
                QuantidadeEstoqueCorrente = produtoAtual.QuantidadeEstoqueCorrente,
                QuantidadeEstoqueMinimo = produtoAtual.QuantidadeEstoqueMinimo,
                PrecoVenda = produtoAtual.PrecoVenda
            };

            // Atualiza o produto
            await produtoRepository.Atualizar(produtoAtualizado);
            logger.LogInformation("Produto {InstanciaNome} atualizado com sucesso - ID: {Id}", req.Nome, req.Id);

            return Result.Ok();
        }
        catch (Exception ex) when (ex.GetType().Name == "DbUpdateConcurrencyException")
        {
            logger.LogWarning(ex, "Conflito de concorrência ao atualizar produto {InstanciaNome} - ID: {Id}", req.Nome, req.Id);
            return Result.Fail("O produto foi modificado por outro usuário. Por favor, recarregue a página e tente novamente.");
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("não foi encontrado"))
        {
            logger.LogWarning("Produto não encontrado para atualização - ID: {Id}", req.Id);
            return Result.Fail("Produto não encontrado");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao atualizar produto {InstanciaNome}: {Message}", req.Nome, ex.Message);
            return Result.Fail($"Erro ao atualizar produto: {ex.Message}");
        }
    }
}