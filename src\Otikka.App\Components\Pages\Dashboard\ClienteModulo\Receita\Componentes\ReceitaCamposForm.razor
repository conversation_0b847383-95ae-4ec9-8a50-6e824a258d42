﻿@inherits PageBase
@using Otikka.Application.Contracts.Infrastructure
@inject ILogger<ReceitaCamposForm> Logger
<div class="row gy-4">
    <div class="@(ShowUpload ? "col-xxl-6 col-md-6" : "col-xxl-9 col-md-9")">
        <div>
            <label for="profissional" class="form-label">Profissional</label>
            <InputText @bind-Value="Receita!.NomeProfissional" autocomplete="not" class="form-control"
                       id="profissional"/>
            <ValidationMessage For="() => Receita!.NomeProfissional"/>
        </div>
    </div>
    <div class="col-xxl-3 col-md-3">
        <div>
            <label for="validade" class="form-label">Validade</label>
            <InputDate @bind-Value="Receita!.DataValidade" autocomplete="not" class="form-control" id="validade"/>
            <ValidationMessage For="() => Receita!.DataValidade"/>
        </div>
    </div>
    @if(ShowUpload){
    <div class="col-xxl-3 col-md-3">
        <div>
            <label for="imagemReceita" class="form-label">
                <i class="ri-camera-fill me-1"></i> Foto da Receita
            </label>
            @if (!string.IsNullOrEmpty(Receita?.ImagemReceitaUrl))
            {
                <div class="">
                    <a href="@Receita.ImagemReceitaUrl" target="_blank" class="btn btn-outline-info">
                        <i class="ri-image-line me-1"></i> Abrir
                    </a>
                    <button type="button" class="btn btn-outline-danger ms-2" @onclick="ConfirmDeleteImage">
                        <i class="ri-delete-bin-line me-1"></i> Excluir
                    </button>
                </div>
            }
            else
            {
                <div class="input-group">
                    <InputFile OnChange="OnFileChange" accept="image/*" class="form-control" id="imagemReceita" />
                </div>
                @if (!string.IsNullOrEmpty(uploadErrorMessage))
                {
                    <div class="text-danger mt-1">
                        <small>@uploadErrorMessage</small>
                    </div>
                }
            }
        </div>
    </div>
    }
</div>

<div class="row gy-4 table-responsive">
    <table class="table">
        <thead>
        <tr>
            <th></th>
            <th></th>
            <th style="min-width:150px">Esférico</th>
            <th style="min-width:150px">Cilíndrico</th>
            <th style="min-width:120px">Eixo</th>
            <th style="min-width:90px">Altura</th>
            <th style="min-width:90px">DNP</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td rowspan="2" class="align-middle bg-success-subtle">Longe</td>
            <td class="align-middle bg-success-subtle"><i class="ri-eye-fill"></i> OD</td>
            <td>
                <div class="input-group">
                    <InputSelect class="form-select" @bind-Value="Receita!.EsfericoOlhoDireitoLonge">
                        @for (double i = -20.0; i <= 20.0; i += 0.25)
                        {
                            <option value="@i">@i.ToString("F2")</option>
                        }
                    </InputSelect>
                </div>
                <ValidationMessage For="() => Receita!.EsfericoOlhoDireitoLonge"/>
            </td>
            <td>
                <div class="input-group">
                    <InputSelect class="form-select" @bind-Value="Receita!.CilindricoOlhoDireitoLonge">
                        @for (double i = -6.0; i <= 6.0; i += 0.25)
                        {
                            <option value="@i">@i.ToString("F2")</option>
                        }
                    </InputSelect>
                </div>
                <ValidationMessage For="() => Receita!.CilindricoOlhoDireitoLonge"/>

            </td>
            <td>
                <div class="input-group">
                    <InputNumber @bind-Value="Receita!.EixoOlhoDireitoLonge" autocomplete="not" class="form-control"
                                 placeholder="L.O.D."/>
                    <span class="input-group-text">°</span>
                </div>
                <ValidationMessage For="() => Receita!.EixoOlhoDireitoLonge"/>
            </td>
            <td>
                <InputNumber @bind-Value="Receita!.AlturaOlhoDireitoLonge" autocomplete="not" class="form-control"
                             placeholder="L.O.D."/>
                <ValidationMessage For="() => Receita!.AlturaOlhoDireitoLonge"/>
            </td>
            <td>
                <InputNumber @bind-Value="Receita!.DNPOlhoDireitoLonge" autocomplete="not" class="form-control"
                             placeholder="L.O.D."/>
                <ValidationMessage For="() => Receita!.DNPOlhoDireitoLonge"/>
            </td>
        </tr>
        <tr>
            <td class="align-middle  bg-success-subtle"><i class="ri-eye-fill"></i> OE</td>
            <td>
                <div class="input-group">
                    <InputSelect class="form-select" @bind-Value="Receita!.EsfericoOlhoEsquerdoLonge">
                        @for (double i = -20.0; i <= 20.0; i += 0.25)
                        {
                            <option value="@i">@i.ToString("F2")</option>
                        }
                    </InputSelect>
                </div>
                <ValidationMessage For="() => Receita!.EsfericoOlhoEsquerdoLonge"/>
            </td>

            <td>
                <div class="input-group">
                    <InputSelect class="form-select" @bind-Value="Receita!.CilindricoOlhoEsquerdoLonge">
                        @for (double i = -6.0; i <= 6.0; i += 0.25)
                        {
                            <option value="@i">@i.ToString("F2")</option>
                        }
                    </InputSelect>
                </div>
                <ValidationMessage For="() => Receita!.CilindricoOlhoEsquerdoLonge"/>

            </td>
            <td>
                <div class="input-group">
                    <InputNumber @bind-Value="Receita!.EixoOlhoEsquerdoLonge" autocomplete="not" class="form-control"
                                 placeholder="L.O.E."/>
                    <span class="input-group-text">°</span>
                </div>
                <ValidationMessage For="() => Receita!.EixoOlhoDireitoLonge"/>
            </td>
            <td>
                <InputNumber @bind-Value="Receita!.AlturaOlhoEsquerdoLonge" autocomplete="not" class="form-control"
                             placeholder="L.O.E."/>
                <ValidationMessage For="() => Receita!.AlturaOlhoDireitoLonge"/>
            </td>
            <td>
                <InputNumber @bind-Value="Receita!.DNPOlhoEsquerdoLonge" autocomplete="not" class="form-control"
                             placeholder="L.O.E."/>
                <ValidationMessage For="() => Receita!.DNPOlhoDireitoLonge"/>
            </td>
        </tr>
        <tr>
            <td colspan="2" class="align-middle bg-warning-subtle"><label for="adicao" class="form-label">Adição</label></td>
            <td colspan="5">
                <div>
                    <InputNumber @bind-Value="Receita!.Adicao" autocomplete="not" class="form-control" id="adicao" @bind-Value:after="Adicionar"/>
                    <ValidationMessage For="() => Receita!.Adicao"/>
                </div>
            </td>
        </tr>
        <tr>
            <td rowspan="2" class="align-middle bg-info-subtle">Perto</td>
            <td class="align-middle bg-info-subtle"><i class="ri-eye-fill"></i> OD</td>

            <td>
                <div class="input-group">
                    <InputSelect class="form-select" @bind-Value="Receita!.EsfericoOlhoDireitoPerto">
                        @for (double i = -20.0; i <= 20.0; i += 0.25)
                        {
                            <option value="@i">@i.ToString("F2")</option>
                        }
                    </InputSelect>
                </div>
                <ValidationMessage For="() => Receita!.EsfericoOlhoDireitoPerto"/>
            </td>

            <td>
                <div class="input-group">
                    <InputSelect class="form-select" @bind-Value="Receita!.CilindricoOlhoDireitoPerto">
                        @for (double i = -6.0; i <= 6.0; i += 0.25)
                        {
                            <option value="@i">@i.ToString("F2")</option>
                        }
                    </InputSelect>
                </div>
                <ValidationMessage For="() => Receita!.CilindricoOlhoDireitoPerto"/>

            </td>
            <td>
                <div class="input-group">
                    <InputNumber @bind-Value="Receita!.EixoOlhoDireitoPerto" autocomplete="not" class="form-control"
                                 placeholder="P.O.D."/>
                    <span class="input-group-text">°</span>
                </div>
                <ValidationMessage For="() => Receita!.EixoOlhoDireitoPerto"/>
            </td>
            <td>
                <InputNumber @bind-Value="Receita!.AlturaOlhoDireitoPerto" autocomplete="not" class="form-control"
                             placeholder="P.O.D."/>
                <ValidationMessage For="() => Receita!.AlturaOlhoDireitoLonge"/>
            </td>
            <td>
                <InputNumber @bind-Value="Receita!.DNPOlhoDireitoPerto" autocomplete="not" class="form-control"
                             placeholder="P.O.D."/>
                <ValidationMessage For="() => Receita!.DNPOlhoDireitoPerto"/>
            </td>
        </tr>
        <tr>
            <td class="align-middle  bg-info-subtle"><i class="ri-eye-fill"></i> OE</td>
            <td>
                <div class="input-group">
                    <InputSelect class="form-select" @bind-Value="Receita!.EsfericoOlhoEsquerdoPerto">
                        @for (double i = -20.0; i <= 20.0; i += 0.25)
                        {
                            <option value="@i">@i.ToString("F2")</option>
                        }
                    </InputSelect>
                </div>
                <ValidationMessage For="() => Receita!.EsfericoOlhoEsquerdoPerto"/>
            </td>

            <td>
                <div class="input-group">
                    <InputSelect class="form-select" @bind-Value="Receita!.CilindricoOlhoEsquerdoPerto">
                        @for (double i = -6.0; i <= 6.0; i += 0.25)
                        {
                            <option value="@i">@i.ToString("F2")</option>
                        }
                    </InputSelect>
                </div>
                <ValidationMessage For="() => Receita!.CilindricoOlhoEsquerdoPerto"/>
            </td>
            <td>
                <div class="input-group">
                    <InputNumber @bind-Value="Receita!.EixoOlhoEsquerdoPerto" autocomplete="not" class="form-control"
                                 placeholder="P.O.E."/>
                    <span class="input-group-text">°</span>
                </div>
                <ValidationMessage For="() => Receita!.EixoOlhoEsquerdoPerto"/>
            </td>
            <td>
                <InputNumber @bind-Value="Receita!.AlturaOlhoEsquerdoPerto" autocomplete="not" class="form-control"
                             placeholder="P.O.E."/>
                <ValidationMessage For="() => Receita!.AlturaOlhoDireitoPerto"/>
            </td>
            <td>
                <InputNumber @bind-Value="Receita!.DNPOlhoEsquerdoPerto" autocomplete="not" class="form-control"
                             placeholder="P.O.E."/>
                <ValidationMessage For="() => Receita!.DNPOlhoDireitoPerto"/>
            </td>
        </tr>
        
        </tbody>
    </table>
</div>

@code {
    [Parameter] public bool ShowUpload { get; set; } = true;
    [Parameter] public Otikka.Domain.Entities.PessoaModulo.Receita? Receita { get; set; }
    [Parameter] public EventCallback<IBrowserFile> OnFileSelected { get; set; }

    private IBrowserFile? selectedFile;
    private string? uploadErrorMessage;
    private const long maxFileSize = 5 * 1024 * 1024; // 5MB
    private readonly string[] allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };

    private async Task OnFileChange(InputFileChangeEventArgs e)
    {
        selectedFile = e.File;
        uploadErrorMessage = null;

        // Validações básicas
        if (selectedFile != null)
        {
            var extension = Path.GetExtension(selectedFile.Name).ToLowerInvariant();
            
            if (!allowedExtensions.Contains(extension))
            {
                uploadErrorMessage = "Formato de arquivo não suportado. Use apenas imagens (JPG, PNG, GIF, BMP).";
                selectedFile = null;
                return;
            }

            if (selectedFile.Size > maxFileSize)
            {
                uploadErrorMessage = "Arquivo muito grande. O tamanho máximo é 5MB.";
                selectedFile = null;
                return;
            }

            await OnFileSelected.InvokeAsync(selectedFile);
        }

        StateHasChanged();
    }

    public void Adicionar()
    {
        if (Receita is null) return;

        if (Receita.Adicao > -20 && Receita.EsfericoOlhoEsquerdoLonge > -20)
        {
            Receita.EsfericoOlhoEsquerdoPerto = Receita.EsfericoOlhoEsquerdoLonge + Receita.Adicao;
            Receita.CilindricoOlhoEsquerdoPerto = Receita.CilindricoOlhoEsquerdoLonge;
            Receita.EixoOlhoEsquerdoPerto = Receita.EixoOlhoEsquerdoLonge;
        }
        if (Receita.Adicao > -20 && Receita.EsfericoOlhoDireitoLonge > -20)
        {
            Receita.EsfericoOlhoDireitoPerto = Receita.EsfericoOlhoDireitoLonge + Receita.Adicao;
            Receita.CilindricoOlhoDireitoPerto = Receita.CilindricoOlhoDireitoLonge;
            Receita.EixoOlhoDireitoPerto = Receita.EixoOlhoDireitoLonge;
        }
    }

    private async Task ConfirmDeleteImage()
    {
        var confirmed = await AlertService.ShowConfirm("Confirmação", "Tem certeza que deseja excluir a imagem da receita?", "Excluído!", "Imagem excluída com sucesso");
        if (confirmed)
        {
            await DeleteImage();
        }
    }

    private async Task DeleteImage()
    {
        if (Receita == null || string.IsNullOrEmpty(Receita.ImagemReceitaUrl)) return;

        try
        {
            // Extrair o nome do objeto do URL
            var uri = new Uri(Receita.ImagemReceitaUrl);
            var objectName = uri.Segments.Last(); // Pega o último segmento da URL, que deve ser o nome do arquivo
            objectName = $"clientes/receitas/{objectName}"; // Reconstroi o caminho completo no S3

            // A exclusão da imagem será feita no handler, então apenas limpa a URL aqui
            Receita.ImagemReceitaUrl = null; 
            selectedFile = null; 
            uploadErrorMessage = null; 
            await AlertService.ShowSuccessMessage("Imagem marcada para exclusão!");
        }
        catch (Exception ex)
        {
            await AlertService.ShowAlert("Erro", "Erro interno ao excluir imagem. Tente novamente.");
            Logger.LogError(ex, "Erro interno ao excluir imagem da receita");
        }
        finally
        {
            StateHasChanged();
        }
    }
}