@using Otikka.App.Components.Pages.Dashboard.ProdutoModulo.MovimentacaoEstoqueModulo.Componentes
@using Otikka.Application.Features.MovimentacaoEstoque.Commands.CreateMovimentacaoEstoque
@using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo
@using Otikka.Domain.Enums
@using FluentResults
@using Wolverine
@attribute [Route(Application.Routes.MovimentacaoEstoqueCadastrar)]
@inherits PageBase

<PageTitle>
    Movimentação de Estoque - Cadastro
</PageTitle>

<MovimentacaoEstoqueForm EhEdicao="@false" MovimentacaoEstoque="MovimentacaoEstoque" OnSave="Save" />

@code {

    [SupplyParameterFromForm] 
    private CreateMovimentacaoEstoque MovimentacaoEstoque { get; set; } = new() 
    { 
        DataMovimentacao = DateTime.Now,
        TipoMovimentacao = TipoMovimentacaoEstoque.Entrada
    };

    private async Task Save()
    {
        try
        {
            var result = await MessageBus.InvokeAsync<Result>(MovimentacaoEstoque);

            if (result.IsSuccess)
            {
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.MovimentacaoEstoqueListar);
            }
            else
            {
                await AlertService.ShowError("Opps!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao cadastrar movimentação de estoque");
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Opps!", $"Erro inesperado: {ex.Message}");
        }
    }
}
