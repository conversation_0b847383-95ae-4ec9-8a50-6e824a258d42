﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<PackageId>Spacedu.DevPack.Blazor</PackageId>
		<Version>0.0.1</Version>
		<Authors><PERSON></Authors>
		<Company>Spacedu</Company>
		<PackageDescription>Uma biblioteca de componentes Blazor reutilizáveis.</PackageDescription>
		<PackageTags>blazor;components</PackageTags>
		<RepositoryUrl>https://github.com/eliasribeiro/repositorio</RepositoryUrl>
		<PackageLicenseExpression>MIT</PackageLicenseExpression>
	</PropertyGroup>

	<ItemGroup>
		<Content Include="wwwroot\**" Pack="true" PackagePath="contentFiles\any\$(TargetFramework)\wwwroot" />
	</ItemGroup>
	<ItemGroup>
		<SupportedPlatform Include="browser" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.8" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="wwwroot\" />
	</ItemGroup>

</Project>
