@attribute [Route(Application.Routes.FornecedorCadastrar)]

@using Otikka.App.Components.Pages.Dashboard.FornecedorModulo.Componentes
@using Otikka.Application.Features.Pessoa.Commands.CreateFornecedor
@using Otikka.Application.Constants
@using Otikka.Domain.Entities.Common
@using Otikka.Domain.Entities.PessoaModulo
@using FluentValidation
@inherits PageBase
@inject Gestao.Dominio.Repositorios.IPessoaRepositorio PessoaRepositorio
@inject IValidator<CreateFornecedor> Validator
@inject ILogger<CreateFornecedorHandler> Logger

<FornecedorForm EhEdicao="@false" Pessoa="Pessoa" OnSave="Save" />

@code {

    public Pessoa? Pessoa { get; set; }

    protected override void OnInitialized()
    {
        Pessoa = new() { Enderecos = new List<Endereco>() { new Endereco() }, ehLaboratorio = true };
    }

    private async Task Save()
    {
        if(<PERSON><PERSON>oa is null)
        {
            await AlertService.ShowError("Opps!", "Fornecedor não existe!");
            return;
        }

        try
        {
            await ProcessingChange(true);
            var EmpresaId = await GetEmpresaIdAsync();

            var command = new CreateFornecedor
            {
                Nome = Pessoa.Nome,
                NomeFantasia = Pessoa.NomeFantasia,
                Documento = Pessoa.Documento,
                Email = Pessoa.Email,
                Telefone1 = Pessoa.Telefone1,
                Telefone2 = Pessoa.Telefone2,
                Observacao = Pessoa.Observacao,
                Tipo = Pessoa.Tipo,
                NascimentoData = Pessoa.NascimentoData,
                EmpresaId = EmpresaId,
                Enderecos = Pessoa.Enderecos,
                // Campos fiscais
                InscricaoEstadualNumero = Pessoa.InscricaoEstadualNumero,
                InscricaoMunicipalNumero = Pessoa.InscricaoMunicipalNumero,
                InscricaoSuframa = Pessoa.InscricaoSuframa,
                ContribuinteICMS = Pessoa.ContribuinteICMS,
                ehLaboratorio = Pessoa.ehLaboratorio,
                
                // Campos de auditoria
                CriadoPorId = await GetUsuarioIdLoggedAsync(),
                DataCriacao = DateTime.UtcNow
            };

            var result = await MessageBus.InvokeAsync<Result<Guid>>(command);

            if (result.IsSuccess)
            {
                // Definir PessoaId nos endereços após criação
                if (Pessoa.Enderecos != null && Pessoa.Enderecos.Any())
                {
                    foreach (var endereco in Pessoa.Enderecos)
                    {
                        endereco.PessoaId = result.Value;
                    }
                }

                await ProcessingChange(false);
                MessagingCenter.Send<Cadastrar>(this, SystemMessages.DadosAtualizados);
                NavigationManager.NavigateTo(Application.Routes.FornecedorListar);
            }
            else
            {
                await ProcessingChange(false);
                await AlertService.ShowError("Erro ao cadastrar fornecedor", result.Errors.FirstOrDefault()?.Message ?? "Erro ao cadastrar fornecedor");
            }


        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro", $"Erro ao salvar fornecedor: {ex.Message}");
        }
        finally
        {
            await ProcessingChange(false);
        }
    }
}