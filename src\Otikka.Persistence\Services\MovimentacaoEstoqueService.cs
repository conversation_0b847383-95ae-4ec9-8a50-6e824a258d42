using Microsoft.EntityFrameworkCore;
using Otikka.Application.Contracts.Persistence;
using Otikka.Domain.Entities.Common;
using Otikka.Domain.Entities.ProdutoModulo;
using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo;
using Otikka.Domain.Enums;

namespace Otikka.Persistence.Services;

public interface IMovimentacaoEstoqueService
{
    Task GerenciarMovimentacoesVenda(Guid vendaId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId);
    Task GerenciarMovimentacoesOrdemServico(Guid ordemServicoId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId);
    Task GerenciarMovimentacoesVenda(ApplicationDbContext db, Guid vendaId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId);
    Task GerenciarMovimentacoesOrdemServico(ApplicationDbContext db, Guid ordemServicoId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId);
    Task RemoverMovimentacoesTransacao(Guid transacaoComercialId);
}

public class MovimentacaoEstoqueService : IMovimentacaoEstoqueService
{
    private readonly IDbContextFactory<ApplicationDbContext> _factory;
    private readonly IMovimentacaoEstoqueRepositorio _movimentacaoEstoqueRepositorio;

    public MovimentacaoEstoqueService(
        IDbContextFactory<ApplicationDbContext> factory,
        IMovimentacaoEstoqueRepositorio movimentacaoEstoqueRepositorio)
    {
        _factory = factory;
        _movimentacaoEstoqueRepositorio = movimentacaoEstoqueRepositorio;
    }

    public async Task GerenciarMovimentacoesVenda(Guid vendaId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId)
    {
        await GerenciarMovimentacoes(vendaId, produtosVendidos, empresaId, TipoTransacaoComercial.Venda);
    }

    public async Task GerenciarMovimentacoesOrdemServico(Guid ordemServicoId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId)
    {
        await GerenciarMovimentacoes(ordemServicoId, produtosVendidos, empresaId, TipoTransacaoComercial.OrdemServico);
    }

    public async Task GerenciarMovimentacoesVenda(ApplicationDbContext db, Guid vendaId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId)
    {
        await GerenciarMovimentacoes(db, vendaId, produtosVendidos, empresaId, TipoTransacaoComercial.Venda);
    }

    public async Task GerenciarMovimentacoesOrdemServico(ApplicationDbContext db, Guid ordemServicoId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId)
    {
        await GerenciarMovimentacoes(db, ordemServicoId, produtosVendidos, empresaId, TipoTransacaoComercial.OrdemServico);
    }

    private async Task GerenciarMovimentacoes(Guid transacaoComercialId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId, TipoTransacaoComercial tipoTransacao)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();

        try
        {
            await GerenciarMovimentacoes(_db, transacaoComercialId, produtosVendidos, empresaId, tipoTransacao);
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    private async Task GerenciarMovimentacoes(ApplicationDbContext db, Guid transacaoComercialId, List<ProdutoServicoVendido> produtosVendidos, Guid empresaId, TipoTransacaoComercial tipoTransacao)
    {
        // Obter movimentações existentes para esta transação
        var movimentacoesExistentes = await _movimentacaoEstoqueRepositorio.ObterPorTransacaoComercial(transacaoComercialId);

        // Processar cada produto vendido
        foreach (var produtoVendido in produtosVendidos)
        {
            // Verificar se o produto tem controle de estoque
            var produto = await db.Produtos.FindAsync(produtoVendido.ProdutoId);
            if (produto?.ControleEstoque != true)
                continue;

            // Verificar se já existe uma movimentação para este produto nesta transação
            var movimentacaoExistente = movimentacoesExistentes
                .FirstOrDefault(m => m.ProdutoId == produtoVendido.ProdutoId);

            if (movimentacaoExistente != null)
            {
                // Atualizar movimentação existente
                AtualizarMovimentacao(db, movimentacaoExistente, produtoVendido, produto);
            }
            else
            {
                // Criar nova movimentação
                await CriarNovaMovimentacao(db, transacaoComercialId, produtoVendido, produto, tipoTransacao);
            }
        }

        // Remover movimentações de produtos que não estão mais na lista
        var produtosVendidosIds = produtosVendidos.Select(p => p.ProdutoId).ToList();
        var movimentacoesParaRemover = movimentacoesExistentes
            .Where(m => !produtosVendidosIds.Contains(m.ProdutoId))
            .ToList();

        foreach (var movimentacao in movimentacoesParaRemover)
        {
            await RemoverMovimentacao(db, movimentacao);
        }

        await db.SaveChangesAsync();
    }

    private async Task CriarNovaMovimentacao(ApplicationDbContext db, Guid transacaoComercialId, ProdutoServicoVendido produtoVendido, Produto produto, TipoTransacaoComercial tipoTransacao)
    {
        var movimentacao = new MovimentacaoEstoque
        {
            Id = Guid.NewGuid(),
            ProdutoId = produto.Id,
            TipoMovimentacao = TipoMovimentacaoEstoque.Saida,
            DataMovimentacao = DateTime.Now,
            Quantidade = -produtoVendido.Quantidade, // Negativo para saída
            CustoUnitario = produtoVendido.PrecoVenda,
            TransacaoComercialId = transacaoComercialId,
            TipoTransacaoComercial = tipoTransacao,
            Descricao = $"Saída por {(tipoTransacao == TipoTransacaoComercial.Venda ? "venda" : "ordem de serviço")}"
        };

        db.MovimentacoesEstoque.Add(movimentacao);

        // Atualizar estoque corrente do produto
        produto.QuantidadeEstoqueCorrente -= produtoVendido.Quantidade;
        db.Produtos.Update(produto);
    }

    private void AtualizarMovimentacao(ApplicationDbContext db, MovimentacaoEstoque movimentacaoExistente, ProdutoServicoVendido produtoVendido, Produto produto)
    {
        // Reverter a quantidade anterior no estoque
        produto.QuantidadeEstoqueCorrente -= movimentacaoExistente.Quantidade;

        // Atualizar a movimentação
        movimentacaoExistente.Quantidade = -produtoVendido.Quantidade; // Negativo para saída
        movimentacaoExistente.CustoUnitario = produtoVendido.PrecoVenda;
        movimentacaoExistente.DataMovimentacao = DateTime.Now;

        // Aplicar a nova quantidade no estoque
        produto.QuantidadeEstoqueCorrente += movimentacaoExistente.Quantidade;

        db.MovimentacoesEstoque.Update(movimentacaoExistente);
        db.Produtos.Update(produto);
    }

    private async Task RemoverMovimentacao(ApplicationDbContext db, MovimentacaoEstoque movimentacao)
    {
        // Reverter a movimentação no estoque
        var produto = await db.Produtos.FindAsync(movimentacao.ProdutoId);
        if (produto != null)
        {
            produto.QuantidadeEstoqueCorrente -= movimentacao.Quantidade;
            db.Produtos.Update(produto);
        }

        db.MovimentacoesEstoque.Remove(movimentacao);
    }

    public async Task RemoverMovimentacoesTransacao(Guid transacaoComercialId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();
        
        try
        {
            var movimentacoes = await _movimentacaoEstoqueRepositorio.ObterPorTransacaoComercial(transacaoComercialId);

            foreach (var movimentacao in movimentacoes)
            {
                await RemoverMovimentacao(_db, movimentacao);
            }

            await _db.SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}
