<Project>
  
  <PropertyGroup>
    <ManagePackageVersionsCentrally>false</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <!-- Definindo versões específicas para resolver conflitos -->
  <ItemGroup>
    <!-- Unificando todas as dependências do Microsoft.CodeAnalysis para usar versão 4.14.0 -->
    <PackageReference Update="Microsoft.CodeAnalysis.Common" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.Workspaces.Common" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.Scripting" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.Scripting.Common" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.VisualBasic" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.VisualBasic.Workspaces" Version="4.14.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.Workspaces.MSBuild" Version="4.14.0" />
    
    <!-- Forçando dependências do System para usar versão 9.0.7 -->
    <PackageReference Update="System.Drawing.Common" Version="9.0.7" />
    <PackageReference Update="System.Threading.AccessControl" Version="9.0.7" />
    <PackageReference Update="System.Formats.Nrbf" Version="9.0.7" />
  </ItemGroup>

</Project>