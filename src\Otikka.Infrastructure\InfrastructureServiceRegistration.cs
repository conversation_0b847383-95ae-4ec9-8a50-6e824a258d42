using Hangfire;
using Hangfire.PostgreSql;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Otikka.Application.Contracts.Infrastructure;
using Otikka.Application.Infrastructure.Contracts;
using Otikka.Infrastructure.CEP;
using Otikka.Infrastructure.Crypto;

using Otikka.Infrastructure.Email;
using Otikka.Infrastructure.Messages;
using Otikka.Infrastructure.Password;
using Otikka.Infrastructure.Storage;
using Otikka.Infrastructure.Worker;
using Otikka.Infrastructure.Services;

namespace Otikka.Infrastructure;

public static class InfrastructureServiceRegistration
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Registrar HttpClient para serviços externos
        services.AddHttpClient<ICnpjService, CnpjService>();
        services.AddHttpClient<INuvemFiscalService, NuvemFiscalService>();


        // Registrar HttpClient e serviço de CEP com cache
        services.AddHttpClient<CachedCepService>();
        services.AddScoped<ICepService, CachedCepService>();

        // Registrar serviços da infraestrutura
        services.AddScoped<IFileStorageService, S3StorageService>();
        services.AddScoped<IAlertService, AlertService>();
        services.AddScoped<IToastService, ToastService>();
        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<ICryptoService, CryptoService>();
        services.AddScoped<IPasswordService, PasswordService>();
        services.AddScoped<IWorkerService, HangfireWorkerService>();
        services.AddSingleton<IAuditLogService, AuditLogService>();

        services.AddScoped<HangfireJobsService>();

        services.AddHangfire(config => config
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UsePostgreSqlStorage(options => options.UseNpgsqlConnection(configuration.GetConnectionString("HangfireConnection"))));

        return services;
    }
}