using FluentResults;
using FluentValidation;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;

namespace Otikka.Application.Features.MovimentacaoEstoque.Commands.CreateMovimentacaoEstoque;

public class CreateMovimentacaoEstoqueHandler(
    IMovimentacaoEstoqueRepositorio movimentacaoEstoqueRepository,
    IValidator<CreateMovimentacaoEstoque> validator,
    ILogger<CreateMovimentacaoEstoqueHandler> logger)
{
    public async Task<Result> Handle(CreateMovimentacaoEstoque req)
    {
        logger.LogInformation("Iniciando cadastro de movimentação de estoque para produto: {ProdutoId}", req.ProdutoId);
        logger.LogInformation("Dados da movimentação - ProdutoId: {ProdutoId}, TipoMovimentacao: {TipoMovimentacao}, Quantidade: {Quantidade}",
            req.ProdutoId, req.TipoMovimentacao, req.Quantidade);

        // Validar o comando antes de processar
        var validationResult = await validator.ValidateAsync(req, options => options.IncludeRuleSets("MovimentacaoEstoqueValidacao"));
        if (!validationResult.IsValid)
        {
            var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
            logger.LogWarning("Falha na validação da movimentação de estoque: {Errors}", string.Join("; ", errors));
            return Result.Fail(string.Join("; ", errors));
        }

        try
        {
            // Gerar ID se não foi fornecido
            if (req.Id == Guid.Empty)
            {
                req.Id = Guid.NewGuid();
            }

            // Definir data de criação
            req.DataCriacao = DateTimeOffset.Now;

            // Cadastra a movimentação de estoque
            await movimentacaoEstoqueRepository.Cadastrar(req);
            logger.LogInformation("Movimentação de estoque cadastrada com sucesso - ID: {Id}", req.Id);

            return Result.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao cadastrar movimentação de estoque: {Message}", ex.Message);
            return Result.Fail($"Erro ao cadastrar movimentação de estoque: {ex.Message}");
        }
    }
}
