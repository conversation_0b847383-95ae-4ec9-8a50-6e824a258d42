@attribute [Route(Application.Routes.ClienteCadastrar)]

@inherits PageBase
@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Cliente.Componentes
@using Otikka.Application.Features.Pessoa.Commands.CreateCliente
@using Otikka.Application.Constants
@using Otikka.Domain.Entities.Common
@using Otikka.Domain.Entities.PessoaModulo

<ClienteForm EhEdicao="@false" Pessoa="Pessoa" OnSave="Save" />

@code {
    public CreateCliente Pessoa { get; set; } = new CreateCliente();

    protected override async Task OnInitializedAsync()
    {
        var EmpresaId = await GetEmpresaIdAsync();
        Pessoa = new() { EmpresaId = EmpresaId, Enderecos = new List<Endereco>() { new() }};
    }

    private async Task Save()
    {

        try
        {
            await ProcessingChange(true);

            // Campos de auditoria
            Pessoa.CriadoPorId = await GetUsuarioIdLoggedAsync();
            Pessoa.DataCriacao = DateTime.UtcNow;

            var result = await MessageBus.InvokeAsync<Result>(Pessoa);

            if (result.IsSuccess)
            {
                await ProcessingChange(false);
                MessagingCenter.Send<Cadastrar>(this, SystemMessages.DadosAtualizados);
                NavigationManager.NavigateTo(Application.Routes.ClienteListar);
            }
            else
            {
                await ProcessingChange(false);
                await AlertService.ShowError("Erro ao cadastrar cliente", result.Errors.FirstOrDefault()?.Message ?? "Erro ao cadastrar cliente");
            }


        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro", $"Erro inesperado: {ex.Message}");
        }
        finally
        {
            await ProcessingChange(false);
        }
    }
}