﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Otikka.Persistence;

#nullable disable

namespace Otikka.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250828204142_RemoveAtivoFromEstoqueProduto")]
    partial class RemoveAtivoFromEstoqueProduto
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Otikka.Domain.Entities.Common.CepCache", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Bairro")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("InstanciaNome do bairro");

                    b.Property<string>("Cep")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasComment("Código postal (CEP) sem formatação");

                    b.Property<bool>("CepValido")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasComment("Indica se o CEP é válido");

                    b.Property<string>("Complemento")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasComment("Complemento do endereço");

                    b.Property<Guid?>("CriadoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("DDD")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasComment("Código DDD");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("GIA")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasComment("Código GIA");

                    b.Property<string>("IBGE")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasComment("Código IBGE");

                    b.Property<string>("Localidade")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("InstanciaNome da cidade/localidade");

                    b.Property<string>("Logradouro")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasComment("InstanciaNome do logradouro");

                    b.Property<string>("SIAFI")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasComment("Código SIAFI");

                    b.Property<string>("UF")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("character(2)")
                        .IsFixedLength()
                        .HasComment("Unidade federativa (estado)");

                    b.Property<DateTime>("UltimaConsulta")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("Data da última consulta");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("Cep")
                        .IsUnique()
                        .HasDatabaseName("IX_CepCache_Cep");

                    b.HasIndex("CriadoPorId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("UltimaConsulta")
                        .HasDatabaseName("IX_CepCache_UltimaConsulta");

                    b.ToTable("CepCache");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.Documento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Caminho")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("TransacaoFinanceiraId")
                        .HasColumnType("uuid");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("TransacaoFinanceiraId");

                    b.ToTable("Documentos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.Endereco", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Bairro")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Cidade")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CodigoPostal")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("Complemento")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<string>("Estado")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Logradouro")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Numero")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("Pais")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)");

                    b.Property<Guid?>("PessoaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("UsuarioId")
                        .HasColumnType("uuid");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId")
                        .IsUnique();

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("PessoaId");

                    b.HasIndex("UsuarioId");

                    b.ToTable("Enderecos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.ProdutoServicoVendido", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("PrecoCusto")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("PrecoProduto")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("PrecoVenda")
                        .HasColumnType("decimal(10,2)");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid");

                    b.Property<int>("Quantidade")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TransacaoComercialBaseId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TransacaoComercialId")
                        .HasColumnType("uuid");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("ProdutoId");

                    b.HasIndex("TransacaoComercialId");

                    b.ToTable("ProdutoServicoVendidos", (string)null);
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.TransacaoComercial", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ClienteId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(21)
                        .HasColumnType("character varying(21)");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("NumeroIdentificador")
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)");

                    b.Property<string>("Observacao")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<Guid?>("VendedorId")
                        .HasColumnType("uuid");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("ClienteId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.ToTable("TransacoesComerciais", (string)null);

                    b.HasDiscriminator().HasValue("TransacaoComercial");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Otikka.Domain.Entities.CompraModule.Compra", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("DataCompra")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FornecedorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("NFeEntradaId")
                        .HasColumnType("uuid");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("FornecedorId");

                    b.HasIndex("NFeEntradaId");

                    b.ToTable("Compras");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.CompraModule.ItemCompra", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompraId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("PrecoUnitario")
                        .HasColumnType("decimal(10,2)");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid");

                    b.Property<int>("Quantidade")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CompraId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("ProdutoId");

                    b.ToTable("ItensCompra");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.EmpresaModule.Empresa", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Documento")
                        .HasMaxLength(18)
                        .HasColumnType("character varying(18)");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("EmpresaPaiId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Logo")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("LogoAltura")
                        .HasColumnType("integer");

                    b.Property<int?>("LogoLargura")
                        .HasColumnType("integer");

                    b.Property<string>("NomeFantasia")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("RazaoSocial")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("Sigla")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Telefone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("TipoNumeracaoOrdemServico")
                        .HasColumnType("integer");

                    b.Property<int>("TipoUnidade")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UsuarioId")
                        .HasColumnType("uuid");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("Documento")
                        .IsUnique();

                    b.HasIndex("EmpresaPaiId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("UsuarioId");

                    b.ToTable("Empresas");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.EmpresaModule.UsuarioEmpresa", b =>
                {
                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadoPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Perfil")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("EmpresaId", "UsuarioId");

                    b.HasIndex("UsuarioId");

                    b.ToTable("UsuariosEmpresas");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.NotaFiscalModule.Entrada.ItemNFeEntrada", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("NFeEntradaId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("PrecoUnitario")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid");

                    b.Property<int>("Quantidade")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadoPorId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("NFeEntradaId");

                    b.HasIndex("ProdutoId");

                    b.ToTable("ItensNFeEntrada");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.NotaFiscalModule.Entrada.NFeEntrada", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("DataEmissao")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("FornecedorCNPJ")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Numero")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("ValorTotal")
                        .HasColumnType("numeric");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadoPorId");

                    b.HasIndex("ExcluidoPorId");

                    b.ToTable("NFesEntrada");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.OrdemServicoModulo.OrdemServicoSituacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriado")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("OrdemServicoId")
                        .HasColumnType("uuid");

                    b.Property<string>("Situacao")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("OrdemServicoId");

                    b.ToTable("OrdensServicoSituacoes");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.PessoaModulo.Pessoa", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<bool>("ContribuinteICMS")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Documento")
                        .HasMaxLength(18)
                        .HasColumnType("character varying(18)");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("InscricaoEstadualNumero")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)");

                    b.Property<string>("InscricaoMunicipalNumero")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("InscricaoSuframa")
                        .HasMaxLength(14)
                        .HasColumnType("character varying(14)");

                    b.Property<DateTimeOffset?>("NascimentoData")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("NomeFantasia")
                        .HasColumnType("text");

                    b.Property<string>("Observacao")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Telefone1")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Telefone2")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Tipo")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.Property<bool>("ehCliente")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("ehFornecedor")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("ehLaboratorio")
                        .HasColumnType("boolean");

                    b.Property<bool>("ehRegimeSimples")
                        .HasColumnType("boolean");

                    b.Property<bool>("ehTransportadora")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.ToTable("Pessoas");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.PessoaModulo.Receita", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<float?>("Adicao")
                        .HasColumnType("real");

                    b.Property<float?>("AlturaOlhoDireitoLonge")
                        .HasColumnType("real");

                    b.Property<float?>("AlturaOlhoDireitoPerto")
                        .HasColumnType("real");

                    b.Property<float?>("AlturaOlhoEsquerdoLonge")
                        .HasColumnType("real");

                    b.Property<float?>("AlturaOlhoEsquerdoPerto")
                        .HasColumnType("real");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<float?>("CilindricoOlhoDireitoLonge")
                        .HasColumnType("real");

                    b.Property<float?>("CilindricoOlhoDireitoPerto")
                        .HasColumnType("real");

                    b.Property<float?>("CilindricoOlhoEsquerdoLonge")
                        .HasColumnType("real");

                    b.Property<float?>("CilindricoOlhoEsquerdoPerto")
                        .HasColumnType("real");

                    b.Property<Guid>("ClienteId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<float?>("DNPOlhoDireitoLonge")
                        .HasColumnType("real");

                    b.Property<float?>("DNPOlhoDireitoPerto")
                        .HasColumnType("real");

                    b.Property<float?>("DNPOlhoEsquerdoLonge")
                        .HasColumnType("real");

                    b.Property<float?>("DNPOlhoEsquerdoPerto")
                        .HasColumnType("real");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataValidade")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("EixoOlhoDireitoLonge")
                        .HasColumnType("integer");

                    b.Property<int?>("EixoOlhoDireitoPerto")
                        .HasColumnType("integer");

                    b.Property<int?>("EixoOlhoEsquerdoLonge")
                        .HasColumnType("integer");

                    b.Property<int?>("EixoOlhoEsquerdoPerto")
                        .HasColumnType("integer");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<float?>("EsfericoOlhoDireitoLonge")
                        .HasColumnType("real");

                    b.Property<float?>("EsfericoOlhoDireitoPerto")
                        .HasColumnType("real");

                    b.Property<float?>("EsfericoOlhoEsquerdoLonge")
                        .HasColumnType("real");

                    b.Property<float?>("EsfericoOlhoEsquerdoPerto")
                        .HasColumnType("real");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("ImagemReceitaUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("URL da imagem da receita armazenada no AWS S3");

                    b.Property<string>("NomeProfissional")
                        .HasColumnType("text");

                    b.Property<Guid?>("OrdemServicoId")
                        .HasColumnType("uuid");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("ClienteId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("OrdemServicoId")
                        .IsUnique();

                    b.ToTable("Receitas");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.CategoriaProduto", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CategoriaProdutoPaiId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(70)
                        .HasColumnType("character varying(70)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CategoriaProdutoPaiId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.ToTable("CategoriasProdutos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo.MovimentacaoEstoque", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompraId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("DataMovimentacao")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Descricao")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("EstoqueProdutoId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ItemCompraId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("PrecoUnitario")
                        .HasColumnType("decimal(10,2)");

                    b.Property<int>("Quantidade")
                        .HasColumnType("integer");

                    b.Property<string>("TipoMovimentacao")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TipoTransacaoComercial")
                        .HasColumnType("text");

                    b.Property<Guid?>("TransacaoComercialId")
                        .HasColumnType("uuid");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CompraId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EstoqueProdutoId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("ItemCompraId");

                    b.HasIndex("TransacaoComercialId");

                    b.ToTable("MovimentacoesEstoque");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.EstoqueProduto", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("PrecoCusto")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("PrecoVenda")
                        .HasColumnType("decimal(10,2)");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid");

                    b.Property<int>("QuantidadeEstoqueCorrente")
                        .HasColumnType("integer");

                    b.Property<int>("QuantidadeEstoqueMinimo")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("ProdutoId", "EmpresaId")
                        .IsUnique()
                        .HasFilter("\"DataExclusao\" IS NULL");

                    b.ToTable("EstoqueProdutos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.MarcaProduto", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(70)
                        .HasColumnType("character varying(70)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.ToTable("MarcasProdutos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.Produto", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CategoriaProdutoId")
                        .HasColumnType("uuid");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("character varying(25)");

                    b.Property<bool>("ControleEstoque")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EAN")
                        .HasMaxLength(25)
                        .HasColumnType("character varying(25)");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Encomenda")
                        .HasColumnType("boolean");

                    b.Property<int?>("EstoqueTotal")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("FornecedorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("MarcaProdutoId")
                        .HasColumnType("uuid");

                    b.Property<string>("NCM")
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasComment("Nomenclatura Comum do Mercosul - Código de classificação fiscal");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(75)
                        .HasColumnType("character varying(75)");

                    b.Property<int?>("OrigemMercadoria")
                        .HasColumnType("integer")
                        .HasComment("Origem da mercadoria conforme especificação da NFe/NFCe");

                    b.Property<string>("SKU")
                        .HasMaxLength(25)
                        .HasColumnType("character varying(25)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CategoriaProdutoId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("FornecedorId");

                    b.HasIndex("MarcaProdutoId");

                    b.ToTable("Produtos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.UnidadeMedidaProduto", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(70)
                        .HasColumnType("character varying(70)");

                    b.Property<string>("Sigla")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.ToTable("UnidadesMedidaProdutos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.TransacaoFinanceiraModulo.CategoriaTransacaoFinanceira", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(70)
                        .HasColumnType("character varying(70)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.ToTable("CategoriasTransacoesFinanceiras");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.TransacaoFinanceiraModulo.FormaPagamento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(70)
                        .HasColumnType("character varying(70)");

                    b.Property<int?>("Prioridade")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.ToTable("FormasPagamento");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.TransacaoFinanceiraModulo.Pagamento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("Data")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FormaPagamentoId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TransacaoComercialId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TransacaoFinanceiraId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Valor")
                        .HasColumnType("decimal(10,2)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("FormaPagamentoId");

                    b.HasIndex("TransacaoComercialId");

                    b.HasIndex("TransacaoFinanceiraId");

                    b.ToTable("Pagamentos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.TransacaoFinanceiraModulo.TransacaoFinanceira", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CategoriaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("FornecedorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Observacao")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Repetir")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int?>("RepetirVezes")
                        .HasColumnType("integer");

                    b.Property<string>("TipoTransacaoComercial")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TipoTransacaoFinanceira")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("TransacaoComercialId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("ValorPago")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal?>("ValorRestante")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal?>("ValorTotal")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTimeOffset?>("VencimentoData")
                        .HasColumnType("timestamp with time zone");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AtualizadorPorId");

                    b.HasIndex("CategoriaId");

                    b.HasIndex("CriadorPorId");

                    b.HasIndex("EmpresaId");

                    b.HasIndex("ExcluidoPorId");

                    b.HasIndex("FornecedorId");

                    b.HasIndex("TransacaoComercialId")
                        .IsUnique();

                    b.ToTable("TransacoesFinanceiras");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.UsuarioModule.Usuario", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AtualizadorPorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CriadorPorId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("DataCriacao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("DataExclusao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("ExcluidoPorId")
                        .HasColumnType("uuid");

                    b.Property<string>("Foto")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(70)
                        .HasColumnType("character varying(70)");

                    b.Property<string>("Senha")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.ToTable("Usuarios");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.OrdemServicoModulo.OrdemServico", b =>
                {
                    b.HasBaseType("Otikka.Domain.Entities.Common.TransacaoComercial");

                    b.Property<DateTimeOffset?>("BaixaDataEntrega")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("BaixaObservacao")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("EntregaData")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("LaboratorioDataEntrega")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("LaboratorioDataEnvio")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LaboratorioId")
                        .HasColumnType("uuid");

                    b.Property<string>("LaboratorioObservacao")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("OrdemServicoUltimoStatus")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)");

                    b.Property<string>("OrdemServicoUltimoStatusObservacao")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("PrevisaoEntregaData")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("RegistroData")
                        .HasColumnType("timestamp with time zone");

                    b.HasIndex("LaboratorioId");

                    b.HasIndex("VendedorId");

                    b.HasDiscriminator().HasValue("OrdemServico");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.VendaModulo.Venda", b =>
                {
                    b.HasBaseType("Otikka.Domain.Entities.Common.TransacaoComercial");

                    b.Property<DateTimeOffset>("DataVenda")
                        .HasColumnType("timestamp with time zone");

                    b.HasIndex("VendedorId");

                    b.HasDiscriminator().HasValue("Venda");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.CepCache", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadoPorId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId");

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("ExcluidoPor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.Documento", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.TransacaoFinanceiraModulo.TransacaoFinanceira", "TransacaoFinanceira")
                        .WithMany("Documentos")
                        .HasForeignKey("TransacaoFinanceiraId");

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("TransacaoFinanceira");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.Endereco", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithOne("Endereco")
                        .HasForeignKey("Otikka.Domain.Entities.Common.Endereco", "EmpresaId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.PessoaModulo.Pessoa", "Pessoa")
                        .WithMany("Enderecos")
                        .HasForeignKey("PessoaId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("Pessoa");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.ProdutoServicoVendido", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.ProdutoModulo.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.Common.TransacaoComercial", "TransacaoComercial")
                        .WithMany("ProdutoServicoVendidos")
                        .HasForeignKey("TransacaoComercialId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("Produto");

                    b.Navigation("TransacaoComercial");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.TransacaoComercial", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.PessoaModulo.Pessoa", "Cliente")
                        .WithMany()
                        .HasForeignKey("ClienteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("Cliente");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.CompraModule.Compra", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.PessoaModulo.Pessoa", "Fornecedor")
                        .WithMany()
                        .HasForeignKey("FornecedorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.NotaFiscalModule.Entrada.NFeEntrada", "NFeEntrada")
                        .WithMany()
                        .HasForeignKey("NFeEntradaId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("Fornecedor");

                    b.Navigation("NFeEntrada");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.CompraModule.ItemCompra", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.CompraModule.Compra", "Compra")
                        .WithMany("ItensCompra")
                        .HasForeignKey("CompraId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.ProdutoModulo.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AtualizadorPor");

                    b.Navigation("Compra");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.EmpresaModule.Empresa", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "EmpresaPai")
                        .WithMany()
                        .HasForeignKey("EmpresaPaiId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "Usuario")
                        .WithMany("Empresas")
                        .HasForeignKey("UsuarioId");

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("EmpresaPai");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.EmpresaModule.UsuarioEmpresa", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany("UsuariosEmpresas")
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "Usuario")
                        .WithMany("UsuariosEmpresas")
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Empresa");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.NotaFiscalModule.Entrada.ItemNFeEntrada", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadoPorId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId");

                    b.HasOne("Otikka.Domain.Entities.NotaFiscalModule.Entrada.NFeEntrada", "NFeEntrada")
                        .WithMany("Itens")
                        .HasForeignKey("NFeEntradaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.ProdutoModulo.Produto", "Produto")
                        .WithMany()
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("NFeEntrada");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.NotaFiscalModule.Entrada.NFeEntrada", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadoPorId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId");

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("ExcluidoPor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.OrdemServicoModulo.OrdemServicoSituacao", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.OrdemServicoModulo.OrdemServico", "OrdemServico")
                        .WithMany("OrdemServicoSituacoes")
                        .HasForeignKey("OrdemServicoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("OrdemServico");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.PessoaModulo.Pessoa", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany("EmpresaPessoas")
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.PessoaModulo.Receita", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.PessoaModulo.Pessoa", "Cliente")
                        .WithMany()
                        .HasForeignKey("ClienteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.OrdemServicoModulo.OrdemServico", "OrdemServico")
                        .WithOne("Receita")
                        .HasForeignKey("Otikka.Domain.Entities.PessoaModulo.Receita", "OrdemServicoId");

                    b.Navigation("AtualizadorPor");

                    b.Navigation("Cliente");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("OrdemServico");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.CategoriaProduto", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.ProdutoModulo.CategoriaProduto", "CategoriaProdutoPai")
                        .WithMany()
                        .HasForeignKey("CategoriaProdutoPaiId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CategoriaProdutoPai");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo.MovimentacaoEstoque", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.CompraModule.Compra", "Compra")
                        .WithMany()
                        .HasForeignKey("CompraId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.ProdutoModulo.EstoqueProduto", "EstoqueProduto")
                        .WithMany("Movimentacoes")
                        .HasForeignKey("EstoqueProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.CompraModule.ItemCompra", "ItemCompra")
                        .WithMany()
                        .HasForeignKey("ItemCompraId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Otikka.Domain.Entities.Common.TransacaoComercial", "TransacaoComercial")
                        .WithMany()
                        .HasForeignKey("TransacaoComercialId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("Compra");

                    b.Navigation("CriadoPor");

                    b.Navigation("EstoqueProduto");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("ItemCompra");

                    b.Navigation("TransacaoComercial");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.EstoqueProduto", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany("EstoqueProdutos")
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.ProdutoModulo.Produto", "Produto")
                        .WithMany("EstoqueProdutos")
                        .HasForeignKey("ProdutoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("Produto");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.MarcaProduto", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.Produto", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.ProdutoModulo.CategoriaProduto", "CategoriaProduto")
                        .WithMany("Produtos")
                        .HasForeignKey("CategoriaProdutoId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.PessoaModulo.Pessoa", "Fornecedor")
                        .WithMany()
                        .HasForeignKey("FornecedorId");

                    b.HasOne("Otikka.Domain.Entities.ProdutoModulo.MarcaProduto", "MarcaProduto")
                        .WithMany()
                        .HasForeignKey("MarcaProdutoId");

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CategoriaProduto");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("Fornecedor");

                    b.Navigation("MarcaProduto");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.UnidadeMedidaProduto", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.TransacaoFinanceiraModulo.CategoriaTransacaoFinanceira", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany("Categorias")
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.TransacaoFinanceiraModulo.FormaPagamento", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.TransacaoFinanceiraModulo.Pagamento", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany()
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.TransacaoFinanceiraModulo.FormaPagamento", "FormaPagamento")
                        .WithMany()
                        .HasForeignKey("FormaPagamentoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.Common.TransacaoComercial", "TransacaoComercial")
                        .WithMany()
                        .HasForeignKey("TransacaoComercialId");

                    b.HasOne("Otikka.Domain.Entities.TransacaoFinanceiraModulo.TransacaoFinanceira", "TransacaoFinanceira")
                        .WithMany("Pagamentos")
                        .HasForeignKey("TransacaoFinanceiraId");

                    b.Navigation("AtualizadorPor");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("FormaPagamento");

                    b.Navigation("TransacaoComercial");

                    b.Navigation("TransacaoFinanceira");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.TransacaoFinanceiraModulo.TransacaoFinanceira", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "AtualizadorPor")
                        .WithMany()
                        .HasForeignKey("AtualizadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.TransacaoFinanceiraModulo.CategoriaTransacaoFinanceira", "Categoria")
                        .WithMany()
                        .HasForeignKey("CategoriaId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "CriadoPor")
                        .WithMany()
                        .HasForeignKey("CriadorPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.EmpresaModule.Empresa", "Empresa")
                        .WithMany("TransacoesFinanceiras")
                        .HasForeignKey("EmpresaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "ExcluidoPor")
                        .WithMany()
                        .HasForeignKey("ExcluidoPorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Otikka.Domain.Entities.PessoaModulo.Pessoa", "Fornecedor")
                        .WithMany()
                        .HasForeignKey("FornecedorId");

                    b.HasOne("Otikka.Domain.Entities.Common.TransacaoComercial", "TransacaoComercial")
                        .WithOne("TransacaoFinanceira")
                        .HasForeignKey("Otikka.Domain.Entities.TransacaoFinanceiraModulo.TransacaoFinanceira", "TransacaoComercialId");

                    b.Navigation("AtualizadorPor");

                    b.Navigation("Categoria");

                    b.Navigation("CriadoPor");

                    b.Navigation("Empresa");

                    b.Navigation("ExcluidoPor");

                    b.Navigation("Fornecedor");

                    b.Navigation("TransacaoComercial");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.OrdemServicoModulo.OrdemServico", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.PessoaModulo.Pessoa", "Laboratorio")
                        .WithMany()
                        .HasForeignKey("LaboratorioId");

                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "Vendedor")
                        .WithMany("OrdensServicos")
                        .HasForeignKey("VendedorId");

                    b.Navigation("Laboratorio");

                    b.Navigation("Vendedor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.VendaModulo.Venda", b =>
                {
                    b.HasOne("Otikka.Domain.Entities.UsuarioModule.Usuario", "Vendedor")
                        .WithMany("Vendas")
                        .HasForeignKey("VendedorId");

                    b.Navigation("Vendedor");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.Common.TransacaoComercial", b =>
                {
                    b.Navigation("ProdutoServicoVendidos");

                    b.Navigation("TransacaoFinanceira");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.CompraModule.Compra", b =>
                {
                    b.Navigation("ItensCompra");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.EmpresaModule.Empresa", b =>
                {
                    b.Navigation("Categorias");

                    b.Navigation("EmpresaPessoas");

                    b.Navigation("Endereco");

                    b.Navigation("EstoqueProdutos");

                    b.Navigation("TransacoesFinanceiras");

                    b.Navigation("UsuariosEmpresas");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.NotaFiscalModule.Entrada.NFeEntrada", b =>
                {
                    b.Navigation("Itens");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.PessoaModulo.Pessoa", b =>
                {
                    b.Navigation("Enderecos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.CategoriaProduto", b =>
                {
                    b.Navigation("Produtos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.EstoqueProduto", b =>
                {
                    b.Navigation("Movimentacoes");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.ProdutoModulo.Produto", b =>
                {
                    b.Navigation("EstoqueProdutos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.TransacaoFinanceiraModulo.TransacaoFinanceira", b =>
                {
                    b.Navigation("Documentos");

                    b.Navigation("Pagamentos");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.UsuarioModule.Usuario", b =>
                {
                    b.Navigation("Empresas");

                    b.Navigation("OrdensServicos");

                    b.Navigation("UsuariosEmpresas");

                    b.Navigation("Vendas");
                });

            modelBuilder.Entity("Otikka.Domain.Entities.OrdemServicoModulo.OrdemServico", b =>
                {
                    b.Navigation("OrdemServicoSituacoes");

                    b.Navigation("Receita");
                });
#pragma warning restore 612, 618
        }
    }
}
