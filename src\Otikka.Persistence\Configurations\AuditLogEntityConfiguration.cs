using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Otikka.Domain.Entities.Common;

namespace Otikka.Persistence.Configurations;

/// <summary>
/// Configuração do Entity Framework para a entidade AuditLog
/// </summary>
public class AuditLogEntityConfiguration : IEntityTypeConfiguration<AuditLog>
{
    public void Configure(EntityTypeBuilder<AuditLog> builder)
    {
        // Chave primária
        builder.HasKey(e => e.Id);

        // Configurações das propriedades
        builder.Property(e => e.Id)
               .IsRequired();

        builder.Property(e => e.NomeTabela)
               .IsRequired()
               .HasMaxLength(100)
               .HasComment("Nome da tabela que foi modificada");

        builder.Property(e => e.EntidadeId)
               .IsRequired()
               .HasMaxLength(50)
               .HasComment("ID da entidade que foi modificada");

        builder.Property(e => e.TipoOperacao)
               .IsRequired()
               .HasMaxLength(20)
               .HasComment("Tipo de operação: INSERT, UPDATE, DELETE");

        builder.Property(e => e.ValoresAntigos)
               .HasColumnType("jsonb")
               .HasComment("Valores anteriores em formato JSON (PostgreSQL JSONB)");

        builder.Property(e => e.ValoresNovos)
               .HasColumnType("jsonb")
               .HasComment("Valores novos em formato JSON (PostgreSQL JSONB)");

        builder.Property(e => e.CamposModificados)
               .HasMaxLength(1000)
               .IsRequired(false)
               .HasComment("Campos que foram modificados (separados por vírgula)");

        builder.Property(e => e.UsuarioId)
               .IsRequired(false)
               .HasComment("ID do usuário que realizou a operação");

        builder.Property(e => e.NomeUsuario)
               .HasMaxLength(100)
               .IsRequired(false)
               .HasComment("Nome do usuário que realizou a operação");

        builder.Property(e => e.EmpresaId)
               .IsRequired(false)
               .HasComment("ID da empresa (para sistemas multi-tenant)");

        builder.Property(e => e.DataOperacao)
               .IsRequired()
               .HasComment("Data e hora da operação");

        builder.Property(e => e.EnderecoIP)
               .HasMaxLength(45)
               .IsRequired(false)
               .HasComment("Endereço IP do usuário (suporta IPv4 e IPv6)");

        builder.Property(e => e.UserAgent)
               .HasMaxLength(500)
               .IsRequired(false)
               .HasComment("User Agent do navegador");

        builder.Property(e => e.InformacoesAdicionais)
               .HasMaxLength(2000)
               .IsRequired(false)
               .HasComment("Informações adicionais sobre a operação");

        builder.Property(e => e.Processado)
               .IsRequired()
               .HasDefaultValue(false)
               .HasComment("Indica se o log foi processado com sucesso");

        builder.Property(e => e.DataProcessamento)
               .IsRequired(false)
               .HasComment("Data de processamento do log");

        builder.Property(e => e.MensagemErro)
               .HasMaxLength(2000)
               .IsRequired(false)
               .HasComment("Mensagem de erro caso o processamento falhe");

        builder.Property(e => e.TentativasProcessamento)
               .IsRequired()
               .HasDefaultValue(0)
               .HasComment("Número de tentativas de processamento");

        // Relacionamento com Usuario (opcional)
        builder.HasOne<Usuario>()
               .WithMany()
               .HasForeignKey(e => e.UsuarioId)
               .OnDelete(DeleteBehavior.SetNull);

        // Relacionamento com Empresa (opcional)
        builder.HasOne<Empresa>()
               .WithMany()
               .HasForeignKey(e => e.EmpresaId)
               .OnDelete(DeleteBehavior.SetNull);

        // Índices para melhorar performance
        builder.HasIndex(e => e.NomeTabela)
               .HasDatabaseName("IX_AuditLog_NomeTabela");

        builder.HasIndex(e => e.EntidadeId)
               .HasDatabaseName("IX_AuditLog_EntidadeId");

        builder.HasIndex(e => e.DataOperacao)
               .HasDatabaseName("IX_AuditLog_DataOperacao");

        builder.HasIndex(e => e.UsuarioId)
               .HasDatabaseName("IX_AuditLog_UsuarioId");

        builder.HasIndex(e => e.EmpresaId)
               .HasDatabaseName("IX_AuditLog_EmpresaId");

        builder.HasIndex(e => new { e.Processado, e.TentativasProcessamento })
               .HasDatabaseName("IX_AuditLog_ProcessamentoStatus")
               .HasFilter("\"Processado\" = false"); // Índice parcial para logs não processados

        // Nome da tabela no banco
        builder.ToTable("AuditLogs");
    }
}