﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Otikka.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class RenameAuditColumns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CategoriasProdutos_Usuarios_AtualizadorPorId",
                table: "CategoriasProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_CategoriasProdutos_Usuarios_CriadorPorId",
                table: "CategoriasProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_CategoriasTransacoesFinanceiras_Usuarios_AtualizadorPorId",
                table: "CategoriasTransacoesFinanceiras");

            migrationBuilder.DropForeignKey(
                name: "FK_CategoriasTransacoesFinanceiras_Usuarios_CriadorPorId",
                table: "CategoriasTransacoesFinanceiras");

            migrationBuilder.DropForeignKey(
                name: "FK_Compras_Usuarios_AtualizadorPorId",
                table: "Compras");

            migrationBuilder.DropForeignKey(
                name: "FK_Compras_Usuarios_CriadorPorId",
                table: "Compras");

            migrationBuilder.DropForeignKey(
                name: "FK_Documentos_Usuarios_AtualizadorPorId",
                table: "Documentos");

            migrationBuilder.DropForeignKey(
                name: "FK_Documentos_Usuarios_CriadorPorId",
                table: "Documentos");

            migrationBuilder.DropForeignKey(
                name: "FK_Empresas_Usuarios_AtualizadorPorId",
                table: "Empresas");

            migrationBuilder.DropForeignKey(
                name: "FK_Empresas_Usuarios_CriadorPorId",
                table: "Empresas");

            migrationBuilder.DropForeignKey(
                name: "FK_Enderecos_Usuarios_AtualizadorPorId",
                table: "Enderecos");

            migrationBuilder.DropForeignKey(
                name: "FK_Enderecos_Usuarios_CriadorPorId",
                table: "Enderecos");

            migrationBuilder.DropForeignKey(
                name: "FK_FormasPagamento_Usuarios_AtualizadorPorId",
                table: "FormasPagamento");

            migrationBuilder.DropForeignKey(
                name: "FK_FormasPagamento_Usuarios_CriadorPorId",
                table: "FormasPagamento");

            migrationBuilder.DropForeignKey(
                name: "FK_ItensCompra_Usuarios_AtualizadorPorId",
                table: "ItensCompra");

            migrationBuilder.DropForeignKey(
                name: "FK_ItensCompra_Usuarios_CriadorPorId",
                table: "ItensCompra");

            migrationBuilder.DropForeignKey(
                name: "FK_MarcasProdutos_Usuarios_AtualizadorPorId",
                table: "MarcasProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_MarcasProdutos_Usuarios_CriadorPorId",
                table: "MarcasProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_MovimentacoesEstoque_Usuarios_AtualizadorPorId",
                table: "MovimentacoesEstoque");

            migrationBuilder.DropForeignKey(
                name: "FK_MovimentacoesEstoque_Usuarios_CriadorPorId",
                table: "MovimentacoesEstoque");

            migrationBuilder.DropForeignKey(
                name: "FK_OrdensServicoSituacoes_Usuarios_AtualizadorPorId",
                table: "OrdensServicoSituacoes");

            migrationBuilder.DropForeignKey(
                name: "FK_OrdensServicoSituacoes_Usuarios_CriadorPorId",
                table: "OrdensServicoSituacoes");

            migrationBuilder.DropForeignKey(
                name: "FK_Pagamentos_Usuarios_AtualizadorPorId",
                table: "Pagamentos");

            migrationBuilder.DropForeignKey(
                name: "FK_Pagamentos_Usuarios_CriadorPorId",
                table: "Pagamentos");

            migrationBuilder.DropForeignKey(
                name: "FK_Pessoas_Usuarios_AtualizadorPorId",
                table: "Pessoas");

            migrationBuilder.DropForeignKey(
                name: "FK_Pessoas_Usuarios_CriadorPorId",
                table: "Pessoas");

            migrationBuilder.DropForeignKey(
                name: "FK_Produtos_Usuarios_AtualizadorPorId",
                table: "Produtos");

            migrationBuilder.DropForeignKey(
                name: "FK_Produtos_Usuarios_CriadorPorId",
                table: "Produtos");

            migrationBuilder.DropForeignKey(
                name: "FK_ProdutoServicoVendidos_Usuarios_AtualizadorPorId",
                table: "ProdutoServicoVendidos");

            migrationBuilder.DropForeignKey(
                name: "FK_ProdutoServicoVendidos_Usuarios_CriadorPorId",
                table: "ProdutoServicoVendidos");

            migrationBuilder.DropForeignKey(
                name: "FK_Receitas_Usuarios_AtualizadorPorId",
                table: "Receitas");

            migrationBuilder.DropForeignKey(
                name: "FK_Receitas_Usuarios_CriadorPorId",
                table: "Receitas");

            migrationBuilder.DropForeignKey(
                name: "FK_TransacoesComerciais_Usuarios_AtualizadorPorId",
                table: "TransacoesComerciais");

            migrationBuilder.DropForeignKey(
                name: "FK_TransacoesComerciais_Usuarios_CriadorPorId",
                table: "TransacoesComerciais");

            migrationBuilder.DropForeignKey(
                name: "FK_TransacoesFinanceiras_Usuarios_AtualizadorPorId",
                table: "TransacoesFinanceiras");

            migrationBuilder.DropForeignKey(
                name: "FK_TransacoesFinanceiras_Usuarios_CriadorPorId",
                table: "TransacoesFinanceiras");

            migrationBuilder.DropForeignKey(
                name: "FK_UnidadesMedidaProdutos_Usuarios_AtualizadorPorId",
                table: "UnidadesMedidaProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_UnidadesMedidaProdutos_Usuarios_CriadorPorId",
                table: "UnidadesMedidaProdutos");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "Usuarios",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "Usuarios",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "UnidadesMedidaProdutos",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "UnidadesMedidaProdutos",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_UnidadesMedidaProdutos_CriadorPorId",
                table: "UnidadesMedidaProdutos",
                newName: "IX_UnidadesMedidaProdutos_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_UnidadesMedidaProdutos_AtualizadorPorId",
                table: "UnidadesMedidaProdutos",
                newName: "IX_UnidadesMedidaProdutos_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "TransacoesFinanceiras",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "TransacoesFinanceiras",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_TransacoesFinanceiras_CriadorPorId",
                table: "TransacoesFinanceiras",
                newName: "IX_TransacoesFinanceiras_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_TransacoesFinanceiras_AtualizadorPorId",
                table: "TransacoesFinanceiras",
                newName: "IX_TransacoesFinanceiras_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "TransacoesComerciais",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "TransacoesComerciais",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_TransacoesComerciais_CriadorPorId",
                table: "TransacoesComerciais",
                newName: "IX_TransacoesComerciais_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_TransacoesComerciais_AtualizadorPorId",
                table: "TransacoesComerciais",
                newName: "IX_TransacoesComerciais_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "Receitas",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "Receitas",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Receitas_CriadorPorId",
                table: "Receitas",
                newName: "IX_Receitas_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Receitas_AtualizadorPorId",
                table: "Receitas",
                newName: "IX_Receitas_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "ProdutoServicoVendidos",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "ProdutoServicoVendidos",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_ProdutoServicoVendidos_CriadorPorId",
                table: "ProdutoServicoVendidos",
                newName: "IX_ProdutoServicoVendidos_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_ProdutoServicoVendidos_AtualizadorPorId",
                table: "ProdutoServicoVendidos",
                newName: "IX_ProdutoServicoVendidos_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "Produtos",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "Produtos",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Produtos_CriadorPorId",
                table: "Produtos",
                newName: "IX_Produtos_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Produtos_AtualizadorPorId",
                table: "Produtos",
                newName: "IX_Produtos_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "Pessoas",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "Pessoas",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Pessoas_CriadorPorId",
                table: "Pessoas",
                newName: "IX_Pessoas_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Pessoas_AtualizadorPorId",
                table: "Pessoas",
                newName: "IX_Pessoas_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "Pagamentos",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "Pagamentos",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Pagamentos_CriadorPorId",
                table: "Pagamentos",
                newName: "IX_Pagamentos_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Pagamentos_AtualizadorPorId",
                table: "Pagamentos",
                newName: "IX_Pagamentos_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "OrdensServicoSituacoes",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "OrdensServicoSituacoes",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_OrdensServicoSituacoes_CriadorPorId",
                table: "OrdensServicoSituacoes",
                newName: "IX_OrdensServicoSituacoes_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_OrdensServicoSituacoes_AtualizadorPorId",
                table: "OrdensServicoSituacoes",
                newName: "IX_OrdensServicoSituacoes_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "NFesEntrada",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "PrecoUnitario",
                table: "MovimentacoesEstoque",
                newName: "CustoUnitario");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "MovimentacoesEstoque",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "MovimentacoesEstoque",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_MovimentacoesEstoque_CriadorPorId",
                table: "MovimentacoesEstoque",
                newName: "IX_MovimentacoesEstoque_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_MovimentacoesEstoque_AtualizadorPorId",
                table: "MovimentacoesEstoque",
                newName: "IX_MovimentacoesEstoque_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "MarcasProdutos",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "MarcasProdutos",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_MarcasProdutos_CriadorPorId",
                table: "MarcasProdutos",
                newName: "IX_MarcasProdutos_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_MarcasProdutos_AtualizadorPorId",
                table: "MarcasProdutos",
                newName: "IX_MarcasProdutos_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "ItensNFeEntrada",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "ItensCompra",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "ItensCompra",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_ItensCompra_CriadorPorId",
                table: "ItensCompra",
                newName: "IX_ItensCompra_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_ItensCompra_AtualizadorPorId",
                table: "ItensCompra",
                newName: "IX_ItensCompra_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "FormasPagamento",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "FormasPagamento",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_FormasPagamento_CriadorPorId",
                table: "FormasPagamento",
                newName: "IX_FormasPagamento_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_FormasPagamento_AtualizadorPorId",
                table: "FormasPagamento",
                newName: "IX_FormasPagamento_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "Enderecos",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "Enderecos",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Enderecos_CriadorPorId",
                table: "Enderecos",
                newName: "IX_Enderecos_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Enderecos_AtualizadorPorId",
                table: "Enderecos",
                newName: "IX_Enderecos_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "Empresas",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "Empresas",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Empresas_CriadorPorId",
                table: "Empresas",
                newName: "IX_Empresas_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Empresas_AtualizadorPorId",
                table: "Empresas",
                newName: "IX_Empresas_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "Documentos",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "Documentos",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Documentos_CriadorPorId",
                table: "Documentos",
                newName: "IX_Documentos_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Documentos_AtualizadorPorId",
                table: "Documentos",
                newName: "IX_Documentos_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "Compras",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "Compras",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Compras_CriadorPorId",
                table: "Compras",
                newName: "IX_Compras_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Compras_AtualizadorPorId",
                table: "Compras",
                newName: "IX_Compras_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "CepCache",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "CategoriasTransacoesFinanceiras",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "CategoriasTransacoesFinanceiras",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_CategoriasTransacoesFinanceiras_CriadorPorId",
                table: "CategoriasTransacoesFinanceiras",
                newName: "IX_CategoriasTransacoesFinanceiras_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_CategoriasTransacoesFinanceiras_AtualizadorPorId",
                table: "CategoriasTransacoesFinanceiras",
                newName: "IX_CategoriasTransacoesFinanceiras_AtualizadoPorId");

            migrationBuilder.RenameColumn(
                name: "CriadorPorId",
                table: "CategoriasProdutos",
                newName: "CriadoPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadorPorId",
                table: "CategoriasProdutos",
                newName: "AtualizadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_CategoriasProdutos_CriadorPorId",
                table: "CategoriasProdutos",
                newName: "IX_CategoriasProdutos_CriadoPorId");

            migrationBuilder.RenameIndex(
                name: "IX_CategoriasProdutos_AtualizadorPorId",
                table: "CategoriasProdutos",
                newName: "IX_CategoriasProdutos_AtualizadoPorId");

            migrationBuilder.AddForeignKey(
                name: "FK_CategoriasProdutos_Usuarios_AtualizadoPorId",
                table: "CategoriasProdutos",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CategoriasProdutos_Usuarios_CriadoPorId",
                table: "CategoriasProdutos",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CategoriasTransacoesFinanceiras_Usuarios_AtualizadoPorId",
                table: "CategoriasTransacoesFinanceiras",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CategoriasTransacoesFinanceiras_Usuarios_CriadoPorId",
                table: "CategoriasTransacoesFinanceiras",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Compras_Usuarios_AtualizadoPorId",
                table: "Compras",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Compras_Usuarios_CriadoPorId",
                table: "Compras",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Documentos_Usuarios_AtualizadoPorId",
                table: "Documentos",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Documentos_Usuarios_CriadoPorId",
                table: "Documentos",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Empresas_Usuarios_AtualizadoPorId",
                table: "Empresas",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Empresas_Usuarios_CriadoPorId",
                table: "Empresas",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Enderecos_Usuarios_AtualizadoPorId",
                table: "Enderecos",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Enderecos_Usuarios_CriadoPorId",
                table: "Enderecos",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_FormasPagamento_Usuarios_AtualizadoPorId",
                table: "FormasPagamento",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_FormasPagamento_Usuarios_CriadoPorId",
                table: "FormasPagamento",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ItensCompra_Usuarios_AtualizadoPorId",
                table: "ItensCompra",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ItensCompra_Usuarios_CriadoPorId",
                table: "ItensCompra",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MarcasProdutos_Usuarios_AtualizadoPorId",
                table: "MarcasProdutos",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MarcasProdutos_Usuarios_CriadoPorId",
                table: "MarcasProdutos",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MovimentacoesEstoque_Usuarios_AtualizadoPorId",
                table: "MovimentacoesEstoque",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MovimentacoesEstoque_Usuarios_CriadoPorId",
                table: "MovimentacoesEstoque",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_OrdensServicoSituacoes_Usuarios_AtualizadoPorId",
                table: "OrdensServicoSituacoes",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_OrdensServicoSituacoes_Usuarios_CriadoPorId",
                table: "OrdensServicoSituacoes",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Pagamentos_Usuarios_AtualizadoPorId",
                table: "Pagamentos",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Pagamentos_Usuarios_CriadoPorId",
                table: "Pagamentos",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Pessoas_Usuarios_AtualizadoPorId",
                table: "Pessoas",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Pessoas_Usuarios_CriadoPorId",
                table: "Pessoas",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Produtos_Usuarios_AtualizadoPorId",
                table: "Produtos",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Produtos_Usuarios_CriadoPorId",
                table: "Produtos",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ProdutoServicoVendidos_Usuarios_AtualizadoPorId",
                table: "ProdutoServicoVendidos",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ProdutoServicoVendidos_Usuarios_CriadoPorId",
                table: "ProdutoServicoVendidos",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Receitas_Usuarios_AtualizadoPorId",
                table: "Receitas",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Receitas_Usuarios_CriadoPorId",
                table: "Receitas",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransacoesComerciais_Usuarios_AtualizadoPorId",
                table: "TransacoesComerciais",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransacoesComerciais_Usuarios_CriadoPorId",
                table: "TransacoesComerciais",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransacoesFinanceiras_Usuarios_AtualizadoPorId",
                table: "TransacoesFinanceiras",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransacoesFinanceiras_Usuarios_CriadoPorId",
                table: "TransacoesFinanceiras",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_UnidadesMedidaProdutos_Usuarios_AtualizadoPorId",
                table: "UnidadesMedidaProdutos",
                column: "AtualizadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_UnidadesMedidaProdutos_Usuarios_CriadoPorId",
                table: "UnidadesMedidaProdutos",
                column: "CriadoPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CategoriasProdutos_Usuarios_AtualizadoPorId",
                table: "CategoriasProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_CategoriasProdutos_Usuarios_CriadoPorId",
                table: "CategoriasProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_CategoriasTransacoesFinanceiras_Usuarios_AtualizadoPorId",
                table: "CategoriasTransacoesFinanceiras");

            migrationBuilder.DropForeignKey(
                name: "FK_CategoriasTransacoesFinanceiras_Usuarios_CriadoPorId",
                table: "CategoriasTransacoesFinanceiras");

            migrationBuilder.DropForeignKey(
                name: "FK_Compras_Usuarios_AtualizadoPorId",
                table: "Compras");

            migrationBuilder.DropForeignKey(
                name: "FK_Compras_Usuarios_CriadoPorId",
                table: "Compras");

            migrationBuilder.DropForeignKey(
                name: "FK_Documentos_Usuarios_AtualizadoPorId",
                table: "Documentos");

            migrationBuilder.DropForeignKey(
                name: "FK_Documentos_Usuarios_CriadoPorId",
                table: "Documentos");

            migrationBuilder.DropForeignKey(
                name: "FK_Empresas_Usuarios_AtualizadoPorId",
                table: "Empresas");

            migrationBuilder.DropForeignKey(
                name: "FK_Empresas_Usuarios_CriadoPorId",
                table: "Empresas");

            migrationBuilder.DropForeignKey(
                name: "FK_Enderecos_Usuarios_AtualizadoPorId",
                table: "Enderecos");

            migrationBuilder.DropForeignKey(
                name: "FK_Enderecos_Usuarios_CriadoPorId",
                table: "Enderecos");

            migrationBuilder.DropForeignKey(
                name: "FK_FormasPagamento_Usuarios_AtualizadoPorId",
                table: "FormasPagamento");

            migrationBuilder.DropForeignKey(
                name: "FK_FormasPagamento_Usuarios_CriadoPorId",
                table: "FormasPagamento");

            migrationBuilder.DropForeignKey(
                name: "FK_ItensCompra_Usuarios_AtualizadoPorId",
                table: "ItensCompra");

            migrationBuilder.DropForeignKey(
                name: "FK_ItensCompra_Usuarios_CriadoPorId",
                table: "ItensCompra");

            migrationBuilder.DropForeignKey(
                name: "FK_MarcasProdutos_Usuarios_AtualizadoPorId",
                table: "MarcasProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_MarcasProdutos_Usuarios_CriadoPorId",
                table: "MarcasProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_MovimentacoesEstoque_Usuarios_AtualizadoPorId",
                table: "MovimentacoesEstoque");

            migrationBuilder.DropForeignKey(
                name: "FK_MovimentacoesEstoque_Usuarios_CriadoPorId",
                table: "MovimentacoesEstoque");

            migrationBuilder.DropForeignKey(
                name: "FK_OrdensServicoSituacoes_Usuarios_AtualizadoPorId",
                table: "OrdensServicoSituacoes");

            migrationBuilder.DropForeignKey(
                name: "FK_OrdensServicoSituacoes_Usuarios_CriadoPorId",
                table: "OrdensServicoSituacoes");

            migrationBuilder.DropForeignKey(
                name: "FK_Pagamentos_Usuarios_AtualizadoPorId",
                table: "Pagamentos");

            migrationBuilder.DropForeignKey(
                name: "FK_Pagamentos_Usuarios_CriadoPorId",
                table: "Pagamentos");

            migrationBuilder.DropForeignKey(
                name: "FK_Pessoas_Usuarios_AtualizadoPorId",
                table: "Pessoas");

            migrationBuilder.DropForeignKey(
                name: "FK_Pessoas_Usuarios_CriadoPorId",
                table: "Pessoas");

            migrationBuilder.DropForeignKey(
                name: "FK_Produtos_Usuarios_AtualizadoPorId",
                table: "Produtos");

            migrationBuilder.DropForeignKey(
                name: "FK_Produtos_Usuarios_CriadoPorId",
                table: "Produtos");

            migrationBuilder.DropForeignKey(
                name: "FK_ProdutoServicoVendidos_Usuarios_AtualizadoPorId",
                table: "ProdutoServicoVendidos");

            migrationBuilder.DropForeignKey(
                name: "FK_ProdutoServicoVendidos_Usuarios_CriadoPorId",
                table: "ProdutoServicoVendidos");

            migrationBuilder.DropForeignKey(
                name: "FK_Receitas_Usuarios_AtualizadoPorId",
                table: "Receitas");

            migrationBuilder.DropForeignKey(
                name: "FK_Receitas_Usuarios_CriadoPorId",
                table: "Receitas");

            migrationBuilder.DropForeignKey(
                name: "FK_TransacoesComerciais_Usuarios_AtualizadoPorId",
                table: "TransacoesComerciais");

            migrationBuilder.DropForeignKey(
                name: "FK_TransacoesComerciais_Usuarios_CriadoPorId",
                table: "TransacoesComerciais");

            migrationBuilder.DropForeignKey(
                name: "FK_TransacoesFinanceiras_Usuarios_AtualizadoPorId",
                table: "TransacoesFinanceiras");

            migrationBuilder.DropForeignKey(
                name: "FK_TransacoesFinanceiras_Usuarios_CriadoPorId",
                table: "TransacoesFinanceiras");

            migrationBuilder.DropForeignKey(
                name: "FK_UnidadesMedidaProdutos_Usuarios_AtualizadoPorId",
                table: "UnidadesMedidaProdutos");

            migrationBuilder.DropForeignKey(
                name: "FK_UnidadesMedidaProdutos_Usuarios_CriadoPorId",
                table: "UnidadesMedidaProdutos");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "Usuarios",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "Usuarios",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "UnidadesMedidaProdutos",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "UnidadesMedidaProdutos",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_UnidadesMedidaProdutos_CriadoPorId",
                table: "UnidadesMedidaProdutos",
                newName: "IX_UnidadesMedidaProdutos_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_UnidadesMedidaProdutos_AtualizadoPorId",
                table: "UnidadesMedidaProdutos",
                newName: "IX_UnidadesMedidaProdutos_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "TransacoesFinanceiras",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "TransacoesFinanceiras",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_TransacoesFinanceiras_CriadoPorId",
                table: "TransacoesFinanceiras",
                newName: "IX_TransacoesFinanceiras_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_TransacoesFinanceiras_AtualizadoPorId",
                table: "TransacoesFinanceiras",
                newName: "IX_TransacoesFinanceiras_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "TransacoesComerciais",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "TransacoesComerciais",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_TransacoesComerciais_CriadoPorId",
                table: "TransacoesComerciais",
                newName: "IX_TransacoesComerciais_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_TransacoesComerciais_AtualizadoPorId",
                table: "TransacoesComerciais",
                newName: "IX_TransacoesComerciais_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "Receitas",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "Receitas",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Receitas_CriadoPorId",
                table: "Receitas",
                newName: "IX_Receitas_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Receitas_AtualizadoPorId",
                table: "Receitas",
                newName: "IX_Receitas_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "ProdutoServicoVendidos",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "ProdutoServicoVendidos",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_ProdutoServicoVendidos_CriadoPorId",
                table: "ProdutoServicoVendidos",
                newName: "IX_ProdutoServicoVendidos_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_ProdutoServicoVendidos_AtualizadoPorId",
                table: "ProdutoServicoVendidos",
                newName: "IX_ProdutoServicoVendidos_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "Produtos",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "Produtos",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Produtos_CriadoPorId",
                table: "Produtos",
                newName: "IX_Produtos_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Produtos_AtualizadoPorId",
                table: "Produtos",
                newName: "IX_Produtos_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "Pessoas",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "Pessoas",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Pessoas_CriadoPorId",
                table: "Pessoas",
                newName: "IX_Pessoas_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Pessoas_AtualizadoPorId",
                table: "Pessoas",
                newName: "IX_Pessoas_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "Pagamentos",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "Pagamentos",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Pagamentos_CriadoPorId",
                table: "Pagamentos",
                newName: "IX_Pagamentos_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Pagamentos_AtualizadoPorId",
                table: "Pagamentos",
                newName: "IX_Pagamentos_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "OrdensServicoSituacoes",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "OrdensServicoSituacoes",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_OrdensServicoSituacoes_CriadoPorId",
                table: "OrdensServicoSituacoes",
                newName: "IX_OrdensServicoSituacoes_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_OrdensServicoSituacoes_AtualizadoPorId",
                table: "OrdensServicoSituacoes",
                newName: "IX_OrdensServicoSituacoes_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "NFesEntrada",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "CustoUnitario",
                table: "MovimentacoesEstoque",
                newName: "PrecoUnitario");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "MovimentacoesEstoque",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "MovimentacoesEstoque",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_MovimentacoesEstoque_CriadoPorId",
                table: "MovimentacoesEstoque",
                newName: "IX_MovimentacoesEstoque_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_MovimentacoesEstoque_AtualizadoPorId",
                table: "MovimentacoesEstoque",
                newName: "IX_MovimentacoesEstoque_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "MarcasProdutos",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "MarcasProdutos",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_MarcasProdutos_CriadoPorId",
                table: "MarcasProdutos",
                newName: "IX_MarcasProdutos_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_MarcasProdutos_AtualizadoPorId",
                table: "MarcasProdutos",
                newName: "IX_MarcasProdutos_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "ItensNFeEntrada",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "ItensCompra",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "ItensCompra",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_ItensCompra_CriadoPorId",
                table: "ItensCompra",
                newName: "IX_ItensCompra_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_ItensCompra_AtualizadoPorId",
                table: "ItensCompra",
                newName: "IX_ItensCompra_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "FormasPagamento",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "FormasPagamento",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_FormasPagamento_CriadoPorId",
                table: "FormasPagamento",
                newName: "IX_FormasPagamento_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_FormasPagamento_AtualizadoPorId",
                table: "FormasPagamento",
                newName: "IX_FormasPagamento_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "Enderecos",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "Enderecos",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Enderecos_CriadoPorId",
                table: "Enderecos",
                newName: "IX_Enderecos_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Enderecos_AtualizadoPorId",
                table: "Enderecos",
                newName: "IX_Enderecos_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "Empresas",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "Empresas",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Empresas_CriadoPorId",
                table: "Empresas",
                newName: "IX_Empresas_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Empresas_AtualizadoPorId",
                table: "Empresas",
                newName: "IX_Empresas_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "Documentos",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "Documentos",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Documentos_CriadoPorId",
                table: "Documentos",
                newName: "IX_Documentos_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Documentos_AtualizadoPorId",
                table: "Documentos",
                newName: "IX_Documentos_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "Compras",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "Compras",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Compras_CriadoPorId",
                table: "Compras",
                newName: "IX_Compras_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_Compras_AtualizadoPorId",
                table: "Compras",
                newName: "IX_Compras_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "CepCache",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "CategoriasTransacoesFinanceiras",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "CategoriasTransacoesFinanceiras",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_CategoriasTransacoesFinanceiras_CriadoPorId",
                table: "CategoriasTransacoesFinanceiras",
                newName: "IX_CategoriasTransacoesFinanceiras_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_CategoriasTransacoesFinanceiras_AtualizadoPorId",
                table: "CategoriasTransacoesFinanceiras",
                newName: "IX_CategoriasTransacoesFinanceiras_AtualizadorPorId");

            migrationBuilder.RenameColumn(
                name: "CriadoPorId",
                table: "CategoriasProdutos",
                newName: "CriadorPorId");

            migrationBuilder.RenameColumn(
                name: "AtualizadoPorId",
                table: "CategoriasProdutos",
                newName: "AtualizadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_CategoriasProdutos_CriadoPorId",
                table: "CategoriasProdutos",
                newName: "IX_CategoriasProdutos_CriadorPorId");

            migrationBuilder.RenameIndex(
                name: "IX_CategoriasProdutos_AtualizadoPorId",
                table: "CategoriasProdutos",
                newName: "IX_CategoriasProdutos_AtualizadorPorId");

            migrationBuilder.AddForeignKey(
                name: "FK_CategoriasProdutos_Usuarios_AtualizadorPorId",
                table: "CategoriasProdutos",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CategoriasProdutos_Usuarios_CriadorPorId",
                table: "CategoriasProdutos",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CategoriasTransacoesFinanceiras_Usuarios_AtualizadorPorId",
                table: "CategoriasTransacoesFinanceiras",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CategoriasTransacoesFinanceiras_Usuarios_CriadorPorId",
                table: "CategoriasTransacoesFinanceiras",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Compras_Usuarios_AtualizadorPorId",
                table: "Compras",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Compras_Usuarios_CriadorPorId",
                table: "Compras",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Documentos_Usuarios_AtualizadorPorId",
                table: "Documentos",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Documentos_Usuarios_CriadorPorId",
                table: "Documentos",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Empresas_Usuarios_AtualizadorPorId",
                table: "Empresas",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Empresas_Usuarios_CriadorPorId",
                table: "Empresas",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Enderecos_Usuarios_AtualizadorPorId",
                table: "Enderecos",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Enderecos_Usuarios_CriadorPorId",
                table: "Enderecos",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_FormasPagamento_Usuarios_AtualizadorPorId",
                table: "FormasPagamento",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_FormasPagamento_Usuarios_CriadorPorId",
                table: "FormasPagamento",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ItensCompra_Usuarios_AtualizadorPorId",
                table: "ItensCompra",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ItensCompra_Usuarios_CriadorPorId",
                table: "ItensCompra",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MarcasProdutos_Usuarios_AtualizadorPorId",
                table: "MarcasProdutos",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MarcasProdutos_Usuarios_CriadorPorId",
                table: "MarcasProdutos",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MovimentacoesEstoque_Usuarios_AtualizadorPorId",
                table: "MovimentacoesEstoque",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MovimentacoesEstoque_Usuarios_CriadorPorId",
                table: "MovimentacoesEstoque",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_OrdensServicoSituacoes_Usuarios_AtualizadorPorId",
                table: "OrdensServicoSituacoes",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_OrdensServicoSituacoes_Usuarios_CriadorPorId",
                table: "OrdensServicoSituacoes",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Pagamentos_Usuarios_AtualizadorPorId",
                table: "Pagamentos",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Pagamentos_Usuarios_CriadorPorId",
                table: "Pagamentos",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Pessoas_Usuarios_AtualizadorPorId",
                table: "Pessoas",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Pessoas_Usuarios_CriadorPorId",
                table: "Pessoas",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Produtos_Usuarios_AtualizadorPorId",
                table: "Produtos",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Produtos_Usuarios_CriadorPorId",
                table: "Produtos",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ProdutoServicoVendidos_Usuarios_AtualizadorPorId",
                table: "ProdutoServicoVendidos",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ProdutoServicoVendidos_Usuarios_CriadorPorId",
                table: "ProdutoServicoVendidos",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Receitas_Usuarios_AtualizadorPorId",
                table: "Receitas",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Receitas_Usuarios_CriadorPorId",
                table: "Receitas",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransacoesComerciais_Usuarios_AtualizadorPorId",
                table: "TransacoesComerciais",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransacoesComerciais_Usuarios_CriadorPorId",
                table: "TransacoesComerciais",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransacoesFinanceiras_Usuarios_AtualizadorPorId",
                table: "TransacoesFinanceiras",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransacoesFinanceiras_Usuarios_CriadorPorId",
                table: "TransacoesFinanceiras",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_UnidadesMedidaProdutos_Usuarios_AtualizadorPorId",
                table: "UnidadesMedidaProdutos",
                column: "AtualizadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_UnidadesMedidaProdutos_Usuarios_CriadorPorId",
                table: "UnidadesMedidaProdutos",
                column: "CriadorPorId",
                principalTable: "Usuarios",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
