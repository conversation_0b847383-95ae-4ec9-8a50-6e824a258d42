@inherits PageBase

@using Otikka.Application.Features.MarcaProduto.Queries.ListAllMarcasProduto
@using Otikka.Application.Features.CategoriaProduto.Queries.ListAllCategoriasProduto
@using Otikka.Application.Features.Pessoa.Queries.ListFornecedores
@using Otikka.Application.Utilities
@using Otikka.Domain.Enums
@using Otikka.Domain.Common.ExtensionMethods

@inject ILogger<ProdutoForm> Logger

<!-- Header <PERSON> -->
<div class="page-title-head d-flex align-items-sm-center flex-sm-row flex-column gap-2">
    <div class="flex-grow-1">
        <h4 class="fs-18 fw-semibold mb-0">@(EhEdicao ? "Editar" : "Cadastrar") Produto</h4>
        <p class="text-muted mb-0">@(EhEdicao ? "Atualize as informações do produto" : "Preencha os dados para cadastrar um novo produto")</p>
    </div>
    <div class="d-flex gap-2">
        <a href="@Application.Routes.ProdutoListar" class="btn btn-light">
            <i class="ti ti-arrow-left me-1"></i> Voltar
        </a>
    </div>
</div>

<EditForm Model="Produto" OnValidSubmit="Submit" FormName="Register">
    <FluentValidationValidator Options="@(options => options.IncludeRuleSets("ProdutoValidacao"))" />

    <!-- Card: Status do Produto -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm me-3">
                        <span class="avatar-title bg-@(Produto?.Ativo == true ? "success" : "danger") bg-opacity-10 rounded">
                            <i class="ti ti-@(Produto?.Ativo == true ? "check" : "x") text-@(Produto?.Ativo == true ? "success" : "danger") fs-20"></i>
                        </span>
                    </div>
                    <div>
                        <h5 class="card-title mb-0">Status do Produto</h5>
                        <p class="text-muted mb-0">Controle de disponibilidade para venda</p>
                    </div>
                </div>
                <div class="form-check form-switch">
                    <InputCheckbox @bind-Value="Produto!.Ativo" class="form-check-input" id="ativoSwitch" disabled="@(!EhEdicao)" />
                    <label class="form-check-label ms-2" for="ativoSwitch">
                        <strong>@(Produto?.Ativo == true ? "Produto Ativo" : "Produto Inativo")</strong>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Card: Informações Básicas -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex align-items-center">
                <div class="avatar-sm me-3">
                    <span class="avatar-title bg-primary bg-opacity-10 rounded">
                        <i class="ti ti-package text-primary fs-20"></i>
                    </span>
                </div>
                <div>
                    <h5 class="card-title mb-0">Informações Básicas</h5>
                    <p class="text-muted mb-0">Dados principais do produto</p>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row gy-4">
                <div class="col-lg-4 col-md-6">
                    <div>
                        <label for="codigo" class="form-label">Código de Referência <span class="text-danger">*</span></label>
                        <InputText @bind-Value="Produto!.Codigo" autocomplete="not" class="form-control" id="codigo" placeholder="Digite o código de referência"/>
                        <ValidationMessage For="() => Produto!.Codigo"/>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div>
                        <label for="name" class="form-label">Nome do Produto <span class="text-danger">*</span></label>
                        <InputText @bind-Value="Produto!.Nome" autocomplete="not" class="form-control" id="name" placeholder="Digite o nome do produto"/>
                        <ValidationMessage For="() => Produto!.Nome"/>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="d-flex align-items-end h-100">
                        <div class="form-check form-switch">
                            <InputCheckbox @bind-Value="Produto!.Encomenda" class="form-check-input" id="encomendaSwitch" />
                            <label class="form-check-label" for="encomendaSwitch">
                                <strong>Lente (Produto de Encomenda)</strong>
                                <small class="d-block text-muted">Produto será enviado para laboratório</small>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row gy-4 mt-3">
                <div class="col-lg-6 col-md-6">
                    <div>
                        <label for="ean" class="form-label">Código de Barras (EAN)</label>
                        <InputText @bind-Value="Produto!.EAN" autocomplete="not" class="form-control" id="ean" placeholder="Digite o código EAN" onkeypress="return (event.charCode !=8 && event.charCode ==0 || (event.charCode >= 48 && event.charCode <= 57))"/>
                        <ValidationMessage For="() => Produto!.EAN"/>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div>
                        <label for="sku" class="form-label">SKU</label>
                        <InputText @bind-Value="Produto!.SKU" autocomplete="not" class="form-control" id="sku" placeholder="Digite o SKU" onkeypress="return (event.charCode !=8 && event.charCode ==0 || (event.charCode >= 48 && event.charCode <= 57))"/>
                        <ValidationMessage For="() => Produto!.SKU"/>
                    </div>
                </div>
            </div>
            <div class="row gy-4 mt-3">
                <div class="col-lg-6 col-md-6">
                    <div>
                        <label for="precoVenda" class="form-label">Preço de Venda</label>
                        <InputNumber @bind-Value="Produto!.PrecoVenda" class="form-control" id="precoVenda" placeholder="0,00" min="0" step="0.01"/>
                        <small class="text-muted">Preço de venda do produto</small>
                        <ValidationMessage For="() => Produto!.PrecoVenda"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Card: Classificação -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex align-items-center">
                <div class="avatar-sm me-3">
                    <span class="avatar-title bg-info bg-opacity-10 rounded">
                        <i class="ti ti-category text-info fs-20"></i>
                    </span>
                </div>
                <div>
                    <h5 class="card-title mb-0">Classificação</h5>
                    <p class="text-muted mb-0">Categoria, marca e fornecedor</p>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row gy-4">
                <div class="col-lg-4 col-md-6">
                    <div>
                        <label for="fornecedor" class="form-label">Fornecedor</label>
                        @if (Fornecedores is not null)
                        {
                            <InputSelect @bind-Value="Produto!.FornecedorId" autocomplete="not" class="form-select" id="fornecedor">
                                <option value="">Selecione um fornecedor</option>
                                @foreach (var entity in Fornecedores)
                                {
                                    <option value="@entity.Id">@entity.Nome</option>
                                }
                            </InputSelect>
                        }
                        else
                        {
                            <select class="form-select" disabled>
                                <option>Nenhum fornecedor cadastrado</option>
                            </select>
                        }
                        <ValidationMessage For="() => Produto!.FornecedorId"/>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div>
                        <label for="categorias" class="form-label">Categoria</label>
                        @if (CategoriasProdutos is not null)
                        {
                            <InputSelect @bind-Value="Produto!.CategoriaProdutoId" autocomplete="not" class="form-select" id="categorias">
                                <option value="">Selecione uma categoria</option>
                                @foreach (var entity in CategoriasProdutos)
                                {
                                    <option value="@entity.Id">@entity.Nome</option>
                                }
                            </InputSelect>
                        }
                        else
                        {
                            <select class="form-select" disabled>
                                <option>Nenhuma categoria cadastrada</option>
                            </select>
                        }
                        <ValidationMessage For="() => Produto!.CategoriaProdutoId"/>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div>
                        <label for="marcas" class="form-label">Marca</label>
                        @if (MarcasProduto is not null)
                        {
                            <InputSelect @bind-Value="Produto!.MarcaProdutoId" autocomplete="not" class="form-select" id="marcas">
                                <option value="">Selecione uma marca</option>
                                @foreach (var entity in MarcasProduto)
                                {
                                    <option value="@entity.Id">@entity.Nome</option>
                                }
                            </InputSelect>
                        }
                        else
                        {
                            <select class="form-select" disabled>
                                <option>Nenhuma marca cadastrada</option>
                            </select>
                        }
                        <ValidationMessage For="() => Produto!.MarcaProdutoId"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Card: Controle de Estoque -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm me-3">
                        <span class="avatar-title bg-warning bg-opacity-10 rounded">
                            <i class="ti ti-package text-warning fs-20"></i>
                        </span>
                    </div>
                    <div>
                        <h5 class="card-title mb-0">Controle de Estoque</h5>
                        <p class="text-muted mb-0">Configurações de estoque do produto</p>
                    </div>
                </div>
                <div class="form-check form-switch">
                    <InputCheckbox @bind-Value="Produto!.ControleEstoque" class="form-check-input" id="controleEstoque"/>
                    <label class="form-check-label ms-2" for="controleEstoque">
                        <strong>Controlar Estoque</strong>
                    </label>
                </div>
            </div>
        </div>
        @if (Produto!.ControleEstoque)
        {
            <div class="card-body">
                @if (EhEdicao)
                {
                    <!-- Mensagem para edição de produto -->
                    <div class="alert alert-info d-flex align-items-center" role="alert">
                        <i class="ti ti-info-circle me-2 fs-18"></i>
                        <div>
                            <strong>Controle de Estoque:</strong> O controle de estoque deve ser gerenciado no menu "Estoque".
                        </div>
                    </div>
                }
                else
                {
                    <!-- Campos de estoque integrados ao produto -->
                    <div class="row gy-4">
                        <div class="col-lg-4 col-md-6">
                            <div>
                                <label for="quantidadeEstoqueCorrente" class="form-label">Quantidade em Estoque</label>
                                <InputNumber @bind-Value="Produto!.QuantidadeEstoqueCorrente" class="form-control" id="quantidadeEstoqueCorrente" placeholder="0" min="0"/>
                                <small class="text-muted">Quantidade atual disponível em estoque</small>
                                <ValidationMessage For="() => Produto!.QuantidadeEstoqueCorrente"/>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div>
                                <label for="quantidadeEstoqueMinimo" class="form-label">Estoque Mínimo</label>
                                <InputNumber @bind-Value="Produto!.QuantidadeEstoqueMinimo" class="form-control" id="quantidadeEstoqueMinimo" placeholder="0" min="0"/>
                                <small class="text-muted">Quantidade mínima para alerta de estoque baixo</small>
                                <ValidationMessage For="() => Produto!.QuantidadeEstoqueMinimo"/>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <div>
                                <label for="custoUnitarioEstoque" class="form-label">Custo Unitário</label>
                                <InputNumber id="custoUnitarioEstoque" class="form-control" @bind-Value="CustoUnitario" placeholder="0,00" min="0" step="0.01" />
                                <small class="text-muted">Custo unitário para movimentação de estoque</small>
                                <ValidationMessage For="() => CustoUnitario"/>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
    </div>

    <!-- Card: Informações da NFe e NFCe -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex align-items-center">
                <div class="avatar-sm me-3">
                    <span class="avatar-title bg-success bg-opacity-10 rounded">
                        <i class="ti ti-file-invoice text-success fs-20"></i>
                    </span>
                </div>
                <div>
                    <h5 class="card-title mb-0">Informações da NFe e NFCe</h5>
                    <p class="text-muted mb-0">Dados fiscais para emissão de notas fiscais</p>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row gy-4">
                <div class="col-lg-6 col-md-6">
                    <div>
                        <label for="ncm" class="form-label">NCM (Nomenclatura Comum do Mercosul)</label>
                        <InputText @bind-Value="Produto!.NCM" class="form-control" id="ncm" placeholder="Digite o código NCM (ex: 12345678)" maxlength="8"/>
                        <small class="text-muted">Código de 8 dígitos para classificação fiscal</small>
                        <ValidationMessage For="() => Produto!.NCM"/>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div>
                        <label for="origemMercadoria" class="form-label">Origem da Mercadoria</label>
                        <InputSelect @bind-Value="Produto!.OrigemMercadoria" class="form-select" id="origemMercadoria">
                            <option value="">Selecione a origem da mercadoria</option>
                            @foreach (OrigemMercadoria origem in Enum.GetValues(typeof(OrigemMercadoria)))
                            {
                                <option value="@origem">@origem.GetDisplayName()</option>
                            }
                        </InputSelect>
                        <small class="text-muted">Classificação conforme especificação da NFe/NFCe</small>
                        <ValidationMessage For="() => Produto!.OrigemMercadoria"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Componente de Estoque (se controle estiver ativo) -->
    

    <!-- Card: Ações -->
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">@(EhEdicao ? "Atualizar Produto" : "Cadastrar Produto")</h6>
                    <p class="text-muted mb-0">@(EhEdicao ? "Confirme as alterações realizadas" : "Verifique os dados antes de salvar")</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="@Application.Routes.ProdutoListar" class="btn btn-light">
                        <i class="ti ti-arrow-left me-1"></i> Cancelar
                    </a>
                    <SubmitButton IsProcessing="Processing" ButtonClass="btn-primary">
                        <i class="ti ti-device-floppy me-1"></i>@(EhEdicao ? "Atualizar Produto" : "Cadastrar Produto")
                    </SubmitButton>
                </div>
            </div>
        </div>
    </div>
</EditForm>


@code {
    public bool Processando { get; set; }
    [Parameter] public bool EhEdicao { get; set; }
    [Parameter] public Domain.Entities.ProdutoModulo.Produto? Produto { get; set; }
    [Parameter] public EventCallback OnSave { get; set; }

    /// <summary>
    /// Custo unitário para movimentação de estoque inicial
    /// </summary>
    [Parameter] public decimal? CustoUnitario { get; set; }

    private List<Domain.Entities.ProdutoModulo.CategoriaProduto>? CategoriasProdutos { get; set; }
    private List<Domain.Entities.ProdutoModulo.MarcaProduto>? MarcasProduto { get; set; }
    private List<Otikka.Domain.Entities.PessoaModulo.Pessoa>? Fornecedores { get; set; }

    protected override async Task OnInitializedAsync()
    {
        Logger.LogInformation("Carregando dados para formulário de produto");

        try
        {
            var empresaId = await GetEmpresaIdAsync();

            // Carregar categorias usando CQRS
            var categoriasResult = await MessageBus.InvokeAsync<Result<List<Domain.Entities.ProdutoModulo.CategoriaProduto>>>(
                new ListAllCategoriasProduto() { EmpresaId = await GetEmpresaIdAsync() }
            );
            if (categoriasResult.IsSuccess)
            {
                CategoriasProdutos = categoriasResult.Value;
                Logger.LogInformation("Categorias carregadas com sucesso: {Count}", CategoriasProdutos.Count);
            }
            else
            {
                Logger.LogWarning("Erro ao carregar categorias: {Error}", categoriasResult.Errors.FirstOrDefault()?.Message);
            }

            // Carregar marcas usando CQRS
            var marcasResult = await MessageBus.InvokeAsync<Result<List<Domain.Entities.ProdutoModulo.MarcaProduto>>>(
                new ListAllMarcasProduto() { EmpresaId = await GetEmpresaIdAsync() }
            );
            if (marcasResult.IsSuccess)
            {
                MarcasProduto = marcasResult.Value;
                Logger.LogInformation("Marcas carregadas com sucesso: {Count}", MarcasProduto.Count);
            }
            else
            {
                Logger.LogWarning("Erro ao carregar marcas: {Error}", marcasResult.Errors.FirstOrDefault()?.Message);
            }

            // Carregar fornecedores usando CQRS
            var fornecedoresResult = await MessageBus.InvokeAsync<Result<List<Otikka.Domain.Entities.PessoaModulo.Pessoa>>>(
                new ListFornecedores { EmpresaId = empresaId });
            if (fornecedoresResult.IsSuccess)
            {
                Fornecedores = fornecedoresResult.Value;
                Logger.LogInformation("Fornecedores carregados com sucesso: {Count}", Fornecedores.Count);
            }
            else
            {
                Logger.LogWarning("Erro ao carregar fornecedores: {Error}", fornecedoresResult.Errors.FirstOrDefault()?.Message);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao carregar dados para formulário de produto");
        }
    }

    private async Task Submit()
    {
        await ProcessingChange(true);
        await OnSave.InvokeAsync();
        await ProcessingChange(false);
    }
}