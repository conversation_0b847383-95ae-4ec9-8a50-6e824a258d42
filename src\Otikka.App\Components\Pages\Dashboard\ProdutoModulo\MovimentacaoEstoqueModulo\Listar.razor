@using Otikka.Application.Models
@using Otikka.Application.Models.Requests
@using Otikka.Application.Features.MovimentacaoEstoque.Queries.ListMovimentacoesEstoque
@using Otikka.Application.Features.MovimentacaoEstoque.Commands.DeleteMovimentacaoEstoque

@using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo
@using Otikka.Domain.Entities.ProdutoModulo
@using Otikka.Domain.Enums
@using FluentResults
@using Wolverine
@using Syncfusion.Blazor.DropDowns
@attribute [Route(Application.Routes.MovimentacaoEstoqueListar)]

@inherits PageBase

<PageTitle>Movimentações de Estoque</PageTitle>

<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Movimentações de Estoque</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="javascript: void(0);">Dashboard</a></li>
                    <li class="breadcrumb-item active">Movimentações de Estoque</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex align-items-center flex-wrap gap-2">
                    <div class="flex-grow-1">
                        <a href="@Application.Routes.MovimentacaoEstoqueCadastrar" class="btn btn-success add-btn">
                            <i class="ri-add-line align-bottom me-1"></i> Adicionar
                        </a>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="hstack text-nowrap gap-2">
                            <input @bind="PaginationParams.SearchWord" @onkeypress="@(async (e) => { if (e.Key == "Enter") await OnSearch(); })"
                                   class="form-control" placeholder="Buscar..." />
                            <button @onclick="OnSearch" class="btn btn-primary">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-nowrap">
                        <thead>
                            <tr>
                                <th scope="col">Data</th>
                                <th scope="col">Produto</th>
                                <th scope="col">Tipo</th>
                                <th scope="col">Quantidade</th>
                                <th scope="col">Preço Unit.</th>
                                <th scope="col">ID Compra</th>
                                <th scope="col">Descrição</th>
                                <th scope="col">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Paginated == null)
                            {
                                <tr>
                                    <td colspan="8">Carregando...</td>
                                </tr>
                            }
                            else if (Paginated.Items.Count == 0)
                            {
                                <tr>
                                    <td colspan="8">Nenhuma movimentação encontrada!</td>
                                </tr>
                            }
                            else
                            {
                                @foreach (var item in Paginated.Items)
                                {
                                    <tr>
                                        <td>
                                            @item.DataMovimentacao.ToString("dd/MM/yyyy HH:mm")
                                        </td>
                                        <td>
                                            @item.Produto?.Nome
                                        </td>
                                        <td>
                                            <span class="badge @GetTipoMovimentacaoClass(item.TipoMovimentacao)">
                                                @GetTipoMovimentacaoText(item.TipoMovimentacao)
                                            </span>
                                        </td>
                                        <td>
                                            <span class="@(item.Quantidade > 0 ? "text-success" : "text-danger")">
                                                @(item.Quantidade > 0 ? "+" : "")@item.Quantidade
                                            </span>
                                        </td>
                                        <td>
                                            @if (item.CustoUnitario.HasValue)
                                {
                                    @item.CustoUnitario.Value.ToString("C")
                                }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.CompraId.HasValue)
                                            {
                                                <a href="@Application.Routes.GerarRota(Application.Routes.CompraEditar, item.CompraId.Value.ToString())"
                                                   class="text-primary text-decoration-underline">
                                                    @item.CompraId.Value.ToString("N")[..8]...
                                                </a>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Descricao))
                                            {
                                                @(item.Descricao.Length > 50 ? item.Descricao.Substring(0, 50) + "..." : item.Descricao)
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                @if (item.CompraId is not null)
                                                {
                                                    <a href="@Application.Routes.GerarRota(Application.Routes.MovimentacaoEstoqueVisualizar, item.Id.ToString())"
                                                       class="btn btn-sm btn-soft-primary" title="Visualizar">
                                                        <i class="ri-eye-line"></i>
                                                    </a>
                                                }
                                                else
                                                {
                                                    <a href="@Application.Routes.GerarRota(Application.Routes.MovimentacaoEstoqueEditar, item.Id.ToString())"
                                                       class="btn btn-sm btn-soft-info" title="Editar">
                                                        <i class="ri-edit-line"></i>
                                                    </a>
                                                    <button @onclick="() => Excluir(item)" type="button"
                                                            class="btn btn-sm btn-soft-danger" title="Excluir">
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
                @if (Paginated != null)
                {
                    <Pagination Paginated="@Paginated" OnPageChanged="@OnPageChanged" />
                }
            </div>
        </div>
    </div>
</div>

@code {
    private PaginationParameters PaginationParams = new PaginationParameters(1, 20);
    private PaginatedList<MovimentacaoEstoque>? Paginated;

    protected override async Task OnInitializedAsync()
    {
        PaginationParams.PageSize = Configuration.GetValue<int>("Pagination:PageSize");
        await LoadDataAsync();
    }

    private async Task Excluir(MovimentacaoEstoque entidade)
    {
        if (await AlertService.ShowConfirmDelete())
        {
            try
            {
                var result = await MessageBus.InvokeAsync<Result>(new DeleteMovimentacaoEstoque(entidade.Id));

                if (result.IsSuccess)
                {
                    await AlertService.ShowSuccessMessage("Movimentação de estoque excluída com sucesso!");
                    await LoadDataAsync();
                }
                else
                {
                    await AlertService.ShowError("Erro!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao excluir movimentação");
                }
            }
            catch (Exception ex)
            {
                await AlertService.ShowError("Erro!", $"Erro inesperado: {ex.Message}");
            }
        }
    }

    private async Task OnSearch()
    {
        PaginationParams.PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task OnPageChanged(int pageNumber)
    {
        PaginationParams.PageIndex = pageNumber;
        await LoadDataAsync();
    }



    private async Task OnSort(string columnName)
    {
        if (PaginationParams.SortColumn == columnName)
        {
            PaginationParams.Ascending = !PaginationParams.Ascending;
        }
        else
        {
            PaginationParams.SortColumn = columnName;
            PaginationParams.Ascending = true;
        }
        PaginationParams.PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();
            var request = new ListMovimentacoesEstoque
            {
                EmpresaId = empresaId,
                PageIndex = PaginationParams.PageIndex,
                PageSize = PaginationParams.PageSize,
                SearchWord = PaginationParams.SearchWord,
                SortColumn = PaginationParams.SortColumn,
                Ascending = PaginationParams.Ascending,
                Filtro = new MovimentacaoEstoqueFiltroRequest
                {
                    PalavraBusca = PaginationParams.SearchWord
                }
            };

            var result = await MessageBus.InvokeAsync<Result<PaginatedList<MovimentacaoEstoque>>>(request);

            if (result.IsSuccess)
            {
                Paginated = result.Value;
            }
            else
            {
                await AlertService.ShowError("Erro!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao carregar movimentações");
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro!", $"Erro inesperado: {ex.Message}");
        }
    }

    private string GetTipoMovimentacaoClass(TipoMovimentacaoEstoque tipo)
    {
        return tipo switch
        {
            TipoMovimentacaoEstoque.Entrada => "bg-success",
            TipoMovimentacaoEstoque.Saida => "bg-danger",
            TipoMovimentacaoEstoque.Ajuste => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetTipoMovimentacaoText(TipoMovimentacaoEstoque tipo)
    {
        return tipo switch
        {
            TipoMovimentacaoEstoque.Entrada => "Entrada",
            TipoMovimentacaoEstoque.Saida => "Saída",
            TipoMovimentacaoEstoque.Ajuste => "Ajuste",
            _ => "Desconhecido"
        };
    }
}
