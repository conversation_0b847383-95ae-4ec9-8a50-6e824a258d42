@using Otikka.App.Components.Pages.Dashboard.FinanceiroModulo.TransacaoFinanceira.Componentes
@using Otikka.Application.Features.TransacaoFinanceira.Commands.CreateTransacaoFinanceira
@using Otikka.Domain.Entities.Common
@using Otikka.Domain.Entities.TransacaoFinanceiraModulo
@using Otikka.Domain.Enums

@attribute [Route(Application.Routes.TransacaoFinanceiraCadastrar)]
@inherits PageBase

@if(Type is not null){
    <PageTitle>
            @TipoTransacaoFinanceiraConverter.Converter(Type) - Cadastro
    </PageTitle>
}

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header align-items-center d-flex">
                @if(Type is not null)
                {
                    <h4 class="card-title mb-0 flex-grow-1">Cadastrar @TipoTransacaoFinanceiraConverter.Converter(Type)</h4>
                }
            </div>
            <div class="card-body">
                <TransacaoFinanceiraForm Type="@Type" OnSave="Save" TransacaoFinanceira="transacaoFinanceira"/>
            </div>
        </div>
    </div>
</div>
@code {
    [Parameter] public string? Type { get; set; }
    
    public TransacaoFinanceira? transacaoFinanceira { get; set; }
    
    protected override void OnInitialized()
    {
        transacaoFinanceira = new TransacaoFinanceira() { Pagamentos = new List<Pagamento>(), Documentos = new List<Documento>() };
    }

    private async Task Save()
    {
        
        try
        {
            // Configura o tipo da transação baseado no parâmetro Type
            if (Enum.TryParse(Type, true, out TipoTransacaoFinanceira tipoTransacao))
            {
                transacaoFinanceira!.TipoTransacaoFinanceira = tipoTransacao;
            }

            // Configura a empresa
            transacaoFinanceira!.EmpresaId = await GetEmpresaIdAsync();

            // Cria o comando
            var comando = new CreateTransacaoFinanceira
            {
                TipoTransacaoFinanceira = transacaoFinanceira.TipoTransacaoFinanceira,
                Descricao = transacaoFinanceira.Descricao,
                VencimentoData = transacaoFinanceira.VencimentoData,
                ValorTotal = transacaoFinanceira.ValorTotal,
                ValorPago = transacaoFinanceira.ValorPago,
                ValorRestante = transacaoFinanceira.ValorRestante,
                Repetir = transacaoFinanceira.Repetir,
                RepetirVezes = transacaoFinanceira.RepetirVezes,
                Observacao = transacaoFinanceira.Observacao,
                CategoriaId = transacaoFinanceira.CategoriaId,
                FornecedorId = transacaoFinanceira.FornecedorId,

                EmpresaId = transacaoFinanceira.EmpresaId,
                Pagamentos = transacaoFinanceira.Pagamentos,
                Documentos = transacaoFinanceira.Documentos,
                
                // Campos de auditoria
                CriadoPorId = await GetUsuarioIdLoggedAsync(),
                DataCriacao = DateTime.UtcNow
            };

            // Envia o comando via MessageBus
            var resultado = await MessageBus.InvokeAsync<Result>(comando);

            if (resultado.IsSuccess)
            {
                await AlertService.ShowSuccessMessage(Application.Messages.SucessoSalvar);
                NavigationManager.NavigateTo(Application.Routes.GerarRota(Application.Routes.TransacaoFinanceiraListar, Type!));
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", resultado.Errors.Select(e => e.Message)));
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowAlert("Erro", $"Erro ao salvar transação financeira: {ex.Message}");
        }
    }
}
