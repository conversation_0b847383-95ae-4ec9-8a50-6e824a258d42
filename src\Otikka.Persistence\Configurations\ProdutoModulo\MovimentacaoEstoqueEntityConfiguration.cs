using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo;

namespace Otikka.Persistence.Configurations.ProdutoModulo;

public class MovimentacaoEstoqueEntityConfiguration : EntidadeBaseEntityConfiguration<MovimentacaoEstoque>
{
    public override void Configure(EntityTypeBuilder<MovimentacaoEstoque> builder)
    {
        base.Configure(builder);

        // Configuração de precisão para campos decimais
        builder.Property(e => e.CustoUnitario)
               .HasColumnType("decimal(10,2)");

        // Configuração de conversão para enums
        builder.Property(e => e.TipoMovimentacao)
               .HasConversion<string>();

        builder.Property(e => e.TipoTransacaoComercial)
               .HasConversion<string>();

        // Configuração de tamanho para campos de texto
        builder.Property(e => e.Descricao)
               .HasMaxLength(500);

        // Relacionamentos
        builder.HasOne(e => e.Produto)
               .WithMany(p => p.MovimentacoesEstoque)
               .HasForeignKey(e => e.ProdutoId)
               .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Compra)
               .WithMany()
               .HasForeignKey(e => e.CompraId)
               .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.ItemCompra)
               .WithMany()
               .HasForeignKey(e => e.ItemCompraId)
               .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.TransacaoComercial)
               .WithMany()
               .HasForeignKey(e => e.TransacaoComercialId)
               .OnDelete(DeleteBehavior.SetNull);
    }
}
