namespace Otikka.Application.Models;

/// <summary>
/// Configurações para o sistema de auditoria
/// </summary>
public class AuditLogConfiguration
{
    /// <summary>
    /// Seção de configuração no appsettings.json
    /// </summary>
    public const string SectionName = "AuditLog";

    /// <summary>
    /// Indica se o sistema de auditoria está habilitado
    /// </summary>
    public bool Habilitado { get; set; } = true;

    /// <summary>
    /// Número de dias para manter os logs de auditoria antes de removê-los
    /// </summary>
    public int DiasRetencao { get; set; } = 90;

    /// <summary>
    /// Número máximo de tentativas para processar um log falhado
    /// </summary>
    public int MaximoTentativas { get; set; } = 3;

    /// <summary>
    /// Intervalo em minutos para reprocessar logs falhados
    /// </summary>
    public int IntervaloReprocessamento { get; set; } = 30;

    /// <summary>
    /// Tamanho do lote para processamento em massa de logs
    /// </summary>
    public int TamanhoLote { get; set; } = 100;

    /// <summary>
    /// Indica se deve capturar valores antigos das entidades
    /// </summary>
    public bool CapturarValoresAntigos { get; set; } = true;

    /// <summary>
    /// Indica se deve capturar valores novos das entidades
    /// </summary>
    public bool CapturarValoresNovos { get; set; } = true;

    /// <summary>
    /// Indica se deve capturar informações do usuário
    /// </summary>
    public bool CapturarInformacoesUsuario { get; set; } = true;

    /// <summary>
    /// Indica se deve capturar informações da requisição HTTP
    /// </summary>
    public bool CapturarInformacoesRequisicao { get; set; } = true;

    /// <summary>
    /// Lista de tabelas que devem ser ignoradas pelo sistema de auditoria
    /// </summary>
    public List<string> TabelasIgnoradas { get; set; } = new()
    {
        "AuditLogs",
        "__EFMigrationsHistory",
        "HangfireJobs",
        "HangfireJobParameters",
        "HangfireJobQueue",
        "HangfireJobState",
        "HangfireServer",
        "HangfireSet",
        "HangfireCounter",
        "HangfireHash",
        "HangfireList",
        "HangfireAggregatedCounter"
    };

    /// <summary>
    /// Lista de campos que devem ser ignorados pelo sistema de auditoria
    /// </summary>
    public List<string> CamposIgnorados { get; set; } = new()
    {
        "DataCriacao",
        "DataAtualizacao",
        "CriadoPorId",
        "AtualizadoPorId",
        "Senha",
        "Hash",
        "Token",
        "RefreshToken"
    };

    /// <summary>
    /// Configurações específicas para diferentes tipos de operação
    /// </summary>
    public OperationConfiguration Operacoes { get; set; } = new();
}

/// <summary>
/// Configurações específicas para diferentes tipos de operação
/// </summary>
public class OperationConfiguration
{
    /// <summary>
    /// Configurações para operações de inserção
    /// </summary>
    public bool AuditarInsercoes { get; set; } = true;

    /// <summary>
    /// Configurações para operações de atualização
    /// </summary>
    public bool AuditarAtualizacoes { get; set; } = true;

    /// <summary>
    /// Configurações para operações de exclusão
    /// </summary>
    public bool AuditarExclusoes { get; set; } = true;

    /// <summary>
    /// Configurações para operações de exclusão lógica (soft delete)
    /// </summary>
    public bool AuditarExclusoesLogicas { get; set; } = true;
}