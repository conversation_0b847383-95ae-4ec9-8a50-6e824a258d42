@using Otikka.App.Components.Pages.Dashboard.FormaPagamentoModulo.Componentes
@using Otikka.Application.Features.FormaPagamento.Commands.CreateFormaPagamento
@using Otikka.Domain.Entities.TransacaoFinanceiraModulo
@attribute [Route(Application.Routes.FormaPagamentoCadastrar)]
@inherits PageBase
@inject ILogger<Cadastrar> Logger

<PageTitle>
    Forma de Pagamento - Cadastro
</PageTitle>

<FormaPagamentoForm EhEdicao="@false" FormaPagamento="FormaPagamento" OnSave="Save" />

@code {
    [SupplyParameterFromForm]
    private CreateFormaPagamento FormaPagamento { get; set; } = new();

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JsRuntime.InvokeVoidAsync("applyThemeConfig");
            await JsRuntime.InvokeVoidAsync("loadApps");
        }
    }

    private async Task Save()
    {
        try
        {
            // Campos de auditoria
            FormaPagamento.EmpresaId = await GetEmpresaIdAsync();
            FormaPagamento.CriadoPorId = await GetUsuarioIdLoggedAsync();
            FormaPagamento.DataCriacao = DateTime.UtcNow;

            // Log para debugging
            Logger.LogInformation("Iniciando cadastro de forma de pagamento pelo usuário");

            var result = await MessageBus.InvokeAsync<Result>(FormaPagamento);
            
            if (result.IsSuccess)
            {
                SuccessMessage = "Forma de pagamento cadastrada com sucesso!";
                
                // Aguarda um pouco para mostrar a mensagem e redireciona para a lista
                NavigationManager.NavigateTo(Application.Routes.FormaPagamentoListar);
            }
            else
            {
                var errorMessages = result.Errors.Select(e => e.Message).ToList();
                ErrorMessage = string.Join("; ", errorMessages);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro inesperado durante o cadastro da forma de pagamento");
            ErrorMessage = $"Ocorreu um erro inesperado: {ex.Message}";
        }
    }
}