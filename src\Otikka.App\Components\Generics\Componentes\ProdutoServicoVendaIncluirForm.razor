@using Otikka.App.Components.Pages
@using Otikka.Application.Models.Requests
@using Otikka.Application.Models.ViewModels
@using Otikka.Application.Utilities
@using Otikka.Domain.Entities.Common
@using Otikka.Domain.Entities.ProdutoModulo
@using Otikka.Domain.Entities.TransacaoFinanceiraModulo
@using Otikka.Application.Features.MarcaProduto.Queries.ListAllMarcasProduto
@using Otikka.Application.Features.CategoriaProduto.Queries.ListAllCategoriasProduto
@using Blazored.Modal
@using Blazored.Modal.Services
@using Otikka.App.Components.Pages.Dashboard.ProdutoModulo.Produto.Componentes
@using Microsoft.Extensions.Logging
@inherits PageBase

@inject ILogger<ProdutoServicoVendaIncluirForm> Logger

@if (Paginated is not null)
{
    <div class="p-2 shadow-none" style="margin-left: -12px; margin-top: -16px; margin-right:-12px;">

        <div class="row">
            <div class="col-md-6 col-lg-4">
                <label for="Marca">Nome</label>
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Digite sua pesquisa"
                           @bind="Filtro.PalavraBusca">
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div>
                    <label for="Marca">Marca</label>
                    <select @bind="Filtro.MarcaId" class="form-select" id="loja">
                        @if (MarcasProdutos is not null)
                        {
                            <option value="@String.Empty">-Selecione-</option>
                            @foreach (var entidade in MarcasProdutos)
                            {
                                <option value="@entidade.Id">@entidade.Nome</option>
                            }
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div>
                    <label for="categoria">Categoria</label>
                    <select @bind="Filtro.CategoriaId" class="form-select" id="categoria">
                        @if (CategoriasProdutos is not null)
                        {
                            <option value="@String.Empty">-Selecione-</option>
                            @foreach (var entidade in CategoriasProdutos)
                            {
                                <option value="@entidade.Id">@CategoriaProdutoUtilidade.Hierarquia(CategoriasProdutos, entidade.Id, true)</option>
                            }
                        }
                    </select>
                </div>
            </div>


            <div class="row gy-4 mt-0">
                <div class="col-md-6 d-flex gap-2">
                    <button class="btn btn-outline-success material-shadow-none" type="button" @onclick="OnSearch">
                        <i class="ti ti-search me-1"></i> Pesquisar
                    </button>
                    <button class="btn btn-outline-primary material-shadow-none" type="button" @onclick="AbrirModalCadastroRapido">
                        <i class="ti ti-plus me-1"></i> Novo Produto
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="row gy-4 table-responsive">
        <table class="table table-bordered">
            <thead class="table-light">
                <tr>
                    <th>Categoria</th>
                    <th>Nome</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                @if (Paginated.Items.Count == 0)
                {
                    <tr>
                        <td colspan="4">Nenhum registro!</td>
                    </tr>
                }
                else
                {
                    @foreach (var produto in Paginated.Items)
                    {
                        <tr>
                            <td>
                                @if (CategoriasProdutos is not null)
                                {
                                    @CategoriaProdutoUtilidade.Hierarquia(CategoriasProdutos, (produto.CategoriaId.HasValue ? produto.CategoriaId.Value : Guid.Empty), true)
                                }
                            </td>
                            <td>
                                @produto.Marca - @produto.Nome (@produto.Codigo)
                                <br />
                                <span class="text-muted">@produto.Preco.ToString("C")</span>

                                @if (produto.ControleEstoque)
                                {
                                    <br />
                                    @if (produto.EstoqueZero)
                                    {
                                        <span class="badge bg-danger"><strong>Estoque:</strong> Esgotado</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-success"><strong>Estoque:</strong> @produto.Estoque</span>
                                    }
                                }
                            </td>
                            <td>
                                @if (produto.ControleEstoque && produto.EstoqueZero)
                                {
                                    <button type="button" class="btn btn-ghost-danger" disabled="disabled">Esgotado</button>
                                }
                                else
                                {
                                    <button @onclick="() => ProdutoSelecionado(produto)" type="button" class="btn btn-success btn-label">
                                        <i class="bx bx-plus-medical label-icon align-middle fs-16 me-2"></i> Incluir
                                    </button>
                                }
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
    <Pagination Paginated="@Paginated" OnPageChanged="@OnPageChanged" />
}
else
{
    <CarregandoComponente />
}

@code {
    private ProdutoFiltroRequest Filtro = new ProdutoFiltroRequest();


    [Parameter] public bool IncluirEncomenda { get; set; }
    [Parameter] public List<MarcaProduto>? MarcasProdutos { get; set; }
    [Parameter] public List<Produto>? Produtos { get; set; }
    [Parameter] public List<ProdutoViewModel>? ProdutosCompleto { get; set; }
    [Parameter] public PaginatedList<ProdutoViewModel>? Paginated { get; set; }
    [Parameter] public TransacaoComercial? OrdemServicoOuVenda { get; set; }

    private List<FormaPagamento>? FormasPagamento { get; set; }
    private ProdutoServicoVendido? ProdutoVenda { get; set; }

    private int PageIndex = 1;
    private int PageSize;
    public List<CategoriaProduto>? CategoriasProdutos { get; set; }

    protected override async Task OnInitializedAsync()
    {
        ProdutoVenda = new ProdutoServicoVendido() { Id = Guid.NewGuid() };

        // Carregar marcas usando handler
        try
        {
            var marcasQuery = new ListAllMarcasProduto() { EmpresaId = await GetEmpresaIdAsync() };
            var marcasResult = await MessageBus.InvokeAsync<FluentResults.Result<List<MarcaProduto>>>(marcasQuery);

            if (marcasResult.IsSuccess)
            {
                MarcasProdutos = marcasResult.Value;
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", marcasResult.Errors.Select(e => e.Message)));
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowAlert("Erro", $"Erro ao carregar marcas: {ex.Message}");
        }

        // Carregar categorias usando handler
        try
        {
            var categoriasQuery = new ListAllCategoriasProduto() { EmpresaId = await GetEmpresaIdAsync() };
            var categoriasResult = await MessageBus.InvokeAsync<FluentResults.Result<List<CategoriaProduto>>>(categoriasQuery);

            if (categoriasResult.IsSuccess)
            {
                CategoriasProdutos = categoriasResult.Value;
            }
            else
            {
                await AlertService.ShowAlert("Erro", string.Join("; ", categoriasResult.Errors.Select(e => e.Message)));
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowAlert("Erro", $"Erro ao carregar categorias: {ex.Message}");
        }

        PageSize = Configuration.GetValue<int>("Pagination:PageSize");
    }

    protected override void OnParametersSet()
    {
        ProdutosCompleto = new List<ProdutoViewModel>();
        if (Produtos is not null)
        {
            foreach (var produto in Produtos)
            {
                var produtoVM = new ProdutoViewModel();
                produtoVM.Id = produto.Id;
                produtoVM.Codigo = produto.Codigo;
                produtoVM.Nome = produto.Nome;
                produtoVM.CategoriaId = produto.CategoriaProdutoId;
                produtoVM.Marca = (produto.MarcaProduto is not null ? produto.MarcaProduto.Nome : "");
                produtoVM.MarcaId = (produto.MarcaProduto is not null ? produto.MarcaProduto.Id : null);
                produtoVM.ControleEstoque = produto.ControleEstoque;
                // Usar as propriedades de estoque diretamente do produto
                produtoVM.Estoque = produto.QuantidadeEstoqueCorrente;
                produtoVM.Preco = produto.PrecoVenda;

                if (produtoVM.ControleEstoque && produtoVM.Estoque == 0)
                {
                    produtoVM.EstoqueZero = true;
                }

                ProdutosCompleto.Add(produtoVM);
            }
        }

        LoadDataAsync();
    }
    public async Task Incluir()
    {
        if (ProdutoVenda is not null)
        {
            if (FormasPagamento is not null)
            {
                ProdutoVenda.EmpresaId = await GetEmpresaIdAsync();
            }

            await BlazoredModal.CloseAsync(ModalResult.Ok(ProdutoVenda));

            ProdutoVenda = new();
        }
    }

    private async Task Fechar()
    {
        await BlazoredModal.CancelAsync();
    }

    public async Task ProdutoSelecionado(ProdutoViewModel produto)
    {
        if (ProdutoVenda is not null)
        {
            ProdutoVenda.Quantidade = 1;
            ProdutoVenda.PrecoVenda = produto.Preco;
            ProdutoVenda.PrecoProduto = produto.Preco;
            ProdutoVenda.ProdutoId = produto.Id;
            await Incluir();
        }
    }

    public void LoadDataAsync()
    {
        if (ProdutosCompleto is null) return;

        var items = ProdutosCompleto
            .OrderBy(a => a.Nome)
            .ToList();

        if (!string.IsNullOrWhiteSpace(Filtro.PalavraBusca))
        {
            items = items.Where(a =>
                a.Nome.ToLower().Contains(Filtro.PalavraBusca.ToLower()) ||
                (a.Marca != null && a.Marca.ToLower().Contains(Filtro.PalavraBusca.ToLower())) ||
                (a.Codigo != null && a.Codigo.ToLower().Contains(Filtro.PalavraBusca.ToLower()))
            ).ToList();
        }

        if (Filtro.MarcaId is not null && Filtro.MarcaId != Guid.Empty)
            items = items.Where(a => a.MarcaId == Filtro.MarcaId).ToList();

        if (Filtro.CategoriasId is not null && Filtro.CategoriasId.Count > 0)
            items = items.Where(a => a.CategoriaId.HasValue && Filtro.CategoriasId.Contains(a.CategoriaId.Value)).ToList();


        items = items
            .Skip((PageIndex - 1) * PageSize)
            .Take(PageSize)
            .ToList();

        var count = ProdutosCompleto.Count();

        int totalPages = (int)Math.Ceiling(count / (double)PageSize);

        Paginated = new PaginatedList<ProdutoViewModel>(items, PageIndex, totalPages);
    }
    private void OnPageChanged(int pageNumber)
    {
        PageIndex = pageNumber;
        LoadDataAsync();
    }
    private void OnSearch()
    {
        if (Filtro.CategoriaId.HasValue && CategoriasProdutos is not null)
        {
            Filtro.CategoriasId = CategoriaProdutoUtilidade.ObterIdsCategoriasFilhas(Filtro.CategoriaId.Value, CategoriasProdutos);
        }

        PageIndex = 1;
        LoadDataAsync();
    }
    
    private async Task AbrirModalCadastroRapido()
    {
        try
        {
            var options = new ModalOptions() { Size = ModalSize.Large };
            var modal = Modal.Show<Otikka.App.Components.Pages.Dashboard.ProdutoModulo.Produto.Componentes.ProdutoCadastroRapidoForm>("Cadastro Rápido de Produto", options);
            var result = await modal.Result;
            
            if (result.Confirmed && result.Data is Domain.Entities.ProdutoModulo.Produto produtoCadastrado)
            {
                // Criar um ProdutoViewModel a partir do produto cadastrado
                var produtoVM = new ProdutoViewModel
                {
                    Id = produtoCadastrado.Id,
                    Codigo = produtoCadastrado.Codigo,
                    Nome = produtoCadastrado.Nome,
                    CategoriaId = produtoCadastrado.CategoriaProdutoId,
                    MarcaId = produtoCadastrado.MarcaProdutoId,
                    Marca = produtoCadastrado.MarcaProduto?.Nome ?? "",
                    ControleEstoque = produtoCadastrado.ControleEstoque,
                    Estoque = produtoCadastrado.QuantidadeEstoqueCorrente,
                    Preco = produtoCadastrado.PrecoVenda,
                    EstoqueZero = produtoCadastrado.ControleEstoque && produtoCadastrado.QuantidadeEstoqueCorrente == 0
                };
                
                // Adicionar o produto à lista de produtos
                if (ProdutosCompleto == null)
                    ProdutosCompleto = new List<ProdutoViewModel>();
                    
                ProdutosCompleto.Add(produtoVM);
                
                // Recarregar os dados para mostrar o novo produto
                LoadDataAsync();
                
                // Selecionar automaticamente o produto recém-cadastrado
                await ProdutoSelecionado(produtoVM);
                
                await ToastService.ShowSuccess("Produto cadastrado e selecionado com sucesso!");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao abrir modal de cadastro rápido de produto");
            await AlertService.ShowError("Erro", $"Não foi possível abrir o formulário de cadastro: {ex.Message}");
        }
    }

}