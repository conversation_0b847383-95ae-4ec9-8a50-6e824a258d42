﻿@using Otikka.Application.Features.FormaPagamento.Commands.DeleteFormaPagamento
@using Otikka.Application.Features.FormaPagamento.Queries.ListFormasPagamento
@using Otikka.Application.Models
@using Otikka.Domain.Entities.TransacaoFinanceiraModulo
@using Otikka.App.Libraries.UI.Components
@attribute [Route(Application.Routes.FormaPagamentoListar)]
@inherits PageBase

<PageTitle>
    Forma de Pagamento - Lista
</PageTitle>

<div class="page-title-head d-flex align-items-sm-center flex-sm-row flex-column gap-2">
    <div class="flex-grow-1">
        <h4 class="fs-18 fw-semibold mb-0">Formas de Pagamento</h4>
    </div>

    <div class="text-end">
        <ol class="breadcrumb m-0 py-0">
            <li class="breadcrumb-item"><a href="@Application.Routes.Dashboard">Dashboard</a></li>
            <li class="breadcrumb-item active">Formas de Pagamento</li>
        </ol>
    </div>
</div>

<div class="card">
    <div class="card-header border-bottom border-dashed d-flex align-items-center justify-content-between">
        <h4 class="header-title">Filtros</h4>
        <div class="flex-shrink-0">
            <button type="button" @onclick="NavigateToCreateAsync" class="btn btn-primary btn-sm material-shadow-none">
                <i class="ri-file-List-3-line align-middle"></i> Cadastrar
            </button>
        </div>
    </div><!-- end card header -->

    <div class="card-body">
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="Digite sua pesquisa" @bind="Request.SearchWord">
                <button class="btn btn-outline-secondary" type="button" @onclick="OnSearch">
                    <i class="ti ti-search"></i>
                </button>
            </div>
        </div>


        <Message SuccessMessage="@SuccessMessage" ErrorMessage="@ErrorMessage" WarningMessage="@WarningMessage" />
        <div class="table-responsive mt-4 table-margin-width-24">
            <table class="table table-borderless table-centered align-middle table-nowrap mb-0">
                <thead class="text-muted table-light">
                    <tr>
                        <SortableTableHeader ColumnName="Nome" CurrentSortColumn="@Request.SortColumn" Ascending="@Request.Ascending" OnSort="@OnSort">
                            Nome
                        </SortableTableHeader>
                        <SortableTableHeader ColumnName="Prioridade" CurrentSortColumn="@Request.SortColumn" Ascending="@Request.Ascending" OnSort="@OnSort">
                            Prioridade
                        </SortableTableHeader>
                        
                        <th scope="col">Ação</th>
                    </tr>
                </thead>
                <tbody>
                    
                        @if (Paginated == null)
                    {
                        <tr>
                            <td colspan="3">Carregando...</td>
                        </tr>
                    }
                    else if (Paginated.Items.Count == 0)
                    {
                        <tr>
                            <td colspan="3">Nenhum registro!</td>
                        </tr>
                    }
                    else
                    {
                        @foreach (var item in Paginated.Items)
                        {
                            <tr>
                                <td>
                                    @item.Nome
                                </td>
                                <td>
                                    @(item.Prioridade?.ToString() ?? "-")
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="@Application.Routes.GerarRota(Application.Routes.FormaPagamentoEditar, item.Id.ToString())" class="btn btn-sm btn-soft-info ms-2">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-soft-danger" @onclick="() => ExcluirFormaPagamentoAsync(item)" title="Excluir">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                        
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
        <Pagination Paginated="@Paginated" OnPageChanged="@OnPageChanged" />
    </div>
</div>
@code {
    ListFormasPagamento Request = new ListFormasPagamento();
    private PaginatedList<FormaPagamento>? Paginated;

    protected override async Task OnInitializedAsync()
    {
        Request.PageSize = Configuration.GetValue<int>("Pagination:PageSize");
        Request.EmpresaId = await GetEmpresaIdAsync();

        await CarregarDadosAsync();
    }

    private async Task OnSearch()
    {
        await CarregarDadosAsync();
    }

    private async Task OnPageChanged(int pageNumber)
    {
        Request.PageIndex = pageNumber;
        await CarregarDadosAsync();
    }

    private async Task CarregarDadosAsync()
    {
        var res = await MessageBus.InvokeAsync<Result<PaginatedList<FormaPagamento>>>(Request);

        if (res.IsFailed)
        {
            ErrorMessage = res.Errors.FirstOrDefault()?.Message ?? "Erro ao carregar dados!";
            return;
        }

        Paginated = res.Value;
    }

    private void NavigateToCreateAsync()
    {
        NavigationManager.NavigateTo(Application.Routes.FormaPagamentoCadastrar);
    }

    private async Task ExcluirFormaPagamentoAsync(FormaPagamento formaPagamento)
    {
        try
        {
            // Pede confirmação do usuário
            var confirmacao = await JsRuntime.InvokeAsync<bool>("confirm", 
                $"Tem certeza que deseja excluir a forma de pagamento '{formaPagamento.Nome}'?\n\nEsta ação não pode ser desfeita.");
            
            if (!confirmacao)
                return;

            // Executa a exclusão
            var command = new DeleteFormaPagamento { Id = formaPagamento.Id };
            var result = await MessageBus.InvokeAsync<Result>(command);

            if (result.IsSuccess)
            {
                SuccessMessage = $"Forma de pagamento '{formaPagamento.Nome}' excluída com sucesso!";
                
                // Recarrega a lista
                await CarregarDadosAsync();
            }
            else
            {
                var errorMessages = result.Errors.Select(e => e.Message).ToList();
                ErrorMessage = string.Join("; ", errorMessages);
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Erro inesperado ao excluir forma de pagamento: {ex.Message}";
        }
    }

    private async Task OnSort(string columnName)
    {
        if (Request.SortColumn == columnName)
        {
            // Se já está ordenando por esta coluna, inverte a direção
            Request.Ascending = !Request.Ascending;
        }
        else
        {
            // Se é uma nova coluna, define como crescente
            Request.SortColumn = columnName;
            Request.Ascending = true;
        }

        // Volta para a primeira página ao ordenar
        Request.PageIndex = 1;
        await CarregarDadosAsync();
    }
}
