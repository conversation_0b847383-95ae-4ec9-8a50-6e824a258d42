using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Otikka.Domain.Entities.Common;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using Otikka.Domain.Entities;
using Otikka.Application.Contracts.Infrastructure;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Hangfire;

namespace Otikka.Persistence.Interceptors
{
    /// <summary>
    /// Interceptor responsável por preencher automaticamente os campos de auditoria
    /// e enfileirar logs de auditoria via Hangfire para processamento em background
    /// </summary>
    public class AuditInterceptor : SaveChangesInterceptor
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IAuditLogService _auditLogService;
        private readonly ILogger<AuditInterceptor> _logger;
        private readonly List<AuditLogInfo> _logsParaProcessar = new();

        public AuditInterceptor(
            IHttpContextAccessor httpContextAccessor,
            IAuditLogService auditLogService,
            ILogger<AuditInterceptor> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _auditLogService = auditLogService;
            _logger = logger;
        }

        public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
        {
            CapturarInformacoesAuditoria(eventData);
            return PreencherCamposAuditoria(eventData, result);
        }

        public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(DbContextEventData eventData, InterceptionResult<int> result, CancellationToken cancellationToken = default)
        {
            CapturarInformacoesAuditoria(eventData);
            return PreencherCamposAuditoria(eventData, result);
        }

        public override int SavedChanges(SaveChangesCompletedEventData eventData, int result)
        {
            _ = Task.Run(() => ProcessarLogsCapturadosAsync());
            return base.SavedChanges(eventData, result);
        }

        public override async ValueTask<int> SavedChangesAsync(SaveChangesCompletedEventData eventData, int result, CancellationToken cancellationToken = default)
        {
            _ = Task.Run(() => ProcessarLogsCapturadosAsync());
            return await base.SavedChangesAsync(eventData, result, cancellationToken);
        }

        /// <summary>
        /// Preenche automaticamente os campos de auditoria baseado no estado da entidade
        /// </summary>
        private InterceptionResult<int> PreencherCamposAuditoria(DbContextEventData eventData, InterceptionResult<int> result)
        {
            if (eventData.Context is null) return result;

            var usuarioId = ObterUsuarioLogado();
            var agora = DateTimeOffset.UtcNow;

            foreach (var entry in eventData.Context.ChangeTracker.Entries())
            {
                if (entry.Entity is not EntidadeBase entidade) continue;

                switch (entry.State)
                {
                    case EntityState.Added:
                        // Preenche campos de criação apenas se não foram definidos
                        if (entidade.CriadoPorId == null && usuarioId.HasValue)
                        {
                            entidade.CriadoPorId = usuarioId.Value;
                        }
                        if (entidade.DataCriacao == default)
                        {
                            entidade.DataCriacao = agora;
                        }
                        break;

                    case EntityState.Modified:
                        // Preenche campos de atualização
                        if (usuarioId.HasValue)
                        {
                            entidade.AtualizadoPorId = usuarioId.Value;
                        }
                        entidade.DataAtualizacao = agora;
                        break;
                }
            }

            return result;
        }

        /// <summary>
        /// Obtém o ID do usuário logado a partir do contexto HTTP
        /// </summary>
        private Guid? ObterUsuarioLogado()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.User?.Identity?.IsAuthenticated == true)
                {
                    var userIdClaim = httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                                   ?? httpContext.User.FindFirst("sub")?.Value
                                   ?? httpContext.User.FindFirst("userId")?.Value;

                    if (Guid.TryParse(userIdClaim, out var userId))
                    {
                        return userId;
                    }
                }
            }
            catch
            {
                // Em caso de erro, retorna null para não quebrar o fluxo
                // O sistema pode funcionar sem auditoria em cenários específicos
            }

            return null;
        }

        /// <summary>
        /// Captura informações de auditoria antes de salvar as mudanças
        /// </summary>
        private void CapturarInformacoesAuditoria(DbContextEventData eventData)
    {
        if (eventData.Context is null) return;

        var usuarioId = ObterUsuarioLogado();
        var httpContext = _httpContextAccessor.HttpContext;
        var enderecoIP = httpContext?.Connection?.RemoteIpAddress?.ToString();
        var userAgent = httpContext?.Request?.Headers["User-Agent"].ToString();
        var nomeUsuario = httpContext?.User?.Identity?.Name;
        var empresaId = ObterEmpresaId();

        foreach (var entry in eventData.Context.ChangeTracker.Entries())
        {
            if (entry.Entity is not EntidadeBase entidade) continue;
            if (entry.State == EntityState.Unchanged || entry.State == EntityState.Detached) continue;

            var nomeTabela = entry.Entity.GetType().Name;
            var entidadeId = ObterEntidadeId(entry);
            var tipoOperacao = ObterTipoOperacao(entry.State);
            var valoresAntigos = CapturarValoresAntigos(entry);
            var valoresNovos = CapturarValoresNovos(entry);
            var camposModificados = CapturarCamposModificados(entry);

            var auditLogInfo = new AuditLogInfo
            {
                NomeTabela = nomeTabela,
                EntidadeId = entidadeId,
                TipoOperacao = tipoOperacao,
                ValoresAntigos = valoresAntigos,
                ValoresNovos = valoresNovos,
                CamposModificados = camposModificados,
                UsuarioId = usuarioId,
                NomeUsuario = nomeUsuario,
                EmpresaId = empresaId,
                EnderecoIP = enderecoIP,
                UserAgent = userAgent,
                DataOperacao = DateTimeOffset.UtcNow
            };

            _logsParaProcessar.Add(auditLogInfo);
        }
    }

        /// <summary>
        /// Processa os logs capturados enfileirando-os via Hangfire
        /// </summary>
        private async Task ProcessarLogsCapturadosAsync()
    {
        if (!_logsParaProcessar.Any()) return;

        try
        {
            var logsParaEnfileirar = _logsParaProcessar.ToList();
            _logsParaProcessar.Clear();

            foreach (var logInfo in logsParaEnfileirar)
            {
                var auditLog = new AuditLog
                {
                    Id = Guid.NewGuid(),
                    NomeTabela = logInfo.NomeTabela,
                    EntidadeId = logInfo.EntidadeId,
                    TipoOperacao = logInfo.TipoOperacao,
                    ValoresAntigos = logInfo.ValoresAntigos,
                    ValoresNovos = logInfo.ValoresNovos,
                    CamposModificados = logInfo.CamposModificados,
                    UsuarioId = logInfo.UsuarioId,
                    NomeUsuario = logInfo.NomeUsuario,
                    EmpresaId = logInfo.EmpresaId,
                    EnderecoIP = logInfo.EnderecoIP,
                    UserAgent = logInfo.UserAgent,
                    DataOperacao = logInfo.DataOperacao,
                    Processado = false,
                    TentativasProcessamento = 0
                };

                await _auditLogService.EnfileirarLogAsync(auditLog);
            }

            _logger.LogInformation("Enfileirados {Quantidade} logs de auditoria para processamento", logsParaEnfileirar.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enfileirar logs de auditoria");
        }
    }

        /// <summary>
        /// Obtém o ID da empresa do contexto atual
        /// </summary>
        private Guid? ObterEmpresaId()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated == true)
            {
                var empresaIdClaim = httpContext.User.FindFirst("empresaId")?.Value
                                  ?? httpContext.User.FindFirst("company_id")?.Value;

                if (Guid.TryParse(empresaIdClaim, out var empresaId))
                {
                    return empresaId;
                }
            }
        }
        catch
        {
            // Em caso de erro, retorna null
        }

        return null;
    }

        /// <summary>
        /// Obtém o ID da entidade
        /// </summary>
        private string ObterEntidadeId(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry)
    {
        try
        {
            var keyValues = entry.Properties
                .Where(p => p.Metadata.IsPrimaryKey())
                .Select(p => p.CurrentValue?.ToString())
                .Where(v => !string.IsNullOrEmpty(v));

            return string.Join("|", keyValues);
        }
        catch
        {
            return "unknown";
        }
    }

        /// <summary>
        /// Obtém o tipo de operação baseado no estado da entidade
        /// </summary>
        private string ObterTipoOperacao(EntityState state)
    {
        return state switch
        {
            EntityState.Added => "INSERT",
            EntityState.Modified => "UPDATE",
            EntityState.Deleted => "DELETE",
            _ => "UNKNOWN"
        };
    }

        /// <summary>
        /// Captura os valores antigos da entidade
        /// </summary>
        private string? CapturarValoresAntigos(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry)
    {
        try
        {
            if (entry.State == EntityState.Added) return null;

            var valoresAntigos = new Dictionary<string, object?>();
            foreach (var property in entry.Properties)
            {
                if (property.IsModified || entry.State == EntityState.Deleted)
                {
                    valoresAntigos[property.Metadata.Name] = property.OriginalValue;
                }
            }

            return valoresAntigos.Any() ? JsonSerializer.Serialize(valoresAntigos) : null;
        }
        catch
        {
            return null;
        }
    }

        /// <summary>
        /// Captura os valores novos da entidade
        /// </summary>
        private string? CapturarValoresNovos(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry)
    {
        try
        {
            if (entry.State == EntityState.Deleted) return null;

            var valoresNovos = new Dictionary<string, object?>();
            foreach (var property in entry.Properties)
            {
                if (entry.State == EntityState.Added || property.IsModified)
                {
                    valoresNovos[property.Metadata.Name] = property.CurrentValue;
                }
            }

            return valoresNovos.Any() ? JsonSerializer.Serialize(valoresNovos) : null;
        }
        catch
        {
            return null;
        }
    }

        /// <summary>
        /// Captura os campos que foram modificados
        /// </summary>
        private string? CapturarCamposModificados(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry)
    {
        try
        {
            if (entry.State != EntityState.Modified) return null;

            var camposModificados = entry.Properties
                .Where(p => p.IsModified)
                .Select(p => p.Metadata.Name)
                .ToList();

            return camposModificados.Any() ? JsonSerializer.Serialize(camposModificados) : null;
        }
        catch
        {
            return null;
        }
    }

        /// <summary>
        /// Classe auxiliar para armazenar informações de auditoria temporariamente
        /// </summary>
        private class AuditLogInfo
    {
        public string NomeTabela { get; set; } = string.Empty;
        public string EntidadeId { get; set; } = string.Empty;
        public string TipoOperacao { get; set; } = string.Empty;
        public string? ValoresAntigos { get; set; }
        public string? ValoresNovos { get; set; }
        public string? CamposModificados { get; set; }
        public Guid? UsuarioId { get; set; }
        public string? NomeUsuario { get; set; }
        public Guid? EmpresaId { get; set; }
        public string? EnderecoIP { get; set; }
        public string? UserAgent { get; set; }
        public DateTimeOffset DataOperacao { get; set; }
    }
    }
}