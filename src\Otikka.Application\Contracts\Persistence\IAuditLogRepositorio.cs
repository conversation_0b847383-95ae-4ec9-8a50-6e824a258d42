using FluentResults;
using Otikka.Application.Contracts.Infrastructure;
using Otikka.Domain.Entities.Common;

namespace Otikka.Application.Contracts.Persistence;

/// <summary>
/// Interface do repositório para operações com logs de auditoria
/// </summary>
public interface IAuditLogRepositorio
{
    /// <summary>
    /// Adiciona um novo log de auditoria
    /// </summary>
    /// <param name="auditLog">Log de auditoria a ser adicionado</param>
    /// <returns>Resultado da operação</returns>
    Task<Result> AdicionarAsync(AuditLog auditLog);

    /// <summary>
    /// Atualiza um log de auditoria existente
    /// </summary>
    /// <param name="auditLog">Log de auditoria a ser atualizado</param>
    /// <returns>Resultado da operação</returns>
    Task<Result> AtualizarAsync(AuditLog auditLog);

    /// <summary>
    /// Obtém um log de auditoria por ID
    /// </summary>
    /// <param name="id">ID do log de auditoria</param>
    /// <returns>Log de auditoria encontrado ou null</returns>
    Task<AuditLog?> ObterPorIdAsync(Guid id);

    /// <summary>
    /// Obtém logs de auditoria que falharam no processamento
    /// </summary>
    /// <param name="maxTentativas">Número máximo de tentativas de processamento</param>
    /// <returns>Lista de logs falhados</returns>
    Task<List<AuditLog>> ObterLogsFalhadosAsync(int maxTentativas = 3);

    /// <summary>
    /// Remove logs antigos baseado na data limite
    /// </summary>
    /// <param name="dataLimite">Data limite para remoção</param>
    /// <returns>Número de logs removidos</returns>
    Task<int> RemoverLogsAntigosAsync(DateTimeOffset dataLimite);

    /// <summary>
    /// Obtém estatísticas dos logs de auditoria em um período
    /// </summary>
    /// <param name="dataInicio">Data de início do período</param>
    /// <param name="dataFim">Data de fim do período</param>
    /// <returns>Estatísticas dos logs</returns>
    Task<AuditLogEstatisticas> ObterEstatisticasAsync(DateTimeOffset dataInicio, DateTimeOffset dataFim);

    /// <summary>
    /// Obtém logs de auditoria com filtros
    /// </summary>
    /// <param name="nomeTabela">Nome da tabela (opcional)</param>
    /// <param name="tipoOperacao">Tipo de operação (opcional)</param>
    /// <param name="usuarioId">ID do usuário (opcional)</param>
    /// <param name="empresaId">ID da empresa (opcional)</param>
    /// <param name="dataInicio">Data de início (opcional)</param>
    /// <param name="dataFim">Data de fim (opcional)</param>
    /// <param name="pagina">Número da página</param>
    /// <param name="tamanhoPagina">Tamanho da página</param>
    /// <returns>Lista paginada de logs</returns>
    Task<(List<AuditLog> Logs, int Total)> ObterComFiltrosAsync(
        string? nomeTabela = null,
        string? tipoOperacao = null,
        Guid? usuarioId = null,
        Guid? empresaId = null,
        DateTimeOffset? dataInicio = null,
        DateTimeOffset? dataFim = null,
        int pagina = 1,
        int tamanhoPagina = 50);

    /// <summary>
    /// Obtém logs de auditoria por entidade
    /// </summary>
    /// <param name="nomeTabela">Nome da tabela</param>
    /// <param name="entidadeId">ID da entidade</param>
    /// <returns>Lista de logs da entidade</returns>
    Task<List<AuditLog>> ObterPorEntidadeAsync(string nomeTabela, string entidadeId);

    /// <summary>
    /// Verifica se existem logs não processados
    /// </summary>
    /// <returns>True se existem logs não processados</returns>
    Task<bool> ExistemLogsNaoProcessadosAsync();

    /// <summary>
    /// Obtém contagem de logs por status
    /// </summary>
    /// <returns>Dicionário com contagem por status</returns>
    Task<Dictionary<string, int>> ObterContagemPorStatusAsync();
}