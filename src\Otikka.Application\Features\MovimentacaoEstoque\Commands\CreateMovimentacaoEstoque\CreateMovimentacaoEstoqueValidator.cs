using FluentValidation;

namespace Otikka.Application.Features.MovimentacaoEstoque.Commands.CreateMovimentacaoEstoque;

public class CreateMovimentacaoEstoqueValidator : AbstractValidator<CreateMovimentacaoEstoque>
{
    public CreateMovimentacaoEstoqueValidator()
    {
        RuleSet("MovimentacaoEstoqueValidacao", () =>
        {
            RuleFor(x => x.ProdutoId)
                .NotEmpty().WithMessage("O produto é obrigatório.");

            RuleFor(x => x.TipoMovimentacao)
                .IsInEnum().WithMessage("O tipo de movimentação é obrigatório.");

            RuleFor(x => x.DataMovimentacao)
                .NotEmpty().WithMessage("A data da movimentação é obrigatória.")
                .LessThanOrEqualTo(DateTime.Now).WithMessage("A data da movimentação não pode ser futura.");

            RuleFor(x => x.Quantidade)
                .NotEqual(0).WithMessage("A quantidade não pode ser zero.");

            RuleFor(x => x.CustoUnitario)
                .GreaterThanOrEqualTo(0).WithMessage("O custo unitário deve ser maior ou igual a zero.")
                .When(x => x.CustoUnitario.HasValue);
        });
    }
}
