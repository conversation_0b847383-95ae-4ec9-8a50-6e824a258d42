using FluentValidation;
using Otikka.Application.Common.Extensions;

namespace Otikka.Application.Features.Pessoa.Commands.CreateReceita;

public class CreateReceitaValidator : AbstractValidator<CreateReceita>
{
    public CreateReceitaValidator()
    {
        // Validação do ClienteId
        RuleFor(x => x.ClienteId)
            .NotEmpty()
            .WithMessage("O ID do cliente é obrigatório");

        // Validação do EmpresaId
        RuleFor(x => x.EmpresaId)
            .NotEmpty()
            .WithMessage("O ID da empresa é obrigatório");

        // Validação do CriadoPorId
        RuleFor(x => x.CriadoPorId)
            .NotEmpty()
            .WithMessage("O ID do usuário que está criando a receita é obrigatório");

        // Validação de Receita - não pode ter todos os campos zerados
        RuleFor(x => x)
            .Must(receita => receita.HasValidMeasurements())
            .WithMessage("Não é possível salvar uma receita com todos os campos de medição zerados. " +
                       "Por favor, preencha pelo menos um valor diferente de zero.");

        // Validação da Data de Validade da Receita
        RuleFor(x => x.DataValidade)
            .GreaterThan(DateTimeOffset.Now.Date)
            .WithMessage("A data de validade da receita deve ser posterior à data atual")
            .When(x => x.DataValidade.HasValue);

        // Validação do Nome do Profissional
        RuleFor(x => x.NomeProfissional)
            .MaximumLength(200)
            .WithMessage("O nome do profissional não pode exceder 200 caracteres")
            .When(x => !string.IsNullOrEmpty(x.NomeProfissional));

        // Validações dos campos de medição - valores dentro de faixas aceitáveis
        ValidarCamposMedicao();
    }

    private void ValidarCamposMedicao()
    {
        // Validação dos campos esféricos (normalmente entre -20 e +20)
        RuleFor(x => x.EsfericoOlhoDireitoPerto)
            .InclusiveBetween(-20f, 20f)
            .WithMessage("O valor esférico do olho direito (perto) deve estar entre -20 e +20")
            .When(x => x.EsfericoOlhoDireitoPerto.HasValue);

        RuleFor(x => x.EsfericoOlhoDireitoLonge)
            .InclusiveBetween(-20f, 20f)
            .WithMessage("O valor esférico do olho direito (longe) deve estar entre -20 e +20")
            .When(x => x.EsfericoOlhoDireitoLonge.HasValue);

        RuleFor(x => x.EsfericoOlhoEsquerdoPerto)
            .InclusiveBetween(-20f, 20f)
            .WithMessage("O valor esférico do olho esquerdo (perto) deve estar entre -20 e +20")
            .When(x => x.EsfericoOlhoEsquerdoPerto.HasValue);

        RuleFor(x => x.EsfericoOlhoEsquerdoLonge)
            .InclusiveBetween(-20f, 20f)
            .WithMessage("O valor esférico do olho esquerdo (longe) deve estar entre -20 e +20")
            .When(x => x.EsfericoOlhoEsquerdoLonge.HasValue);

        // Validação dos campos cilíndricos (normalmente entre -10 e +10)
        RuleFor(x => x.CilindricoOlhoDireitoPerto)
            .InclusiveBetween(-10f, 10f)
            .WithMessage("O valor cilíndrico do olho direito (perto) deve estar entre -10 e +10")
            .When(x => x.CilindricoOlhoDireitoPerto.HasValue);

        RuleFor(x => x.CilindricoOlhoDireitoLonge)
            .InclusiveBetween(-10f, 10f)
            .WithMessage("O valor cilíndrico do olho direito (longe) deve estar entre -10 e +10")
            .When(x => x.CilindricoOlhoDireitoLonge.HasValue);

        RuleFor(x => x.CilindricoOlhoEsquerdoPerto)
            .InclusiveBetween(-10f, 10f)
            .WithMessage("O valor cilíndrico do olho esquerdo (perto) deve estar entre -10 e +10")
            .When(x => x.CilindricoOlhoEsquerdoPerto.HasValue);

        RuleFor(x => x.CilindricoOlhoEsquerdoLonge)
            .InclusiveBetween(-10f, 10f)
            .WithMessage("O valor cilíndrico do olho esquerdo (longe) deve estar entre -10 e +10")
            .When(x => x.CilindricoOlhoEsquerdoLonge.HasValue);

        // Validação dos eixos (entre 0 e 180 graus)
        RuleFor(x => x.EixoOlhoDireitoPerto)
            .InclusiveBetween(0, 180)
            .WithMessage("O eixo do olho direito (perto) deve estar entre 0 e 180 graus")
            .When(x => x.EixoOlhoDireitoPerto.HasValue);

        RuleFor(x => x.EixoOlhoDireitoLonge)
            .InclusiveBetween(0, 180)
            .WithMessage("O eixo do olho direito (longe) deve estar entre 0 e 180 graus")
            .When(x => x.EixoOlhoDireitoLonge.HasValue);

        RuleFor(x => x.EixoOlhoEsquerdoPerto)
            .InclusiveBetween(0, 180)
            .WithMessage("O eixo do olho esquerdo (perto) deve estar entre 0 e 180 graus")
            .When(x => x.EixoOlhoEsquerdoPerto.HasValue);

        RuleFor(x => x.EixoOlhoEsquerdoLonge)
            .InclusiveBetween(0, 180)
            .WithMessage("O eixo do olho esquerdo (longe) deve estar entre 0 e 180 graus")
            .When(x => x.EixoOlhoEsquerdoLonge.HasValue);

        // Validação da adição (normalmente entre 0 e +4)
        RuleFor(x => x.Adicao)
            .InclusiveBetween(0f, 4f)
            .WithMessage("O valor da adição deve estar entre 0 e +4")
            .When(x => x.Adicao.HasValue);
    }
}
