using Hangfire;
using Hangfire.PostgreSql;
using Otikka.Application;
using Otikka.Application.Models;
using Otikka.Infrastructure;
using Otikka.Persistence;
using Otikka.Workers.Configuration;
using Otikka.Workers.Exceptions;
using Otikka.Workers.Jobs;
using Serilog;

namespace Otikka.Workers;

public static class StartupExtensions
{
    public static WebApplication ConfigureServices(this WebApplicationBuilder builder)
    {
        #region Configure - Serilog
        Log.Logger = new LoggerConfiguration()
                            .WriteTo.Console()
                            .CreateBootstrapLogger();

        builder.Host.UseSerilog((context, services, configuration) =>
        {
            configuration
                .ReadFrom.Configuration(context.Configuration)
                .ReadFrom.Services(services);
        });
        #endregion

        #region Configure - Hangfire Service
        builder.Services.AddHangfire(config => config
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UsePostgreSqlStorage(options => options.UseNpgsqlConnection(builder.Configuration.GetConnectionString("HangfireConnection"))));
        builder.Services.AddHangfireServer();
        #endregion

        #region Configure - AuditLog Configuration
        builder.Services.Configure<AuditLogConfiguration>(builder.Configuration.GetSection(AuditLogConfiguration.SectionName));
        #endregion

        builder.Services.AddApplicationServices();
        builder.Services.AddInfrastructureServices(builder.Configuration);
        builder.Services.AddPersistenceServices(builder.Configuration);
        
        // Registrar jobs do Hangfire
        builder.Services.AddScoped<AuditLogJob>();

        return builder.Build();

    }
    public static WebApplication ConfigurePipeline(this WebApplication app)
    {

        ILoggerFactory loggerFactory = app.Services.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger<LogJobExceptionFilter>();
        GlobalJobFilters.Filters.Add(new LogJobExceptionFilter(logger));

        // Configure the HTTP request pipeline.
        if (!app.Environment.IsDevelopment())
        {
            app.UseExceptionHandler("/Home/Error");
            // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            app.UseHsts();
        }
        app.UseSerilogRequestLogging();
        app.UseHangfireDashboard("");
        
        // Configurar jobs recorrentes do sistema de auditoria
        HangfireJobsConfiguration.ConfigurarJobsRecorrentes();
        
        app.Run();
        return app;
    }
}