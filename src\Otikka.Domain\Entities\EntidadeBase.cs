﻿namespace Otikka.Domain.Entities
{
    public abstract class EntidadeBase : IExclusaoLogica
    {
        public Guid Id { get; set; } = Guid.NewGuid();


        #region Controle de Auditoria
        public DateTimeOffset DataCriacao { get; set; } = DateTimeOffset.UtcNow;
        public Guid? CriadoPorId { get; set; }
        public Usuario? CriadoPor { get; set; }

        public DateTimeOffset? DataAtualizacao { get; set; }
        public Guid? AtualizadoPorId { get; set; }
        public Usuario? AtualizadorPor { get; set; }
        #endregion

        #region Soft Delete (Exclusão Lógica)
        public DateTimeOffset? DataExclusao { get; set; }
        public Guid? ExcluidoPorId { get; set; }
        public Usuario? ExcluidoPor { get; set; }
        #endregion

        #region Controle de Conflito (Concorrência)
        [Timestamp]
        public uint Version { get; set; }
        #endregion
    }
}
