using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;
using FluentResults;
using Otikka.Domain.Entities.PessoaModulo;

namespace Otikka.Application.Features.Pessoa.Queries.GetReceita;

public class GetReceitaHandler(
    IReceitaRepositorio receitaRepository,
    ILogger<GetReceitaHandler> logger)
{
    public async Task<Result<Receita>> Handle(GetReceita req)
    {
        logger.LogInformation("Buscando receita: {ReceitaId} para empresa: {EmpresaId}", req.Id, req.EmpresaId);

        try
        {
            var receita = await receitaRepository.Obter(req.Id);

            if (receita == null)
            {
                logger.LogWarning("Receita não encontrada: {ReceitaId}", req.Id);
                return Result.Fail<Receita>("Receita não encontrada");
            }

            // Verificar se a receita pertence à empresa
            if (receita.EmpresaId != req.EmpresaId)
            {
                logger.LogWarning("Tentativa de acessar receita {ReceitaId} que não pertence à empresa {EmpresaId}", req.Id, req.EmpresaId);
                return Result.Fail<Receita>("A receita não pertence à empresa");
            }

            logger.LogInformation("Receita encontrada com sucesso: {ReceitaId}", req.Id);
            return Result.Ok(receita);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao buscar receita {ReceitaId}: {Message}", req.Id, ex.Message);
            return Result.Fail<Receita>($"Erro ao buscar receita: {ex.Message}");
        }
    }
}
