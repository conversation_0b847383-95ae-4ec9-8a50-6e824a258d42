@attribute [Route(Application.Routes.ReceitaCadastrar)]
@using Otikka.Application.Features.Pessoa.Commands.CreateReceita
@using Otikka.Domain.Entities.PessoaModulo
@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Receita.Componentes
@using Otikka.App.Components.Pages.Dashboard.ReceitaModulo.Componentes
@using FluentResults
@using Wolverine
@using Otikka.Application.Contracts.Infrastructure
@inherits PageBase
@inject ILogger<Cadastrar> Logger
@inject IFileStorageService FileStorageService

<PageTitle>Cadastrar Receita - Otikka</PageTitle>

<div class="row" id="receita-container">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header align-items-center d-flex">
                <h4 class="card-title mb-0 flex-grow-1">Cadastrar Receita</h4>
            </div>
            <EditForm Model="Receita" OnValidSubmit="Submit" FormName="CadastrarReceita">
                <FluentValidationValidator />
                <div class="card-body">
                    <div class="row gy-4">
                        <!-- Seleção de Cliente -->
                        <ClienteSelectorForm @bind-ClienteId="Receita.ClienteId" OnClienteSelected="OnClienteSelected" />
                        
                        @if (ClienteSelecionado != null)
                        {
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="ti ti-info-circle me-2"></i>
                                    <strong>Cliente selecionado:</strong> @ClienteSelecionado.Nome
                                    @if (!string.IsNullOrEmpty(ClienteSelecionado.Email))
                                    {
                                        <span class="text-muted"> - @ClienteSelecionado.Email</span>
                                    }
                                </div>
                            </div>
                        }
                    </div>

                    <!-- Campos da Receita -->
                    <div class="mt-4">
                        <h5 class="card-title mb-3">Dados da Receita</h5>
                        <ReceitaCamposForm Receita="Receita" OnFileSelected="OnFileSelected" />
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="@Application.Routes.ReceitaListar" class="btn btn-light">
                                    <i class="ti ti-arrow-left me-1"></i> Voltar
                                </a>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-secondary" @onclick="LimparFormulario">
                                        <i class="ti ti-refresh me-1"></i> Limpar
                                    </button>
                                    <SubmitButton IsProcessing="Processing" ButtonClass="btn-primary">
                                        <i class="ti ti-save me-1"></i> Cadastrar Receita
                                    </SubmitButton>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </EditForm>
        </div>
    </div>
</div>

@code {
    private CreateReceita Receita = new();
    private Pessoa? ClienteSelecionado;
    private IBrowserFile? selectedFile;

    protected override async Task OnInitializedAsync()
    {
        Receita.EmpresaId = await GetEmpresaIdAsync();
        Receita.CriadoPorId = await GetUsuarioIdLoggedAsync();
        Receita.Id = Guid.NewGuid();
    }

    private async Task Submit()
    {
        try
        {
            await ProcessingChange(true);

            if (Receita.ClienteId == Guid.Empty)
            {
                await AlertService.ShowAlert("Atenção", "Por favor, selecione um cliente para a receita.");
                return;
            }

            Logger.LogInformation("Iniciando cadastro de receita para cliente: {ClienteId}", Receita.ClienteId);

            // Upload da imagem se selecionada
            if (selectedFile != null)
            {
                await UploadImagemReceita();
            }

            // Definir campos de auditoria
            Receita.DataCriacao = DateTimeOffset.Now;
            Receita.DataValidade = Receita.DataValidade ?? DateTime.Now.AddYears(1);

            var result = await MessageBus.InvokeAsync<Result>(Receita);

            if (result.IsSuccess)
            {
                Logger.LogInformation("Receita cadastrada com sucesso para cliente {ClienteId} - ID: {Id}", Receita.ClienteId, Receita.Id);
                
                await AlertService.ShowAlert("Sucesso", "Receita cadastrada com sucesso!");
                NavigationManager.NavigateTo(Application.Routes.ReceitaListar);
            }
            else
            {
                var errorMessage = string.Join("; ", result.Errors.Select(e => e.Message));
                Logger.LogWarning("Falha ao cadastrar receita: {Errors}", errorMessage);
                await AlertService.ShowAlert("Erro", errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao cadastrar receita para cliente {ClienteId}: {Message}", Receita.ClienteId, ex.Message);
            await AlertService.ShowAlert("Erro", "Erro interno do sistema. Tente novamente.");
        }
        finally
        {
            await ProcessingChange(false);
        }
    }

    private async Task OnFileSelected(IBrowserFile file)
    {
        selectedFile = file;
        Logger.LogInformation("Arquivo selecionado para upload: {FileName}", file.Name);
    }

    private async Task UploadImagemReceita()
    {
        if (selectedFile == null) return;

        try
        {
            Logger.LogInformation("Iniciando upload da imagem da receita: {FileName}", selectedFile.Name);

            using var stream = selectedFile.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024); // 5MB
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);

            var fileName = $"receita-{Receita.Id}-{selectedFile.Name}";
            var contentType = selectedFile.ContentType;

            var bucketName = "otikka-receitas";
            var objectName = $"receitas/imagens/{fileName}";

            memoryStream.Position = 0; // Reset stream position
            var uploadResult = await FileStorageService.UploadFileAsync(
                bucketName,
                objectName,
                memoryStream,
                contentType);

            if (uploadResult.IsSuccess)
            {
                Receita.ImagemReceitaUrl = uploadResult.Value;
                Logger.LogInformation("Upload da imagem concluído com sucesso: {Url}", Receita.ImagemReceitaUrl);
            }
            else
            {
                Logger.LogError("Erro no upload da imagem: {Errors}", string.Join("; ", uploadResult.Errors.Select(e => e.Message)));
                await AlertService.ShowAlert("Atenção", "Erro ao fazer upload da imagem. A receita será salva sem a imagem.");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro no upload da imagem da receita");
            await AlertService.ShowAlert("Atenção", "Erro ao fazer upload da imagem. A receita será salva sem a imagem.");
        }
    }

    private void OnClienteSelected(Pessoa cliente)
    {
        ClienteSelecionado = cliente;
        Logger.LogInformation("Cliente selecionado: {ClienteId} - {Nome}", cliente.Id, cliente.Nome);
        StateHasChanged();
    }

    private async Task LimparFormulario()
    {
        var confirmacao = await AlertService.ShowConfirm(
            "Confirmar",
            "Tem certeza que deseja limpar todos os campos do formulário?",
            "Sim, Limpar",
            "Cancelar");

        if (confirmacao)
        {
            Receita = new CreateReceita
            {
                EmpresaId = await GetEmpresaIdAsync(),
                CriadoPorId = await GetUsuarioIdLoggedAsync(),
                Id = Guid.NewGuid()
            };
            ClienteSelecionado = null;
            selectedFile = null;
            StateHasChanged();
        }
    }
}
