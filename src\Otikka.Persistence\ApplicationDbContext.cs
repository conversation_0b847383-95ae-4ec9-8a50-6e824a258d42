using Microsoft.EntityFrameworkCore;
using Otikka.Application.Constants;
using Otikka.Domain.Entities.Common;
using Otikka.Domain.Entities.CompraModule;

using Otikka.Domain.Entities.NotaFiscalModule.Entrada;
using Otikka.Domain.Entities.OrdemServicoModulo;
using Otikka.Domain.Entities.PessoaModulo;
using Otikka.Domain.Entities.ProdutoModulo;
using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo;
using Otikka.Domain.Entities.TransacaoFinanceiraModulo;
using Otikka.Domain.Entities.VendaModulo;
using Otikka.Persistence.Interceptors;
using Microsoft.AspNetCore.Http;
using Otikka.Application.Contracts.Infrastructure;

namespace Otikka.Persistence
{
    public class ApplicationDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IAuditLogService _auditLogService;

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IHttpContextAccessor httpContextAccessor, IAuditLogService auditLogService) 
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
            _auditLogService = auditLogService;
        }
        #region DbSets

        public DbSet<Empresa> Empresas { get; set; }
        public DbSet<UsuarioEmpresa> UsuariosEmpresas { get; set; }
        public DbSet<OrdemServico> OrdensServicos { get; set; }
        public DbSet<OrdemServicoSituacao> OrdensServicoSituacoes { get; set; }
        public DbSet<TransacaoComercial> TransacoesComerciais { get; set; }
        public DbSet<Venda> Vendas { get; set; }
        public DbSet<ProdutoServicoVendido> ProdutosVendidos { get; set; }
        public DbSet<CategoriaTransacaoFinanceira> CategoriasTransacoesFinanceiras { get; set; }
        public DbSet<TransacaoFinanceira> TransacoesFinanceiras { get; set; }
        public DbSet<Documento> Documentos { get; set; }
        public DbSet<Pessoa> Pessoas { get; set; }
        public DbSet<Receita> Receitas { get; set; }
        public DbSet<Usuario> Usuarios { get; set; }
        public DbSet<Endereco> Enderecos { get; set; }
        public DbSet<CategoriaProduto> CategoriasProdutos { get; set; }
        public DbSet<MarcaProduto> MarcasProdutos { get; set; }
        public DbSet<UnidadeMedidaProduto> UnidadesMedidaProdutos { get; set; }
        public DbSet<Produto> Produtos { get; set; }
        public DbSet<ProdutoServicoVendido> ProdutoServicoVendidos { get; set; }
        public DbSet<FormaPagamento> FormasPagamento { get; set; }
        public DbSet<Pagamento> Pagamentos { get; set; }
        public DbSet<CepCache> CepCache { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }

        // Módulo de Estoque
        public DbSet<MovimentacaoEstoque> MovimentacoesEstoque { get; set; }

        // Módulo de Compras
        public DbSet<Compra> Compras { get; set; }
        public DbSet<ItemCompra> ItensCompra { get; set; }

        // Módulo de Nota Fiscal de Entrada
        public DbSet<NFeEntrada> NFesEntrada { get; set; }
        public DbSet<ItemNFeEntrada> ItensNFeEntrada { get; set; }


        #endregion
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);
            optionsBuilder.AddInterceptors(
                new AuditInterceptor(_httpContextAccessor, _auditLogService, null)
            );
        }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
        }
    }
}
