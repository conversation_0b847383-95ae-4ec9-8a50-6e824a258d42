@using Otikka.Application.Features.Produto.Queries.GetAllProdutos
@using Otikka.Application.Features.Produto.Queries.GetProduto
@using Otikka.Application.Contracts.Persistence
@using Otikka.Domain.Entities.ProdutoModulo
@using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo
@inherits PageBase

@if (MovimentacaoEstoque is not null)
{
    <EditForm Model="MovimentacaoEstoque" OnValidSubmit="Submit" FormName="MovimentacaoEstoqueForm">
        <FluentValidationValidator Options="@(options => options.IncludeRuleSets("MovimentacaoEstoqueValidacao"))" />

        <!-- Campo oculto para garantir que o ProdutoId seja enviado -->
        <input type="hidden" @bind="MovimentacaoEstoque!.ProdutoId" />

        <!-- Cabeçalho da Movimentação -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0">@(EhEdicao ? "Editar" : "Nova") Movimentação de Estoque</h4>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="@Application.Routes.MovimentacaoEstoqueListar">Movimentações de Estoque</a></li>
                            <li class="breadcrumb-item active">@(EhEdicao ? "Editar" : "Cadastrar")</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dados da Movimentação -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Dados da Movimentação</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="produto" class="form-label">Produto <span class="text-danger">*</span></label>
                                    <InputSelect @bind-Value="ProdutoSelecionado"
                                                 class="form-select"
                                                 id="produto">
                                        <option value="">Selecione um produto</option>
                                        @if (Produtos != null)
                                        {
                                            @foreach (var produto in Produtos)
                                            {
                                                <option value="@produto.Id">@produto.Nome</option>
                                            }
                                        }
                                    </InputSelect>
                                    <ValidationMessage For="@(() => MovimentacaoEstoque.ProdutoId)" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="tipoMovimentacao" class="form-label">Tipo de Movimentação <span class="text-danger">*</span></label>
                                    <InputSelect @bind-Value="MovimentacaoEstoque.TipoMovimentacao" class="form-select" id="tipoMovimentacao">
                                        <option value="">Selecione o tipo</option>
                                        <option value="@TipoMovimentacaoEstoque.Entrada">Entrada</option>
                                        <option value="@TipoMovimentacaoEstoque.Saida">Saída</option>
                                        <option value="@TipoMovimentacaoEstoque.Ajuste">Ajuste</option>

                                    </InputSelect>
                                    <ValidationMessage For="@(() => MovimentacaoEstoque.TipoMovimentacao)" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="dataMovimentacao" class="form-label">Data da Movimentação <span class="text-danger">*</span></label>
                                    <InputDate @bind-Value="MovimentacaoEstoque.DataMovimentacao" class="form-control" id="dataMovimentacao" />
                                    <ValidationMessage For="@(() => MovimentacaoEstoque.DataMovimentacao)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="quantidade" class="form-label">Quantidade <span class="text-danger">*</span></label>
                                    <InputNumber @bind-Value="MovimentacaoEstoque.Quantidade" class="form-control" id="quantidade" />
                                    <ValidationMessage For="@(() => MovimentacaoEstoque.Quantidade)" />
                                    <div class="form-text">Use valores negativos para saídas</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="custoUnitario" class="form-label">Custo Unitário</label>
                                    <InputNumber @bind-Value="MovimentacaoEstoque.CustoUnitario" class="form-control" id="custoUnitario" />
                                    <ValidationMessage For="@(() => MovimentacaoEstoque.CustoUnitario)" />
                                    <div class="form-text">Opcional</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="descricao" class="form-label">Descrição</label>
                                    <InputTextArea @bind-Value="MovimentacaoEstoque.Descricao" class="form-control" id="descricao" rows="3" />
                                    <ValidationMessage For="@(() => MovimentacaoEstoque.Descricao)" />
                                </div>
                            </div>
                        </div>

                        @if (ProdutoAtual != null)
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">Informações do Estoque</h6>
                                        <p class="mb-1"><strong>Estoque Atual:</strong> @ProdutoAtual.QuantidadeEstoqueCorrente unidades</p>
                                        <p class="mb-1"><strong>Estoque Mínimo:</strong> @ProdutoAtual.QuantidadeEstoqueMinimo unidades</p>
                                        <p class="mb-1"><strong>ProdutoId:</strong> @MovimentacaoEstoque?.ProdutoId</p>
                                        @if (MovimentacaoEstoque!.Quantidade != 0)
                                        {
                                            var novoEstoque = ProdutoAtual.QuantidadeEstoqueCorrente + MovimentacaoEstoque.Quantidade;
                                            <p class="mb-0">
                                                <strong>Novo Estoque:</strong>
                                                <span class="@(novoEstoque < ProdutoAtual.QuantidadeEstoqueMinimo ? "text-danger" : "text-success")">
                                                    @novoEstoque unidades
                                                </span>
                                                @if (novoEstoque < ProdutoAtual.QuantidadeEstoqueMinimo)
                                                {
                                                    <small class="text-danger">(Abaixo do mínimo!)</small>
                                                }
                                            </p>
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Botões de Ação -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="@Application.Routes.MovimentacaoEstoqueListar" class="btn btn-outline-primary">Voltar</a>

                            <SubmitButton IsProcessing="Processing" ButtonClass="btn-primary">
                                <i class="ri-save-line me-2"></i> @(EhEdicao ? "Atualizar" : "Cadastrar")
                            </SubmitButton>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </EditForm>
}

@code {
    [Parameter] public bool EhEdicao { get; set; }
    [Parameter] public Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo.MovimentacaoEstoque? MovimentacaoEstoque { get; set; }
    [Parameter] public EventCallback OnSave { get; set; }

    private List<Produto>? Produtos;
    private Produto? ProdutoAtual;
    private Guid? _produtoSelecionadoId;

    private Guid? ProdutoSelecionado
    {
        get => _produtoSelecionadoId;
        set
        {
            if (_produtoSelecionadoId != value)
            {
                _produtoSelecionadoId = value;
                // Quando o produto é alterado, o ProdutoId deve ser atualizado
                // e as informações do produto devem ser recarregadas.
                InvokeAsync(() => OnProdutoChanged(value));
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await CarregarDados();
        await ConfigurarEdicao();
    }

    protected override async Task OnParametersSetAsync()
    {
        await ConfigurarEdicao();
    }

    private async Task ConfigurarEdicao()
    {
        // Se estamos editando, carregar o produto selecionado
        if (EhEdicao && MovimentacaoEstoque?.Produto != null)
        {
            // Definir diretamente o produto selecionado sem acionar a lógica de mudança
            _produtoSelecionadoId = MovimentacaoEstoque.Produto.Id;
            ProdutoAtual = MovimentacaoEstoque.Produto;

            // Forçar atualização da UI
            StateHasChanged();
        }
        else if (EhEdicao && MovimentacaoEstoque != null && MovimentacaoEstoque.ProdutoId != Guid.Empty)
        {
            // Se o Produto não está carregado, buscar pelo ProdutoId
            try
            {
                var empresaId = await GetEmpresaIdAsync();
                var produtoResult = await MessageBus.InvokeAsync<Result<Produto>>(new GetProduto { Id = MovimentacaoEstoque.ProdutoId });
                if (produtoResult.IsSuccess && produtoResult.Value != null)
                {
                    _produtoSelecionadoId = produtoResult.Value.Id;
                    ProdutoAtual = produtoResult.Value;
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                await AlertService.ShowError("Erro!", $"Erro ao carregar dados do produto: {ex.Message}");
            }
        }
        // Se não estamos editando mas há um produto selecionado, carregar o produto
        else if (!EhEdicao && _produtoSelecionadoId.HasValue && _produtoSelecionadoId.Value != Guid.Empty)
        {
            await CarregarProduto(_produtoSelecionadoId.Value);
        }
    }

    private async Task OnProdutoChanged(Guid? produtoId)
    {
        if (produtoId.HasValue && produtoId.Value != Guid.Empty)
        {
            await CarregarProduto(produtoId.Value);
        }
        else
        {
            ProdutoAtual = null;
            if (MovimentacaoEstoque != null)
            {
                MovimentacaoEstoque.ProdutoId = Guid.Empty; // Limpa o ID se nenhum produto for selecionado
            }
        }
        StateHasChanged();
    }

    private async Task CarregarDados()
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();

            // Carregar produtos
            var produtosResult = await MessageBus.InvokeAsync<Result<List<Produto>>>(new GetAllProdutos { EmpresaId = empresaId });
            if (produtosResult.IsSuccess)
            {
                Produtos = produtosResult.Value;
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro!", $"Erro ao carregar dados: {ex.Message}");
        }
    }

    private async Task OnProdutoChanged(ChangeEventArgs e)
    {
        if (Guid.TryParse(e.Value?.ToString(), out var produtoId) && produtoId != Guid.Empty)
        {
            await CarregarProduto(produtoId);
        }
        else
        {
            ProdutoAtual = null;
            if (MovimentacaoEstoque != null)
            {
                MovimentacaoEstoque.ProdutoId = Guid.Empty;
            }
        }
        StateHasChanged(); // Força a atualização da UI
    }

    private async Task CarregarProduto(Guid produtoId)
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();

            if (MovimentacaoEstoque != null)
            {
                // Buscar o produto
                var produtoResult = await MessageBus.InvokeAsync<Result<Produto>>(new GetProduto { Id = produtoId });
                
                if (produtoResult.IsSuccess && produtoResult.Value != null)
                {
                    ProdutoAtual = produtoResult.Value;
                    MovimentacaoEstoque.ProdutoId = ProdutoAtual.Id;
                }
                else
                {
                    await AlertService.ShowError("Erro!", "Produto não encontrado.");
                }
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro!", $"Erro ao carregar produto: {ex.Message}");
        }
    }

    private async Task Submit()
    {

        if (MovimentacaoEstoque?.ProdutoId == Guid.Empty)
        {
            await AlertService.ShowError("Erro!", "Erro interno: ProdutoId não foi definido. Tente selecionar o produto novamente.");
            return;
        }

        await ProcessingChange(true);
        try
        {
            // Verificação adicional para garantir que o ProdutoId está definido
            if (MovimentacaoEstoque != null && MovimentacaoEstoque.ProdutoId == Guid.Empty && ProdutoSelecionado.HasValue)
            {
                await CarregarProduto(ProdutoSelecionado.Value);
            }

            await OnSave.InvokeAsync();
        }
        finally
        {
            await ProcessingChange(false);
        }
    }
}
