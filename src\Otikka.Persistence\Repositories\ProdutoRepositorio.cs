using Gestao.Dominio.Repositorios;
using Otikka.Application.Models;
using Otikka.Application.Models.Requests;
using Otikka.Domain.Entities.ProdutoModulo;
using Microsoft.EntityFrameworkCore;

namespace Otikka.Persistence.Repositories;

public class ProdutoRepositorio : IProdutoRepositorio
{
    private readonly IDbContextFactory<ApplicationDbContext> _factory;

    public ProdutoRepositorio(IDbContextFactory<ApplicationDbContext> factory)
    {
        _factory = factory;
    }

    public async Task<List<Produto>> ObterTudo(Guid EmpresaId, bool incluirProdutoPorEncomendado)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.Produtos.AsQueryable();

            if (!incluirProdutoPorEncomendado)
            {
                query = query.Where(a => a.Encomenda == false);
            }
            var items = await query
                .Where(a => a.EmpresaId == EmpresaId)
                .Include(a => a.Marca<PERSON>roduto)
                .Include(a => a.CategoriaProduto)
                .OrderBy(a => a.Nome)
                .ToListAsync();

            return items;
        }
    }

    public async Task<List<Produto>> ObterTudo(Guid EmpresaId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {

            var query = _db.Produtos.AsQueryable();

            var items = await query
                .Where(a => a.EmpresaId == EmpresaId)
                .Include(a => a.MarcaProduto)
                .Include(a => a.CategoriaProduto)
                .OrderBy(a => a.Nome)
                .ToListAsync();

            return items;
        }
    }

    public async Task<PaginatedList<Produto>> ObterTudo(Guid EmpresaId, ProdutoFiltroRequest filtro, PaginationParameters paginationParams)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.Produtos.AsQueryable();

            if (!string.IsNullOrWhiteSpace(filtro.PalavraBusca))
                query = query.Where(a => a.Nome.Contains(filtro.PalavraBusca));

            if (filtro.MarcaId is not null && filtro.MarcaId != Guid.Empty)
                query = query.Where(a => a.MarcaProdutoId == filtro.MarcaId);

            if (filtro.CategoriasId is not null && filtro.CategoriasId.Count > 0)
                query = query.Where(a => a.CategoriaProdutoId.HasValue && filtro.CategoriasId.Contains(a.CategoriaProdutoId.Value));

            query = query.Where(a => a.EmpresaId == EmpresaId)
                .Include(a => a.MarcaProduto)
                .Include(a => a.CategoriaProduto);

            // Aplicar ordenação
            query = ApplySort(query, paginationParams.SortColumn, paginationParams.Ascending);

            var items = await query
                .Skip((paginationParams.PageIndex - 1) * paginationParams.PageSize)
                .Take(paginationParams.PageSize)
                .ToListAsync();

            var count = await _db.Produtos
                .Where(a => a.EmpresaId == EmpresaId)
                .Where(a => string.IsNullOrWhiteSpace(filtro.PalavraBusca) || a.Nome.Contains(filtro.PalavraBusca))
                .Where(a => filtro.MarcaId == null || filtro.MarcaId == Guid.Empty || a.MarcaProdutoId == filtro.MarcaId)
                .Where(a => filtro.CategoriasId == null || filtro.CategoriasId.Count == 0 || (a.CategoriaProdutoId.HasValue && filtro.CategoriasId.Contains(a.CategoriaProdutoId.Value)))
                .CountAsync();

            int totalPages = (int)Math.Ceiling((decimal)count / paginationParams.PageSize);

            return new PaginatedList<Produto>(items, paginationParams.PageIndex, totalPages, paginationParams.SortColumn, paginationParams.Ascending);
        }
    }

    private IQueryable<Produto> ApplySort(IQueryable<Produto> query, string sortColumn, bool ascending)
    {
        if (string.IsNullOrWhiteSpace(sortColumn))
        {
            return query.OrderBy(a => a.Nome);
        }

        return sortColumn.ToLower() switch
        {
            "nome" => ascending ? query.OrderBy(a => a.Nome) : query.OrderByDescending(a => a.Nome),
            "marcaproduto.nome" => ascending
                ? query.OrderBy(a => a.MarcaProduto != null ? a.MarcaProduto.Nome : "")
                : query.OrderByDescending(a => a.MarcaProduto != null ? a.MarcaProduto.Nome : ""),
            "categoriaproduto.nome" => ascending
                ? query.OrderBy(a => a.CategoriaProduto != null ? a.CategoriaProduto.Nome : "")
                : query.OrderByDescending(a => a.CategoriaProduto != null ? a.CategoriaProduto.Nome : ""),
            "ativo" => ascending ? query.OrderBy(a => a.Ativo) : query.OrderByDescending(a => a.Ativo),
            _ => query.OrderBy(a => a.Nome)
        };
    }

    public async Task<Produto?> Obter(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            return await _db.Produtos.Include(a => a.MovimentacoesEstoque).SingleOrDefaultAsync(a => a.Id == id);
        }
    }

    public async Task<Produto?> ObterPorCodigo(string codigo)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            return await _db.Produtos.FirstOrDefaultAsync(p => p.Codigo.ToLower() == codigo.ToLower());
        }
    }

    public async Task Cadastrar(Produto entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            _db.Produtos.Add(entity);
            await _db.SaveChangesAsync();
        }
    }

    public async Task Atualizar(Produto entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            try 
            {
                // Recarrega a entidade existente do banco para obter a versão atual
                var produtoExistente = await _db.Produtos
                    .Include(p => p.MovimentacoesEstoque)
                    .FirstOrDefaultAsync(p => p.Id == entity.Id);

                if (produtoExistente == null)
                {
                    throw new InvalidOperationException($"Produto com ID {entity.Id} não foi encontrado.");
                }

                // Atualiza apenas os campos modificados, preservando a versão original
                produtoExistente.Nome = entity.Nome;
                produtoExistente.Codigo = entity.Codigo;
                produtoExistente.SKU = entity.SKU;
                produtoExistente.EAN = entity.EAN;
                produtoExistente.CategoriaProdutoId = entity.CategoriaProdutoId;
                produtoExistente.MarcaProdutoId = entity.MarcaProdutoId;
                produtoExistente.FornecedorId = entity.FornecedorId;
                produtoExistente.Encomenda = entity.Encomenda;
                produtoExistente.ControleEstoque = entity.ControleEstoque;
                produtoExistente.EstoqueTotal = entity.EstoqueTotal;
                produtoExistente.QuantidadeEstoqueCorrente = entity.QuantidadeEstoqueCorrente;
                produtoExistente.QuantidadeEstoqueMinimo = entity.QuantidadeEstoqueMinimo;
                produtoExistente.PrecoVenda = entity.PrecoVenda;
                produtoExistente.Ativo = entity.Ativo;
                produtoExistente.DataAtualizacao = DateTimeOffset.Now;
                produtoExistente.AtualizadoPorId = entity.AtualizadoPorId;

                // Durante edição de produto, não manipula movimentações de estoque
                // As movimentações são gerenciadas separadamente

                await _db.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                // Em caso de conflito de concorrência, relança a exceção para ser tratada na camada superior
                throw;
            }
        }
    }

    public async Task Excluir(Guid id)
    {
        var entity = await Obter(id);
        if (entity is not null)
        {
            await Excluir(entity);
        }
    }

    public async Task Excluir(Produto entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();

        _db.Produtos.Remove(entity);
        await _db.SaveChangesAsync();
    }
}