﻿@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Cliente.Componentes
@using Otikka.Application.Models
@using Otikka.Domain.Entities.Common
@using Otikka.Domain.Entities.PessoaModulo
@attribute [Route(Application.Routes.OrdemServicoVendasListar)]
@inherits PageBase
@inject Otikka.Application.Contracts.Persistence.ITransacaoComercialRepositorio TransacaoComercialRepositorio
@inject Gestao.Dominio.Repositorios.IPessoaRepositorio PessoaRepositorio


<PageTitle>
    Ordem de Serviço e Venda - Lista
</PageTitle>
<div class="card">
    <div class="card-header align-items-center d-flex">
        <h4 class="card-title mb-0 flex-grow-1">
            <a href="@Application.Routes.ClienteListar" class="btn btn-info btn-sm material-shadow-none">
                <i class="ri-arrow-left-circle-line align-middle me-1"></i> Voltar
            </a>
        </h4>
    </div><!-- end card header -->

    <div class="card-body">
        <div class="col-lg-12">
            @if (Cliente is not null)
            {
                <ClienteResumo Pessoa="Cliente" />
            }
        </div>
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="Digite sua pesquisa" @bind="PaginationParams.SearchWord">
                <button class="btn btn-outline-success material-shadow-none" type="button" @onclick="OnSearch">Pesquisar</button>
            </div>
        </div>
        <div class="table-responsive mt-4 table-margin-width-24">
            <table class="table table-borderless table-centered align-middle table-nowrap mb-0">
                <thead class="text-muted table-light">
                    <tr>
                        <th scope="col">OS/Venda - Código</th>
                        <th scope="col">Data de Cadastro</th>
                        <th scope="col">Data de Validade</th>
                        <th scope="col">Ação</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Paginated == null)
                    {
                        <tr>
                            <td colspan="4">Carregando...</td>
                        </tr>
                    }
                    else if (Paginated.Items.Count == 0)
                    {
                        <tr>
                            <td colspan="4">Nenhum registro!</td>
                        </tr>
                    }
                    else
                    {
                        @foreach (var item in Paginated.Items)
                        {
                            <tr>
                                <td>
                                    @if (item.Discriminator == "OrdemServico")
                                    {
                                        <span class="text-start d-block">
                                            <strong>Ordem de Serviço (Cod.):</strong>
                                            <br />
                                            <a class="fs-20 badge border border-secondary text-secondary" href="@Application.Routes.GerarRota(Application.Routes.OrdemServicoEditar, item.Id.ToString())">@item.NumeroIdentificador</a>
                                        </span>
                                    }
                                    else
                                    {
                                        <span><strong>Venda:</strong> <a href="@Application.Routes.GerarRota(Application.Routes.VendaEditar, item.Id.ToString())">@item.NumeroIdentificador</a></span>
                                    }
                                </td>
                                <td>
                                    @item.DataCriacao.ToString("dd/MM/yyyy")
                                </td>
                                <td>
                                    @(item.TransacaoFinanceira is not null && item.TransacaoFinanceira.ValorTotal.HasValue ? item.TransacaoFinanceira.ValorTotal.Value.ToString("C") : "-")
                                </td>
                                <td>
                                    <div class="btn-group">
                                        @if (item.Discriminator == "OrdemServico")
                                        {
                                            <a href="@Application.Routes.GerarRota(Application.Routes.OrdemServicoEditar, item.Id.ToString())" class="btn btn-sm btn-soft-info ms-2">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                        }
                                        else
                                        {
                                            <a href="@Application.Routes.GerarRota(Application.Routes.ReceitaDoClienteEditar, ClienteId.ToString(), item.Id.ToString())" class="btn btn-sm btn-soft-info ms-2">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
        <Pagination Paginated="@Paginated" OnPageChanged="@OnPageChanged" />
    </div>
</div>

@code {

    [Parameter] public Guid ClienteId { get; set; }
    public Pessoa? Cliente { get; set; }

    private PaginationParameters PaginationParams = new PaginationParameters(1, 20);
    private PaginatedList<TransacaoComercial>? Paginated;

    protected override void OnInitialized()
    {
        PaginationParams.PageSize = Configuration.GetValue<int>("Pagination:PageSize");
    }

    protected override async Task OnParametersSetAsync()
    {
        Paginated = await TransacaoComercialRepositorio.ObterTudo(await GetEmpresaIdAsync(), ClienteId, PaginationParams);
        var cliente = Paginated.Items.FirstOrDefault();
        if (cliente is not null)
            Cliente = cliente.Cliente;

        await LoadDataAsync();
    }

    private async Task OnSearch()
    {
        PaginationParams.PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task OnPageChanged(int pageNumber)
    {
        PaginationParams.PageIndex = pageNumber;
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        var EmpresaId = await GetEmpresaIdAsync();
        Paginated = await TransacaoComercialRepositorio.ObterTudo(EmpresaId, ClienteId, PaginationParams);
        StateHasChanged();
    }

}